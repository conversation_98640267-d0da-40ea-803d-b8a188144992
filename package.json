{"name": "ant-design-vue-jeecg", "version": "2.0.2", "private": true, "scripts": {"serve": "vue-cli-service serve --open", "build": "vue-cli-service build", "build:dev": "vue-cli-service build --mode development", "build:pro": "vue-cli-service build --mode production", "lint": "vue-cli-service lint", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e"}, "dependencies": {"@antv/data-set": "^0.10.2", "@tinymce/tinymce-vue": "^2.0.0", "ant-design-vue": "^1.7.3", "apexcharts": "^3.6.5", "axios": "^0.18.0", "clipboard": "^2.0.4", "codemirror": "^5.46.0", "crypto-js": "^4.2.0", "dayjs": "^1.8.0", "echarts": "^5.6.0", "enquire.js": "^2.1.6", "html2canvas": "^1.4.1", "js-cookie": "^2.2.0", "k-htmlpdf": "0.0.2", "vue-video-player": "^5.0.2", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "md5": "^2.2.1", "node-sass": "^4.14.1", "nprogress": "^0.2.0", "qrcodejs2": "^0.0.2", "tinymce": "^5.0.2", "viser-vue": "^2.4.4", "voice-input-button2": "^1.1.9", "vue": "^2.6.10", "vue-apexcharts": "^1.3.2", "vue-class-component": "^6.0.0", "vue-cropper": "^0.4.8", "vue-i18n": "^8.7.0", "vue-loader": "^15.7.0", "vue-ls": "^3.2.0", "vue-photo-preview": "^1.1.3", "vue-print-nb-jeecg": "^1.0.8", "vue-property-decorator": "^7.3.0", "vue-router": "^3.0.1", "vue-splitpane": "^1.0.4", "vuedraggable": "^2.24.3", "vuex": "^3.0.1", "vuex-class": "^0.3.1", "webpack": "^4.46.0"}, "devDependencies": {"@babel/polyfill": "^7.2.5", "@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-eslint": "^3.3.0", "@vue/cli-service": "^3.3.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.1", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.1.0", "less": "^3.9.0", "less-loader": "^4.1.0", "postcss-plugin-px2rem": "^0.8.1", "sass-loader": "^8.0.1", "vue-template-compiler": "^2.6.10", "webpack": "^4.46.0", "webpack-cli": "^4.10.0", "worker-loader": "^2.0.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/strongly-recommended", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"generator-star-spacing": "off", "no-mixed-operators": 0, "vue/max-attributes-per-line": [2, {"singleline": 5, "multiline": {"max": 1, "allowFirstLine": false}}], "vue/attribute-hyphenation": 0, "vue/html-self-closing": 0, "vue/component-name-in-template-casing": 0, "vue/html-closing-bracket-spacing": 0, "vue/singleline-html-element-content-newline": 0, "vue/no-unused-components": 0, "vue/multiline-html-element-content-newline": 0, "vue/no-use-v-if-with-v-for": 0, "vue/html-closing-bracket-newline": 0, "vue/no-parsing-error": 0, "no-console": 0}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}
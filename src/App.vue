<template>
  <div style="height: 100%;">
    <a-config-provider :locale="locale">
      <div id="app">
        <router-view />
      </div>
    </a-config-provider>
    <!-- 患者表单 -->
    <UserForm ref="UserForm" />
    <UserForm2 ref="UserForm2" />
  </div>
</template>
<script>
import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'
import enquireScreen from '@/utils/device'
import UserForm from './views/dashboard/UserForm'
import UserForm2 from './views/dashboard/UserForm2'
import { WebsocketMixin } from '@/mixins/WebsocketMixin'

export default {
  mixins: [WebsocketMixin],
  components: {
    UserForm,
    UserForm2
  },
  data() {
    return {
      locale: zhCN
    }
  },
  mounted() {
  },
  created() {
    let that = this
    enquireScreen(deviceType => {
      // tablet
      if (deviceType === 0) {
        that.$store.commit('TOGGLE_DEVICE', 'mobile')
        that.$store.dispatch('setSidebar', false)
      }
      // mobile
      else if (deviceType === 1) {
        that.$store.commit('TOGGLE_DEVICE', 'mobile')
        that.$store.dispatch('setSidebar', false)
      } else {
        that.$store.commit('TOGGLE_DEVICE', 'desktop')
        that.$store.dispatch('setSidebar', true)
      }
    })
  }
}
</script>
<style>
#app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}
</style>
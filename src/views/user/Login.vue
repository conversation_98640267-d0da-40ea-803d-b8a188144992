<template>
  <div class="login-main">
    <div class="login-bg"></div>

    <div class="login-top">
      <div class="top-icon"></div>
    </div>

    <div class="login-cotent">
      <img class="content-left" src="../../assets/img/img.png" />

      <div class="content-right">
        <a-form v-show="loginWay === 1" sandbox="allow-scripts allow-top-navigation allow-same-origin" :form="form" class="user-layout-login" ref="formLogin" id="formLogin">
          <a-form-item label="账号">
            <a-input
              size="large"
              v-decorator="['username',validatorRules.username,{ validator: this.handleUsernameOrEmail }]"
              type="text"
              placeholder="请输入帐户名">
            </a-input>
          </a-form-item>

          <a-form-item label="密码">
            <a-input
              v-decorator="['password',validatorRules.password]"
              size="large"
              type="password"
              autocomplete="false"
              placeholder="请输入密码">
            </a-input>
          </a-form-item>

          <!--          <a-row :gutter="0">
                      <a-col :span="14">
                        <a-form-item>
                          <a-input
                            v-decorator="['inputCode',validatorRules.inputCode]"
                            size="large"
                            type="text"
                            @change="inputCodeChange"
                            placeholder="请输入验证码">
                            <a-icon slot="prefix" v-if=" inputCodeContent==verifiedCode " type="smile" :style="{ color: 'rgba(0,0,0,.25)' }"/>
                            <a-icon slot="prefix" v-else type="frown" :style="{ color: 'rgba(0,0,0,.25)' }"/>
                          </a-input>
                        </a-form-item>
                      </a-col>
                      <a-col  :span="10">
                        <j-graphic-code @success="generateCode" style="float: right"></j-graphic-code>
                      </a-col>
                    </a-row>-->

          <a-form-item>
            <a-button
              size="large"
              type="primary"
              htmlType="submit"
              class="login-button"
              :loading="loginBtn"
              @click.stop.prevent="handleSubmit"
              :disabled="loginBtn">确定
            </a-button>
          </a-form-item>
        </a-form>

        <wxlogin v-show="loginWay === 2" :href="wxlogin.href" :state="wxlogin.state" :appid="wxlogin.appid" :scope="wxlogin.scope" :redirect_uri="wxlogin.redirect_uri"></wxlogin>

        <a-button type="link" class="link-btn" @click="handleChange">
          {{ loginWay === 1 ? '扫码登录' : '账号密码登录'}}
        </a-button>
      </div>

        <!-- <div class="user-login-other">
          <span>其他登陆方式</span>
          <a><a-icon class="item-icon" type="alipay-circle"></a-icon></a>
          <a><a-icon class="item-icon" type="taobao-circle"></a-icon></a>
          <a><a-icon class="item-icon" type="weibo-circle"></a-icon></a>
          <router-link class="register" :to="{ name: 'register' }">
            注册账户
          </router-link>
        </div>-->
    </div>
    <!-- 新增设备信息区域 -->
    <!-- <div class="device-info">
      <div>型号：ZSRZ-T</div>
      <div>序列号：ZS2025042601</div>
      <div>生产日期：2025年04月26日</div>
      <div>软件版本：V1.0</div>
    </div> -->
    <two-step-captcha
      v-if="requiredTwoStepCaptcha"
      :visible="stepCaptchaVisible"
      @success="stepCaptchaSuccess"
      @cancel="stepCaptchaCancel"></two-step-captcha>

    <a-modal
      title="登录部门选择"
      :width="450"
      :visible="departVisible"
      :closable="false"
      :maskClosable="false">

      <template slot="footer">
        <a-button type="primary" @click="departOk">确认</a-button>
      </template>

      <a-form>
        <a-form-item
          :labelCol="{span:4}"
          :wrapperCol="{span:20}"
          style="margin-bottom:10px"
          :validate-status="validate_status">
          <a-tooltip placement="topLeft">
            <template slot="title">
              <span>您隶属于多部门，请选择登录部门</span>
            </template>
            <a-avatar style="backgroundColor:#87d068" icon="gold"/>
          </a-tooltip>
          <a-select @change="departChange" :class="{'valid-error':validate_status=='error'}" placeholder="请选择登录部门"
                    style="margin-left:10px;width: 80%">
            <a-icon slot="suffixIcon" type="gold"/>
            <a-select-option
              v-for="d in departList"
              :key="d.id"
              :value="d.orgCode">
              {{ d.departName }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>


    </a-modal>

  </div>
</template>

<script>
  //import md5 from "md5"
  import TwoStepCaptcha from '@/components/tools/TwoStepCaptcha'
  import { mapActions } from 'vuex'
  import { timeFix } from '@/utils/util'
  import Vue from 'vue'
  import { ACCESS_TOKEN, ENCRYPTED_STRING } from '@/store/mutation-types'
  import JGraphicCode from '@/components/jeecg/JGraphicCode'
  import { postAction, putAction } from '@/api/manage'
  import { encryption, getEncryptedString } from '@/utils/encryption/aesEncrypt'
  import wxlogin from '@/components/wxLogin/vueWxlogin'

  export default {
    components: {
      TwoStepCaptcha,
      JGraphicCode,
      wxlogin
    },
    data() {
      return {
        loginWay: 1, // 1 - 密码登录 2 - 扫码登录
        loginBtn: false,
        // login type: 0 email, 1 username, 2 telephone
        loginType: 0,
        requiredTwoStepCaptcha: false,
        stepCaptchaVisible: false,
        form: this.$form.createForm(this),
        encryptedString: {
          key: '',
          iv: ''
        },
        state: {
          time: 60,
          smsSendBtn: false
        },
        formLogin: {
          username: '',
          password: '',
          captcha: '',
          mobile: '',
          rememberMe: true
        },
        validatorRules: {
          username: { rules: [{ required: true, message: '请输入用户名!', validator: 'click' }] },
          password: { rules: [{ required: true, message: '请输入密码!', validator: 'click' }] },
          mobile: { rules: [{ validator: this.validateMobile }] },
          captcha: { rule: [{ required: true, message: '请输入验证码!' }] },
          inputCode: { rules: [{ required: false, message: '请输入验证码!' }, { validator: this.validateInputCode }] }
        },
        verifiedCode: '',
        inputCodeContent: '',
        inputCodeNull: true,
        departList: [],
        departVisible: false,
        departSelected: '',
        currentUsername: '',
        validate_status: '',
        wxlogin: {
          appid: 'wxbb016da10c719979',
          scope: 'snsapi_login',
          redirect_uri: 'https%3a%2f%2fzcpm.zhisongkeji.com%2fdashboard%2fanalysis',
          state: '123',
          theme: 'black',
          href: 'data:text/css;base64,LmltcG93ZXJCb3ggLnFyY29kZSB7d2lkdGg6IDIwMHB4O30KLmltcG93ZXJCb3ggLnRpdGxlIHtkaXNwbGF5OiBub25lO30KLmltcG93ZXJCb3ggLmluZm8ge3dpZHRoOiAyMTBweDt9Ci5pbXBvd2VyQm94IC5pY29uMzhfbXNnLnN1Y2Mge2Rpc3BsYXk6IG5vbmV9Ci5pbXBvd2VyQm94IC5pY29uMzhfbXNnLndhcm4ge2Rpc3BsYXk6IG5vbmV9Ci5pbXBvd2VyQm94IC5zdGF0dXMge3RleHQtYWxpZ246IGNlbnRlcjt9Cg=='
        }
      }
    },
    created() {
      Vue.ls.remove(ACCESS_TOKEN)
      this.getRouterData()
      this.getEncrypte()
      // update-begin- --- author:scott ------ date:20190225 ---- for:暂时注释，未实现登录验证码功能
//      this.$http.get('/auth/2step-code')
//        .then(res => {
//          this.requiredTwoStepCaptcha = res.result.stepCode
//        }).catch(err => {
//          console.log('2step-code:', err)
//        })
      // update-end- --- author:scott ------ date:20190225 ---- for:暂时注释，未实现登录验证码功能
      // this.requiredTwoStepCaptcha = true

    },
    methods: {
      ...mapActions(['Login', 'Logout', 'PhoneLogin']),
      // handler
      handleUsernameOrEmail(rule, value, callback) {
        const regex = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/
        if (regex.test(value)) {
          this.loginType = 0
        } else {
          this.loginType = 1
        }
        callback()
      },
      handleChange() {
        if (this.loginWay === 1) {
          this.loginWay = 2
        } else {
          this.loginWay = 1
        }
      },
      handleSubmit() {
        let that = this
        let loginParams = {
          remember_me: that.formLogin.rememberMe
        }
        that.loginBtn = true
        // 使用账户密码登陆
        if (that.loginWay === 1) {
          that.form.validateFields(['username', 'password', 'inputCode'], { force: true }, (err, values) => {
            if (!err) {
              loginParams.username = values.username
              //loginParams.password = md5(values.password)
              loginParams.password = encryption(values.password, that.encryptedString.key, that.encryptedString.iv).replace(/\+/g, '%2B')
              that.Login(loginParams).then((res) => {
                this.departConfirm(res)
              }).catch((err) => {
                that.requestFailed(err)
              })
            } else {
              that.loginBtn = false
            }
          })
          // 使用手机号登陆
        } else {
          that.form.validateFields(['mobile', 'captcha'], { force: true }, (err, values) => {
            if (!err) {
              loginParams.mobile = values.mobile
              loginParams.captcha = values.captcha
              that.PhoneLogin(loginParams).then((res) => {
                // console.log(res.result)
                this.departConfirm(res)
              }).catch((err) => {
                that.requestFailed(err)
              })

            }
          })
        }
      },
      getCaptcha(e) {
        e.preventDefault()
        let that = this
        this.form.validateFields(['mobile'], { force: true }, (err, values) => {
            if (!values.mobile) {
              that.cmsFailed('请输入手机号')
            } else if (!err) {
              this.state.smsSendBtn = true
              let interval = window.setInterval(() => {
                if (that.state.time-- <= 0) {
                  that.state.time = 60
                  that.state.smsSendBtn = false
                  window.clearInterval(interval)
                }
              }, 1000)
              const hide = this.$message.loading('验证码发送中..', 0)
              let smsParams = {}
              smsParams.mobile = values.mobile
              smsParams.smsmode = '0'
              postAction('/sys/sms', smsParams)
                .then(res => {
                  if (!res.success) {
                    setTimeout(hide, 0)
                    this.cmsFailed(res.message)
                  }
                  console.log(res)
                  setTimeout(hide, 500)
                })
                .catch(err => {
                  setTimeout(hide, 1)
                  clearInterval(interval)
                  that.state.time = 60
                  that.state.smsSendBtn = false
                  this.requestFailed(err)
                })
            }
          }
        )
      },
      stepCaptchaSuccess() {
        this.loginSuccess()
      },
      stepCaptchaCancel() {
        this.Logout().then(() => {
          this.loginBtn = false
          this.stepCaptchaVisible = false
        })
      },
      loginSuccess() {
        this.loginBtn = false
        this.$router.push({ name: 'dashboard' })
        this.$notification.success({
          message: '欢迎',
          description: `${timeFix()}，欢迎回来`
        })
      },
      cmsFailed(err) {
        this.$notification['error']({
          message: '登录失败',
          description: err,
          duration: 4
        })
      },
      requestFailed(err) {
        this.$notification['error']({
          message: '登录失败',
          description: ((err.response || {}).data || {}).message || err.message || '请求出现错误，请稍后再试',
          duration: 4
        })
        this.loginBtn = false
      },
      validateMobile(rule, value, callback) {
        if (!value || new RegExp(/^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\d{8}$/).test(value)) {
          callback()
        } else {
          callback('您的手机号码格式不正确!')
        }

      },
      validateInputCode(rule, value, callback) {
        if (!value || this.verifiedCode == this.inputCodeContent) {
          callback()
        } else {
          callback('您输入的验证码不正确!')
        }
      },
      generateCode(value) {
        this.verifiedCode = value.toLowerCase()
      },
      inputCodeChange(e) {
        this.inputCodeContent = e.target.value
        if (!e.target.value || 0 == e.target.value) {
          this.inputCodeNull = true
        } else {
          this.inputCodeContent = this.inputCodeContent.toLowerCase()
          this.inputCodeNull = false
        }
      },
      departConfirm(res) {
        if (res.success) {
          let multi_depart = res.result.multi_depart
          //0:无部门 1:一个部门 2:多个部门
          if (multi_depart == 0) {
            this.loginSuccess()
            this.$notification.warn({
              message: '提示',
              description: `您尚未归属部门,请确认账号信息`,
              duration: 3
            })
          } else if (multi_depart == 2) {
            this.departVisible = true
            this.currentUsername = this.form.getFieldValue('username')
            this.departList = res.result.departs
          } else {
            this.loginSuccess()
          }
        } else {
          this.requestFailed(res)
          this.Logout()
        }
      },
      departOk() {
        if (!this.departSelected) {
          this.validate_status = 'error'
          return false
        }
        let obj = {
          orgCode: this.departSelected,
          username: this.form.getFieldValue('username')
        }
        putAction('/sys/selectDepart', obj).then(res => {
          if (res.success) {
            this.departClear()
            this.loginSuccess()
          } else {
            this.requestFailed(res)
            this.Logout().then(() => {
              this.departClear()
            })
          }
        })
      },
      departClear() {
        this.departList = []
        this.departSelected = ''
        this.currentUsername = ''
        this.departVisible = false
        this.validate_status = ''
      },
      departChange(value) {
        this.validate_status = 'success'
        this.departSelected = value
      },
      getRouterData() {
        this.$nextTick(() => {
          this.form.setFieldsValue({
            'username': this.$route.params.username
          })
        })
      },
      //获取密码加密规则
      getEncrypte() {
        var encryptedString = Vue.ls.get(ENCRYPTED_STRING)
        if (encryptedString == null) {
          getEncryptedString().then((data) => {
            this.encryptedString = data
          })
        } else {
          this.encryptedString = encryptedString
        }
      }
    }
  }
</script>

<style lang="scss">
.login-main {
  width: 100%;
  height: 100%;
  padding: 62px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative; /* 确保子元素定位正常 */
  z-index: 1;

  .login-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("../../assets/img/bg.png");
  }

  .login-top {
    position: relative;
    width: 540px;
    display: flex;
    align-items: center;

    .top-icon {
      width: 100px;
      height: 100px;
    }
  }

  .login-cotent {
    position: relative;
    width: 1051px;
    height: 606px;
    display: flex;
    justify-content: space-between;
    margin-top: 75px;
    padding: 0 58px;
    background: #fff;
    border-radius: 10px;
    
    .content-left {
      width: 382px;
      margin: 105px 0 119px 0;
    }

    .content-right {
      display: flex;
      flex-direction: column;
      width: 485px;
      padding: 65px 0;
    }

    .user-layout-login {
      padding-top: 35px;
      height: 426px;

      .ant-form-item-label {
        height: 57px;

        label {
          padding-bottom: 15px;
          font-size: 24px;
          line-height: 33px;

          &::after {
            content: '';
          }
        }
      }

      .ant-input {
        height: 60px;
      }

      .getCaptcha {
        display: block;
        width: 100%;
        height: 40px;
      }

      .forge-password {
        font-size: 14px;
      }

      .user-login-other {
        text-align: left;
        margin-top: 24px;
        line-height: 22px;

        .item-icon {
          font-size: 24px;
          color: rgba(0, 0, 0, .2);
          margin-left: 16px;
          vertical-align: middle;
          cursor: pointer;
          transition: color .3s;

          &:hover {
            color: #1890ff;
          }
        }

        .register {
          float: right;
        }
      }

      .login-button {
        height: 60px;
        width: 100%;
        margin-top: 20px;
        background: #0066FF;
        border-radius: 6px;
        font-size: 24px;
        line-height: 60px;
      }
    }

    .link-btn {
      padding-top: 25px;
      font-size: 20px;
      line-height: 20px;
    }
  }
}
/* 新增设备信息样式 */
.device-info {
  position: fixed;
  right: 20px; /* 距离右侧20px */
  bottom: 20px; /* 距离底部20px */
  padding: 12px 18px;
  background: transparent; /* 完全透明背景 */
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.6;
  z-index: 999; /* 防止被其他元素遮挡 */

  div {
    color: #333; /* 保持文字颜色 */
    margin-bottom: 4px;

    /* 序列号加粗 */
    &:nth-child(2) {
      font-weight: 500;
    }
  }
}
</style>
<style>
  .valid-error .ant-select-selection__placeholder {
    color: #f5222d;
  }
</style>
<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="充值单号">
              <a-input placeholder="请输入充值单号" v-model="queryParam.sn"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="用户名称">
              <a-input placeholder="请输入用户名称" v-model="queryParam.userName"></a-input>
            </a-form-item>
          </a-col>
          <!--          <template v-if="toggleSearchStatus">
                      <a-col :md="6" :sm="8">
                        <a-form-item label="充值本金金额">
                          <a-input placeholder="请输入充值本金金额" v-model="queryParam.rechargeAmount"></a-input>
                        </a-form-item>
                      </a-col>
                    </template>-->
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button type="primary" icon="download" @click="handleExportXls('用户充值卡充值记录')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl"
                @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete"/>
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down"/>
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">
      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <psRechargeRecord-modal ref="modalForm" @ok="modalFormOk"></psRechargeRecord-modal>
  </a-card>
</template>

<script>
  import PsRechargeRecordModal from './modules/PsRechargeRecordModal'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'

  export default {
    name: 'PsRechargeRecordList',
    mixins: [JeecgListMixin],
    components: {
      PsRechargeRecordModal
    },
    data() {
      return {
        description: '用户充值卡充值记录管理页面',
        // 表头
        columns: [
          {
            title: '充值单号',
            align: 'center',
            dataIndex: 'sn'
          },
          {
            title: '用户名称',
            align: 'center',
            dataIndex: 'userName'
          },
          {
            title: '充值金额(元)',
            align: 'center',
            dataIndex: 'rechargeAmount'
          },
          {
            title: '支付方式',
            align: 'center',
            dataIndex: 'payMethod_dictText'
          },
          {
            title: '支付状态',
            align: 'center',
            dataIndex: 'paymentState_dictText'
          },
          {
            title: '充值前余额',
            align: 'center',
            dataIndex: 'beforeRechargeRemaining'
          },
          {
            title: '充值后余额',
            align: 'center',
            dataIndex: 'afterRechargeRemaining'
          },
          {
            title: '创建时间',
            align: 'center',
            dataIndex: 'createTime'
          },
          {
            title: '微信支付单号',
            align: 'center',
            dataIndex: 'apiPayTradeno'
          },
          {
            title: '支付时间',
            align: 'center',
            dataIndex: 'paymentTime'
          }
        ],
        url: {
          list: '/bill/psRechargeRecord/list',
          delete: '/bill/psRechargeRecord/delete',
          deleteBatch: '/bill/psRechargeRecord/deleteBatch',
          exportXlsUrl: 'bill/psRechargeRecord/exportXls',
          importExcelUrl: 'bill/psRechargeRecord/importExcel'
        }
      }
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {}
  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>
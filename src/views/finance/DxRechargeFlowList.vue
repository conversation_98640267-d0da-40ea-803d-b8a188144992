<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">

          <a-col :md="6" :sm="8">
            <a-form-item label="交易流水号">
              <a-input placeholder="请输入交易流水号" v-model="queryParam.serialNumber"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="余额变动人">
              <a-input placeholder="请输入余额变动人名称" v-model="queryParam.userName"></a-input>
            </a-form-item>
          </a-col>
          <!--        <template v-if="toggleSearchStatus">
                  <a-col :md="6" :sm="8">
                      <a-form-item label="相关单据编码">
                        <a-input placeholder="请输入相关单据编码" v-model="queryParam.billNumber"></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :md="6" :sm="8">
                      <a-form-item label="患者id">
                        <a-input placeholder="请输入患者id" v-model="queryParam.doctorId"></a-input>
                      </a-form-item>
                    </a-col>
                    <a-col :md="6" :sm="8">
                      <a-form-item label="患者名称">
                        <a-input placeholder="请输入患者名称" v-model="queryParam.doctorName"></a-input>
                      </a-form-item>
                    </a-col>
                    </template>-->
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <!--              <a @click="handleToggleSearch" style="margin-left: 8px">
                              {{ toggleSearchStatus ? '收起' : '展开' }}
                              <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
                            </a>-->
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <!--    <div class="table-operator">
          <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
          <a-button type="primary" icon="download" @click="handleExportXls('余额变动流水')">导出</a-button>
          <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
            <a-button type="primary" icon="import">导入</a-button>
          </a-upload>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
            </a-menu>
            <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
          </a-dropdown>
        </div>-->

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">
      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <dxRechargeFlow-modal ref="modalForm" @ok="modalFormOk"></dxRechargeFlow-modal>
  </a-card>
</template>

<script>
  import DxRechargeFlowModal from './modules/DxRechargeFlowModal'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'

  export default {
    name: 'DxRechargeFlowList',
    mixins: [JeecgListMixin],
    components: {
      DxRechargeFlowModal
    },
    data() {
      return {
        description: '余额变动流水管理页面',
        // 表头
        columns: [
          {
            title: '交易流水号',
            align: 'center',
            dataIndex: 'trxNo'
          },
          {
            title: '相关单据编码',
            align: 'center',
            dataIndex: 'billNumber'
          },
          {
            title: '余额变动人',
            align: 'center',
            dataIndex: 'userName'
          },
          {
            title: '操作人',
            align: 'center',
            dataIndex: 'operatorName'
          },
          {
            title: '入账类型',
            align: 'center',
            dataIndex: 'fundType_dictText'
          },
          {
            title: '业务类型',
            align: 'center',
            dataIndex: 'transactionType_dictText'
          },
          {
            title: '收支金额（元）',
            align: 'center',
            dataIndex: 'rechargeAmount'
          },
          {
            title: '变更前余额(元)',
            align: 'center',
            dataIndex: 'beforeRechargeRemaining'
          },
          {
            title: '变更后余额(元)',
            align: 'center',
            dataIndex: 'afterRechargeRemaining'
          },
          {
            title: '操作人类别',
            align: 'center',
            dataIndex: 'operatorType_dictText'
          },
          {
            title: '交易时间',
            align: 'center',
            dataIndex: 'createTime'
          },
          {
            title: '备注',
            align: 'center',
            dataIndex: 'description'
          },
/*          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: { customRender: 'action' }
          }*/
        ],
        url: {
          list: '/finance/dxRechargeFlow/list',
          delete: '/finance/dxRechargeFlow/delete',
          deleteBatch: '/finance/dxRechargeFlow/deleteBatch',
          exportXlsUrl: 'finance/dxRechargeFlow/exportXls',
          importExcelUrl: 'finance/dxRechargeFlow/importExcel'
        }
      }
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {}
  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>
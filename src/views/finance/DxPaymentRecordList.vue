<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="支付状态">
              <a-select v-model="queryParam.status" placeholder="请选择支付状态查询">
                <a-select-option value="0">等待支付</a-select-option>
                <a-select-option value="1">支付成功</a-select-option>
                <a-select-option value="2">支付失败</a-select-option>
                <a-select-option value="3">已取消</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="商户订单号">
              <a-input placeholder="请输入商户订单号" v-model="queryParam.outTradeNo"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :md="6" :sm="8">
              <a-form-item label="支付流水号">
                <a-input placeholder="请输入支付流水号" v-model="queryParam.trxNo"></a-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button type="primary" icon="download" @click="handleExportXls('支付记录')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl"
                @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete"/>
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down"/>
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">
      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <dxPaymentRecord-modal ref="modalForm" @ok="modalFormOk"></dxPaymentRecord-modal>
  </a-card>
</template>

<script>
  import DxPaymentRecordModal from './modules/DxPaymentRecordModal'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'

  export default {
    name: 'DxPaymentRecordList',
    mixins: [JeecgListMixin],
    components: {
      DxPaymentRecordModal
    },
    data() {
      return {
        description: '支付记录管理页面',
        // 表头
        columns: [
          {
            title: '商品名称',
            align: 'center',
            dataIndex: 'productName'
          },
          {
            title: '商户订单号',
            align: 'center',
            dataIndex: 'outTradeNo'
          },
          {
            title: '支付流水号',
            align: 'center',
            dataIndex: 'trxNo'
          },
          {
            title: '付款人名称',
            align: 'center',
            dataIndex: 'payerName'
          },
          {
            title: '付款方支付金额',
            align: 'center',
            dataIndex: 'payerPayAmount'
          },
          {
            title: '订单金额',
            align: 'center',
            dataIndex: 'totalFee'
          },
          {
            title: '支付方式',
            align: 'center',
            dataIndex: 'paymentType'
          },
          {
            title: '支付状态',
            align: 'center',
            dataIndex: 'status_dictText'
          },
          {
            title: '支付成功时间',
            align: 'center',
            dataIndex: 'paySuccessTime'
          },
          {
            title: '完成时间',
            align: 'center',
            dataIndex: 'completeTime'
          },
          // {
          //   title: '是否退款',
          //   align: 'center',
          //   dataIndex: 'isRefund_dictText'
          // },
          // {
          //   title: '退款次数',
          //   align: 'center',
          //   dataIndex: 'refundTimes'
          // },
          // {
          //   title: '成功退款总金额',
          //   align: 'center',
          //   dataIndex: 'successRefundAmount'
          // },
          {
            title: '订单来源',
            align: 'center',
            dataIndex: 'orderFrom'
          },
          {
            title: '货币类型',
            align: 'center',
            dataIndex: 'feeType'
          },
          {
            title: '银行类型，采用字符串类型的银行标识',
            align: 'center',
            dataIndex: 'bankType'
          }
        ],
        url: {
          list: '/bill/dxPaymentRecord/list',
          delete: '/bill/dxPaymentRecord/delete',
          deleteBatch: '/bill/dxPaymentRecord/deleteBatch',
          exportXlsUrl: 'bill/dxPaymentRecord/exportXls',
          importExcelUrl: 'bill/dxPaymentRecord/importExcel'
        }
      }
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {}
  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>
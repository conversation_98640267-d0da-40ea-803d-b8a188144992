<template>
  <a-modal
    :title='title'
    :width='800'
    :visible='visible'
    :confirmLoading='confirmLoading'
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <a-form :form='form'>
        <a-row class="form-row" :gutter="24">
          <a-form-item
            :labelCol='labelCol'
            :wrapperCol='wrapperCol'
            label='医生'>
            <a-input placeholder='请输入医生姓名' disabled :defaultValue='nickname()' v-model='model.doctorName' />
          </a-form-item>
          <a-form-item
            :labelCol='labelCol'
            :wrapperCol='wrapperCol'
            label='名称'>
            <a-input placeholder='请输入量表套餐名称'
                    v-decorator="['name',{rules: [{ required: true, message: '请输入套餐名称' }]}]" />
          </a-form-item>
          <a-form-item v-show='!isAdd' label='量表' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='measureIds'>
            <a-tree-select
              showSearch
              multiple
              style='width:100%'
              :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
              :treeData='measureTree'
              v-model='selectedMeasure'
              treeNodeFilterProp='title'
              placeholder='请选择量表'>
            </a-tree-select>
          </a-form-item>
          <a-form-item
            v-show='isAdd'
            :labelCol='labelCol'
            :wrapperCol='wrapperCol'
            label='授权状态'>
            <j-dict-select-tag v-model='model.status' placeholder='请选择授权状态' dictCode='auth_status' />
          </a-form-item>
          <a-col :lg="24">
            <a-form-item
              :labelCol='labelCol'
              :wrapperCol='wrapperCol'
              label='编号生成规则'>
              <j-dict-select-tag v-model='model.noGenerRule' placeholder='请选择编号生成规则' dictCode='noGenerRule' />
            </a-form-item>
          </a-col>
          <a-col :lg="12">
            <a-form-item
              :labelCol='labelCol1'
              :wrapperCol='wrapperCol1'
              label='是否可以查看报告'>
              <a-switch checkedChildren='是' unCheckedChildren='否' v-model='resultStatus' />
            </a-form-item>
          </a-col>
          <a-col :lg="12">
            <a-form-item
              :labelCol='labelCol1'
              :wrapperCol='wrapperCol1'
              label='是否可以多次扫码'>
              <a-switch checkedChildren='是' unCheckedChildren='否' v-model='repetitionStatus' />
            </a-form-item>
          </a-col>
          <a-col :lg="12">
            <a-form-item
              :labelCol='labelCol1'
              :wrapperCol='wrapperCol1'
              label='是否需要测试者签名'>
              <a-switch checkedChildren='是' unCheckedChildren='否' v-model='signUserStatus' />
            </a-form-item>
          </a-col>
          <a-col :lg="12">
            <a-form-item
              :labelCol='labelCol1'
              :wrapperCol='wrapperCol1'
              label='是否显示量表标题'>
              <a-switch checkedChildren='是' unCheckedChildren='否' v-model='isShowTitle' />
            </a-form-item>
          </a-col>
          <a-col :lg="12">
            <a-form-item
              :labelCol='labelCol1'
              :wrapperCol='wrapperCol1'
              label='是否依从患者档案'>
              <a-switch checkedChildren='是' unCheckedChildren='否' v-model='patientGroupStatus' />
            </a-form-item>
          </a-col>
          <a-col :lg="24">
            <a-form-item
              :labelCol='labelCol'
              :wrapperCol='wrapperCol'
              label='限制扫码次数'>
              <a-input-number :min='0' :defaultValue='0' placeholder='请输入限制扫码次数(0为不限制)'
                              v-model='model.limitScanNum'
                              style='width: 60%' />
            </a-form-item>
          </a-col>
          <a-col :lg="24">
            <a-form-item
              :labelCol='labelCol'
              :wrapperCol='wrapperCol'
              label='失效时间'>
              <a-date-picker showTime format='YYYY-MM-DD HH:mm' placeholder='请输入失效时间(不选择即为永久)'
                            :show-time="{ format: 'HH:mm' }"
                            v-model='model.limitTime' style='width: 60%' />
            </a-form-item>
          </a-col>
          <a-col :lg="24">
            <a-form-item
              :labelCol='labelCol'
              :wrapperCol='wrapperCol'
              label='编码前缀'>
              <a-input placeholder='请输入编码前缀' v-model='model.prefixEncode' />
            </a-form-item>
          </a-col>
          <a-col :lg="24">
            <a-form-item
              :labelCol='labelCol'
              :wrapperCol='wrapperCol'
              label='答题验证码'>
              <a-input placeholder='请输入答题验证码' v-model='model.password' />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'
import { mapGetters } from 'vuex'
import { globalShowedAuth } from '@/utils/authFilter'
import pick from 'lodash.pick'

export default {
  name: 'WxAuthorizationRecordsModal',
  data() {
    return {
      title: '操作',
      visible: false,
      selectedMeasure: [],
      measureTree: [],
      model: {
        status: '0',
        noGenerRule: '0'
      },
      isAdd: true,
      doctorId: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      labelCol1: {
        xs: { span: 24 },
        sm: { span: 10 }
      },
      wrapperCol1: {
        xs: { span: 24 },
        sm: { span: 12 }
      },
      resultStatus: false,
      repetitionStatus: false,
      signUserStatus: false,
      isShowTitle: false,
      patientGroupStatus: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {},
      url: {
        add: '/wx/wxAuthorizationRecords/add',
        edit: '/wx/wxAuthorizationRecords/edit',
        treeList: '/psychology/psCategory/queryTreeSelectList'
      }
    }
  },
  created() {
  },
  methods: {
    ...mapGetters(['nickname', 'userInfo']),
    add() {
      this.isAdd = false
      this.doctorId = this.userInfo().id
      this.edit({
        isShowTitle: 1,
      })
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.resultStatus = this.model.resultStatus == 1
      this.repetitionStatus = this.model.repetitionStatus == 1
      this.signUserStatus = this.model.signUserStatus == 1
      this.isShowTitle = this.model.isShowTitle == 1
      this.patientGroupStatus = this.model.patientGroupStatus == 1
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'name'))
      })
      this.visible = true
      this.loadTreeData()
    },
    close() {
      this.$emit('close')
      this.isAdd = true
      this.model = {}
      this.selectedMeasure = []
      this.measureTree = []
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          let selectedMeasure = this.selectedMeasure.map(select => select.split(',')[0])
          if (selectedMeasure.length == 0 && !this.isAdd) {
            this.$message.error('请选择套餐量表')
            that.confirmLoading = false
            return
          }
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            formData.doctorId = this.doctorId
            formData.measureIds = selectedMeasure.length > 0 ? selectedMeasure.join(',') : ''
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          formData.resultStatus = that.resultStatus ? 1 : 0
          formData.repetitionStatus = that.repetitionStatus ? 1 : 0
          formData.signUserStatus = that.signUserStatus ? 1 : 0
          formData.isShowTitle = that.isShowTitle ? 1: 0
          formData.patientGroupStatus = that.patientGroupStatus ? 1 : 0
          formData.isTop = 0
          //时间格式化
          formData.limitTime = formData.limitTime ? typeof formData.limitTime == 'object' ? formData.limitTime.format('YYYY-MM-DD HH:mm') : formData.limitTime : null
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
            that.close()
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    loadTreeData() {
      getAction(this.url.treeList, { 'isMobile': '1' }).then((res) => {
        if (res.success) {
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            this.measureTree.push(temp)
          }
          this.loading = false
        }
      })
    }
  }
}
</script>

<style lang='less' scoped>

</style>
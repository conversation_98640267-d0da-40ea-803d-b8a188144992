<template>
  <a-card :bordered='false'>

    <!-- 查询区域 -->
    <div class='table-page-search-wrapper'>
      <a-form layout='inline'>
        <a-row :gutter='24'>
          <a-col :md='6' :sm='8'>
            <a-form-item label='医生姓名'>
              <a-input placeholder='请输入医生姓名' v-model='queryParam.doctorName'></a-input>
            </a-form-item>
          </a-col>
          <a-col :md='6' :sm='8'>
            <a-form-item label='套餐名称'>
              <a-input v-model='queryParam.name' placeholder='请输入量表套餐名称'></a-input>
            </a-form-item>
          </a-col>
          <a-col :md='6' :sm='8'>
            <a-form-item label='授权状态'>
              <j-dict-select-tag v-model='queryParam.status' placeholder='请选择授权状态' dictCode='auth_status' />
            </a-form-item>
          </a-col>
          <a-col :md='6' :sm='8'>
            <span style='float: left;overflow: hidden;' class='table-page-search-submitButtons'>
              <a-button type='primary' @click='searchQuery' icon='search'>查询</a-button>
              <a-button type='primary' @click='searchReset' icon='reload' style='margin-left: 8px'>重置</a-button>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>
    <!-- 操作按钮区域 -->
    <div class='table-operator'>
      <a-button @click='handleAdd' type='primary' icon='plus'>新增</a-button>
      <a-dropdown v-if='selectedRowKeys.length > 0'>
        <a-menu slot='overlay'>
          <a-menu-item key='1' @click='batchDel'>
            <a-icon type='delete' />
            删除
          </a-menu-item>
        </a-menu>
        <a-button style='margin-left: 8px'> 批量操作
          <a-icon type='down' />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class='ant-alert ant-alert-info' style='margin-bottom: 16px;'>
        <i class='anticon anticon-info-circle ant-alert-icon'></i> 已选择 <a style='font-weight: 600'>{{
          selectedRowKeys.length }}</a>项
        <a style='margin-left: 24px' @click='onClearSelected'>清空</a>
      </div>

      <a-table
        ref='table'
        size='middle'
        bordered
        rowKey='id'
        :columns='columns'
        :dataSource='dataSource'
        :pagination='ipagination'
        :loading='loading'
        :rowSelection='{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}'
        @change='handleTableChange'>

        <span slot='action' slot-scope='text, record'>
          <a @click='handleEdit(record)'>编辑</a>
          <a-divider v-if="isShowAuth('authList:actionParam:qrcode')" type='vertical' />
          <a v-if="isShowAuth('authList:actionParam:qrcode')" @click='downQRCode(record)'>下载二维码</a>
          <a-divider v-if="isShowAuth('authList:actionParam:qrcode')" type='vertical' />
          <a v-if="isShowAuth('authList:actionParam:qrcode')" @click='openQRCode(record)'>预览二维码</a>
          <a-divider v-if="isShowAuth('authList:actionParam:urlScheme')" type='vertical' />
          <a v-if="isShowAuth('authList:actionParam:urlScheme')" @click='openUrlScheme(record)'>小程序链接</a>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <wxAuthorizationRecords-modal ref='modalForm' @ok='modalFormOk'></wxAuthorizationRecords-modal>
    <a-modal
      title="二维码预览"
      :width="500"
      :visible="visibleCodeImage"
      :footer="null"
      @cancel="handleCancel"
      cancelText="关闭">
      <div class="code-image-modal">
        <img class="code-image" alt="example" :src="codeImageSrc" />
        <div class="code-title">{{ codeTitle }}</div>
      </div>
    </a-modal>
  </a-card>
</template>

<script>
import WxAuthorizationRecordsModal from './modules/WxAuthorizationRecordsModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, downFile } from '@/api/manage'
import JInput from '@/components/jeecg/JInput'
import { colAuthFilter, globalShowedAuth } from '@/utils/authFilter'

export default {
  name: 'WxAuthorizationRecordsList',
  mixins: [JeecgListMixin],
  components: {
    JInput,
    WxAuthorizationRecordsModal
  },
  data() {
    return {
      description: '量表套餐表管理页面',
      visibleCodeImage: false,
      codeImageSrc: '',
      codeTitle: '',
      queryParam: {
        isTop: 0
      },
      // 表头
      columns: [
        {
          title: '医生名称',
          align: 'center',
          dataIndex: 'doctorName'
        },
        {
          title: '名称',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '授权状态',
          align: 'center',
          dataIndex: 'status_dictText'
        },
        {
          title: '扫描次数',
          align: 'center',
          dataIndex: 'scanNum'
        },
        {
          title: '套餐量表',
          align: 'center',
          dataIndex: 'measureNames',
          width: 600,
          ellipsis: true
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 200,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/wx/wxAuthorizationRecords/list',
        delete: '/wx/wxAuthorizationRecords/delete',
        deleteBatch: '/wx/wxAuthorizationRecords/deleteBatch',
        exportXlsUrl: 'wx/wxAuthorizationRecords/exportXls',
        importExcelUrl: 'wx/wxAuthorizationRecords/importExcel',
        downQRCode: 'wx/wxAuthorizationRecords/createQRCode',
        createUrlScheme: 'wx/wxAuthorizationRecords/createUrlScheme'
      }
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  created() {
    //权限控制
    this.initColumns()
  },
  methods: {
    handleCancel() {
      this.visibleCodeImage = false
    },
    openQRCode(record) {
      let fileName = '答题二维码'
      getAction(this.url.downQRCode, {
        scene: record.id,
        page: 'pages/answer/answer',
        width: 280,
        appid: 'wxdc6fa6c14a53b1b9'
      }).then((data) => {
        if (data.code != 200) {
          this.$message.warning('获取二维码失败')
          return
        }
        let url = data.result
        this.codeImageSrc = url
        // 设置隐藏量表标题
        if (record.isShowTitle == 1) {
          this.codeTitle = record.measureNames
        } else {
          this.codeTitle = ''
        }
        this.visibleCodeImage = true
      })
    },
    openUrlScheme(record) {
      let fileName = '小程序链接'
      getAction(this.url.createUrlScheme, {
        scene: record.id,
        page: 'pages/answer/answer',
        appid: 'wxdc6fa6c14a53b1b9'
      }).then((result) => {
        if (result.code != 0) {
          this.$message.warning('获取链接失败')
          return
        }
        this.copyToClipboard(result.result)
      })
    },
    async copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text);
        this.$message.success('小程序链接已复制')
      } catch (err) {
        console.error('Failed to copy: ', err);
      }
    },
    downQRCode(record) {
      let fileName = '答题二维码'
      getAction(this.url.downQRCode, {
        scene: record.id,
        page: 'pages/answer/answer',
        width: 280,
        appid: 'wxdc6fa6c14a53b1b9'
      }).then((data) => {
        if (data.code != 200) {
          this.$message.warning('文件下载失败')
          return
        }
        let url = data.result
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', fileName + '.png')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) //下载完成移除元素
        window.URL.revokeObjectURL(url) //释放掉blob对象
      })
    },
    // downQRCode(record) {
    //   let fileName = '答题二维码'
    //   downFile(this.url.downQRCode, { id: record.id }).then((data) => {
    //     if (!data) {
    //       this.$message.warning('文件下载失败')
    //       return
    //     }
    //     if (typeof window.navigator.msSaveBlob !== 'undefined') {
    //       window.navigator.msSaveBlob(new Blob([data]), fileName + '.png')
    //     } else {
    //       let url = window.URL.createObjectURL(new Blob([data]))
    //       let link = document.createElement('a')
    //       link.style.display = 'none'
    //       link.href = url
    //       link.setAttribute('download', fileName + '.png')
    //       document.body.appendChild(link)
    //       link.click()
    //       document.body.removeChild(link) //下载完成移除元素
    //       window.URL.revokeObjectURL(url) //释放掉blob对象
    //     }
    //   })
    // },
    isShowAuth(code) {
      return globalShowedAuth(code)
    },
    initColumns() {
      //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
      this.columns = colAuthFilter(this.columns, 'WxAuthorizationRecordsList:')
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
.code-image-modal {
  width: 100%;
  padding: 20px 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: scroll;
}
.code-image {
  width: 300px;
  min-width: 300px;
  height: 300px;
  min-height: 300px;
}
.code-title {
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  margin-top: 20px;
}
</style>
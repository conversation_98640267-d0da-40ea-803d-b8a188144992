<!-- 测评套餐 -->
<template>
  <a-card :bordered='false'>
    <!-- 查询区域 -->
    <div class='table-page-search-wrapper'>
      <a-form layout='inline'>
        <a-row :gutter='24'>
          <a-col :span="6">
            <a-form-item label="测试者编号">
              <a-input placeholder="请输入测试者编号" v-model="queryParam.userNo"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="测试者姓名">
              <a-input placeholder="请输入测试者姓名" v-model="queryParam.userName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="操作人">
              <a-input placeholder="请输入操作人" v-model="queryParam.doctorName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='6'>
            <span style='float: left;overflow: hidden;' class='table-page-search-submitButtons'>
              <a-button type='primary' @click='searchQuery' icon='search'>查询</a-button>
              <a-button type='primary' @click='searchReset' icon='reload' style='margin-left: 8px'>重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- table区域-begin -->
    <div class="table_box">
      <div class="tab_box">
        <div :class="queryParam.finishStatus == '1'?'tab_li actived':'tab_li'" @click="tabClick('1')">未完成</div>
        <div :class="queryParam.finishStatus == '2'?'tab_li actived':'tab_li'" @click="tabClick('2')">已完成</div>
      </div>

      <div class="table_center">
        <a-table
          ref='table'
          size='middle'
          bordered
          rowKey='id'
          :columns='columns'
          :dataSource='dataSource'
          :pagination='ipagination'
          :loading='loading'
          @change='handleTableChange'>
          <span slot='action' slot-scope='text, record'>
            <a @click='handleInfo(record)'>详情</a>
            <template v-if="queryParam.finishStatus == '1'">
              <a-divider type="vertical"/>
              <a @click='handleAnswer(record)'>继续答题</a>
            </template>
          </span>
        </a-table>
      </div>
    </div>
    <!-- table区域-end -->



    <!-- 继续答题 -->
    <ContinueModal ref='modalForm'></ContinueModal>

    <!-- 详情列表 -->
    <DxThailInfoList ref='modalList'></DxThailInfoList>
  </a-card>
</template>

<script>
import ContinueModal from './modules/DxContinueUnswering'
import JInput from '@/components/jeecg/JInput'
import Vue from 'vue'
import DxThailInfoList from './modules/DxThailInfoList'
import {getAction} from '@/api/manage'

export default {
  name: 'MeasurePackageRecordsList',
  components: {
    JInput,
    ContinueModal,
    DxThailInfoList
  },
  data() {
    return {
      description: '测评套餐管理',
      queryParam:{
        finishStatus:'1',
      },
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '测试者编号',
          align: 'center',
          dataIndex: 'userNo'
        },
        {
          title: '测试者姓名',
          align: 'center',
          dataIndex: 'userName'
        },
        {
          title: '量表',
          align: 'center',
          dataIndex: 'measureNames',
          width: 600,
          ellipsis: true
        },
        {
          title: '操作人',
          align: 'center',
          dataIndex: 'doctorName'
        },
        {
          title: '修改时间',
          align: 'center',
          dataIndex: 'updateTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      dataSource:[],
      url: {
        list: '/diagnosis/dxResultPackage/list',
      },
      // 分页参数
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
    }
  },
  created() {
    this.loadData();
  },
  methods: {
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = {
        userNo: this.queryParam.userNo?'*'+this.queryParam.userNo+'*':'',
        doctorName:this.queryParam.doctorName?'*'+this.queryParam.doctorName+'*':'',
        userName:this.queryParam.userName?'*'+this.queryParam.userName+'*':''
      }
      
      params.pageNo = this.ipagination.current
      params.pageSize = this.ipagination.pageSize
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          let result = res.result.records
          this.ipagination.total = res.result.total
          this.dataSource = result
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      this.ipagination = pagination
      this.loadData()
    },
    searchQuery() {
      this.loadData(1)
    },
    searchReset() {
      this.queryParam = {}
      this.loadData(1)
    },
    handleAnswer(record){
      this.$refs.modalForm.add(record.userId,record.doctorId,record.measureIds)
    },
    handleInfo(record) {
      this.$refs.modalList.add(record.id)
    },
    tabClick(val){
      if(this.queryParam.finishStatus == val){
        return;
      }
      this.queryParam.finishStatus = val;
      this.loadData(1);
    }
  }
}
</script>
<style scoped lang="scss">
@import '~@assets/less/common.less';
.table_box{
  border: 1px solid #ddd;
  .tab_box{
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    .tab_li{
      padding: 0 20px;
      height: 40px;
      box-sizing: border-box;
      line-height: 40px;
      display: inline-block;
      list-style: none;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      position: relative;
      border: 1px solid transparent;
      cursor: pointer;
    }
    .tab_li:first-child{
      margin-left: -1px;
    }
    .actived{
      color: #409eff;
      background-color: #fff;
      border-right-color: #dcdfe6;
      border-left-color: #dcdfe6;
      position: relative;
      &::after{
        position: absolute;
        content: '';
        height: 4px;
        bottom: -2px;
        width: 100%;
        background: #fff;
        left: 0;
      }
    }
  }
  .table_center{
    padding: 20px;
  }
}
</style>
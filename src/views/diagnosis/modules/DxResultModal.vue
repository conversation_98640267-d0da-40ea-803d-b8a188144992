<template>
  <a-modal
    :title="title"
    :width="1200"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    :okButtonProps="{ props: {disabled: disableSubmit} }"
    @ok="handleOk"
    @cancel="handleCancel">
    <a-spin :spinning="confirmLoading">
      <!-- 主表单区域 -->
      <a-form :form="form">
        <a-row>
          <a-col :span="12" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="测试者编号">
              <a-input disabled placeholder="请输入测试者编号" v-decorator="['userNo', {}]"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="测试者名称">
              <a-input disabled placeholder="请输入测试者名称" v-decorator="['userName', {}]"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="量表名称">
              <a-input disabled placeholder="请输入量表名称" v-decorator="['measureName', {}]"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="答题状态">
              <a-input disabled placeholder="请输入答题状态" v-decorator="['status_dictText', {}]"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="总分">
              <a-input-number placeholder="请输入总分" style="width:100%" v-decorator="[ 'totalPoints', {}]"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" :gutter="8" v-if="hasTimeStr">
            <a-form-item v-if="hasTimeStr"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="测试时间(毫秒)">
              <a-input placeholder="请输入测试时间" v-decorator="['time', {}]"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12" :gutter="8">
            <a-form-item label="答题时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker showTime format='YYYY-MM-DD HH:mm:ss' v-decorator="[ 'createTime', {}]"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" :gutter="8" v-has="'resultList:updateTime'" v-if="!hasRole1">
            <a-form-item label="修改时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker showTime format='YYYY-MM-DD HH:mm:ss' v-decorator="[ 'updateTime', {}]"/>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 子表单区域 -->
        <a-tabs defaultActiveKey="1" v-if="isShowDetail">
          <a-tab-pane tab="答案" key="1">
            <a-spin :spinning="optionLoading">
              <div>
                <a-row type="flex" style="margin-bottom:10px" :gutter="16">
                  <a-col :span="2">题号</a-col>
                  <a-col :span="14">标题</a-col>
                  <a-col :span="3">选项内容</a-col>
                  <a-col :span="2">分数</a-col>
                  <a-col :span="2" v-if="hasTimeStr">用时(毫秒)</a-col>
                </a-row>

                <a-row type="flex" style="margin-bottom:10px" :gutter="16"
                       v-for="(item, index) in resultMainModel.dxResultOptionList" :key="index">
                  <a-col :span="2">
                    <a-form-item>
                      <a-input placeholder="题号"
                               v-decorator="['dxResultOptionList['+index+'].questionNumber', {'initialValue':item.questionNumber,rules: [{ required: true, message: '请输入题号!' }]}]"/>
                    </a-form-item>
                  </a-col>
                  <a-col :span="14">
                    <a-form-item>
                      <a-input placeholder="标题"
                               v-decorator="['dxResultOptionList['+index+'].questionTitle', {'initialValue':item.questionTitle,rules: [{ required: true, message: '请输入标题!' }]}]"/>
                    </a-form-item>
                  </a-col>
                  <a-col :span="3">
                    <a-form-item>
                      <a-input placeholder="内容"
                               v-decorator="['dxResultOptionList['+index+'].answer', {'initialValue':item.answer,rules: [{ required: true, message: '请输入内容!' }]}]"/>
                    </a-form-item>
                  </a-col>
                  <a-col :span="2">
                    <a-form-item>
                      <a-input placeholder="分数"
                               v-decorator="['dxResultOptionList['+index+'].score', {'initialValue':item.score,rules: [{ required: true, message: '请输入分数!' }]}]"/>
                    </a-form-item>
                  </a-col>
                  <a-col :span="2" v-if="hasTimeStr">
                    <a-form-item>
                      <a-input placeholder="用时"
                               v-decorator="['dxResultOptionList['+index+'].time', {'initialValue':item.time,rules: [{ required: true, message: '请输入用时!' }]}]"/>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
            </a-spin>
          </a-tab-pane>
        </a-tabs>

      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import Vue from 'vue'
  import { USER_ROLE } from '@/store/mutation-types'
  import pick from 'lodash.pick'
  import moment from 'moment'
  import { getAction, httpAction } from '@/api/manage'
  import { globalShowedAuth } from '@/utils/authFilter'

  export default {
    name: 'DxResultModal',
    data() {
      return {
        title: '操作',
        // 新增时子表默认添加几行空数据
        addDefaultRowNum: 1,
        disableSubmit: false,
        visible: false,
        isShowDetail: false,
        confirmLoading: false,
        optionLoading: true,
        form: this.$form.createForm(this),
        dateFormat: 'YYYY-MM-DD HH:mm:ss',
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        resultMainModel: { dxResultOptionList: [{}] },
        url: {
          add: '/diagnosis/dxResult/add',
          edit: '/diagnosis/dxResult/edit',
          dxResultOption: {
            list: '/diagnosis/dxResult/queryDxResultOptionByMainId'
          }
        },
        hasRole1: Vue.ls.get(USER_ROLE, []).includes('myyywswbi'),
        hasTimeStr: false,
      }
    },
    methods: {
      moment,
      add(resultId) {
        this.resultMainModel.resultId = resultId
        this.edit({})
      },
      handleCancel() {
        this.close()
      },
      view(record) {
        this.isShowAuth("resultList:resultDetail:timeStr")
        this.form.resetFields()
        this.resultMainModel = Object.assign({}, record)
        this.resultMainModel.dxResultOptionList = [{}]
        this.isShowDetail = true
        // 加载子表数据
        if (this.resultMainModel.id) {
          this.optionLoading = true
          let params = { id: this.resultMainModel.id }
          //初始化选项列表
          getAction(this.url.dxResultOption.list, params).then((res) => {
            if (res.success) {
              this.resultMainModel.dxResultOptionList = res.result
              this.$forceUpdate()
              this.optionLoading = false
            }
          })
          this.visible = true
          this.$nextTick(() => {
            this.form.setFieldsValue(pick(this.resultMainModel, 'userNo', 'status_dictText', 'totalPoints', 'measureName', 'userName', 'createTime'))
            if (this.hasTimeStr) this.form.setFieldsValue({time: this.resultMainModel.time})
            //时间格式化
            this.form.setFieldsValue({createTime: this.resultMainModel.createTime ? moment(this.resultMainModel.createTime, 'YYYY-MM-DD HH:mm:ss') : null})
            if (!this.hasRole1) this.form.setFieldsValue({updateTime: this.resultMainModel.updateTime ? moment(this.resultMainModel.updateTime, 'YYYY-MM-DD HH:mm:ss') : null})
          })
        }
      },
      edit(record) {
        this.isShowAuth("resultList:resultDetail:timeStr")
        this.form.resetFields()
        this.resultMainModel = Object.assign({}, record)
        this.resultMainModel.dxResultOptionList = [{}]
        this.isShowDetail = false
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.resultMainModel, 'userNo', 'status_dictText', 'totalPoints', 'measureName', 'userName', 'createTime'))
          if (this.hasTimeStr) this.form.setFieldsValue({time: this.resultMainModel.time})
          //时间格式化
          this.form.setFieldsValue({createTime: this.resultMainModel.createTime ? moment(this.resultMainModel.createTime, 'YYYY-MM-DD HH:mm:ss') : null})
          if (!this.hasRole1) this.form.setFieldsValue({updateTime: this.resultMainModel.updateTime ? moment(this.resultMainModel.updateTime, 'YYYY-MM-DD HH:mm:ss') : null})
        })
      },
      // 确定
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.resultMainModel.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let quesionData = Object.assign(this.resultMainModel, values)
            let formData = {
              ...quesionData,
            }
            if (this.isShowDetail) {
              let dxResultOptionList = this.resultMainModel.dxResultOptionList
              for (let i = 0; i < this.resultMainModel.dxResultOptionList.length; i++) {
                dxResultOptionList[i].title = values.dxResultOptionList[i].title
                dxResultOptionList[i].content = values.dxResultOptionList[i].content
                dxResultOptionList[i].score = values.dxResultOptionList[i].score
                dxResultOptionList[i].sort = values.dxResultOptionList[i].sort
              }
              formData.psOptions = dxResultOptionList
            }else{
              formData.dxResultOptionList = [];
            }
            //时间格式化
            formData.createTime = formData.createTime ? formData.createTime.format('YYYY-MM-DD HH:mm:ss') : null;
            if (!this.hasRole1) formData.updateTime = formData.updateTime ? formData.updateTime.format('YYYY-MM-DD HH:mm:ss') : null;
            formData.type = '0'
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      isShowAuth(code) {
        this.hasTimeStr = globalShowedAuth(code)
      },
      close() {
        this.$emit('close')
        this.disableSubmit = false
        this.visible = false
      }
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
</style>
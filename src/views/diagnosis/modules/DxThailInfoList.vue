<template>
  <a-modal
    title='详情'
    :width='1000'
    :visible='visible'
    :confirmLoading='downLoading'
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'>
    <a-spin :spinning="downLoading" tip="下载中...">
      <a-card :bordered="false">
        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-button type="primary" icon="arrow-down" @click="batchExportDoc('批量下载报告')">批量下载报告</a-button>
          <a-button type="primary" icon="printer" @click="batchPrint">批量打印</a-button>
        </div>

        <div>
          <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
            <i class="anticon anticon-info-circle ant-alert-icon"></i>
            <span>已选择</span>
            <a style="font-weight: 600">
              {{ selectedRowKeys.length }}
            </a>
            <span>项</span>
            <a style="margin-left: 24px" @click="onClearSelected">清空</a>
          </div>

          <a-table
            ref="table"
            size="middle"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
            @change="handleTableChange">
            <span slot="degree" slot-scope="text">
              <a-badge :color="text | statusTypeFilter" :text="text | statusFilter"/>
            </span>
            <span slot="action" slot-scope="text, record">
              <a @click="handlePrintForm(record)">内容预览</a>
              <a-divider type="vertical"/>
              <a @click="handleExportDoc(record)">报告下载</a>
            </span>
          </a-table>
        </div>
        <!-- table区域-end -->
      </a-card>
      <!-- 打印表单页 -->
      <report-common-form ref="ReportForm"></report-common-form>
      <!-- 眼动筛查报告 -->
      <dx-em-report-model ref="emReportFrom"></dx-em-report-model>
    </a-spin>
  </a-modal>
</template>
<script>
import Vue from 'vue'
import { USER_ROLE } from '@/store/mutation-types'
import DxEmReportModel from '../modules/DxEmReportModel'
import {deleteAction, downFile, getAction} from '@/api/manage'
import ReportCommonForm from '../../dashboard/ReportCommonForm'
import JInput from '@comp/jeecg/JInput.vue'

const degreeMap = {
  0: {
    color: '#D9D9D9',
    text: '未答完'
  },
  1: {
    color: 'green',
    text: '正常'
  },
  2: {
    color: 'yellow',
    text: '轻度'
  },
  3: {
    color: 'red',
    text: '中度'
  },
  4: {
    color: 'purple',
    text: '重度'
  },
  5: {
    color: '#D9D9D9',
    text: '未知'
  }
}

export default {
  name: 'DxThailInfoList',
  components: {
    JInput,
    ReportCommonForm,
    DxEmReportModel
  },
  data() {
    return {
      visible:false,
      description: '测评结果详情',
      downLoading: false,
      dxResultPackageId:'',
      // 表头
      columns: [
        {
          title: '量表名称',
          align: 'center',
          dataIndex: 'measureName'
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'status_dictText'
        },
        {
          title: '预警结果',
          align: 'center',
          dataIndex: 'degree',
          scopedSlots: {customRender: 'degree'}
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: {customRender: 'action'}
        }
      ],
      // 请求参数
      url: {
        list: '/diagnosis/dxResult/list',
        batchExportDoc: 'diagnosis/dxResult/batchExportDoc',
        importExcelUrl: 'diagnosis/dxResult/importExcel',
        exportDocUrl: 'diagnosis/dxResult/exportSingleDoc'
      },
      // 分页参数
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      loading:false,
      dataSource:[],
      selectedRowKeys: [],
      hasRole3: Vue.ls.get(USER_ROLE, []).includes('myyyts'),
      hasRoleFZD: Vue.ls.get(USER_ROLE, []).includes('fzddt'),
    }
  },
  filters: {
    statusFilter(type) {
      return degreeMap[type].text
    },
    statusTypeFilter(type) {
      return degreeMap[type].color
    }
  },
  created() {
    
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    add(dxResultPackageId) {
      this.visible = true;
      this.dxResultPackageId = dxResultPackageId;
      this.loadData()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.close()
    },
    handleCancel() {
      this.close()
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = {}
      params.dxResultPackageId = this.dxResultPackageId;
      params.pageNo = this.ipagination.current
      params.pageSize = this.ipagination.pageSize
      params.column = 'updateTime';
      params.order = 'desc'
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          let result = res.result.records
          this.ipagination.total = res.result.total
          this.dataSource = result
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    //报告下载
    handleExportDoc(record) {
      if (record.status != '2') {
        this.$message.error('未答完题目不能查看报告！')
        return
      }
      this.downLoading = true
      var fileName = record.userName + '_' + record.measureName + '.pdf'
      let param = {...this.queryParam}
      delete param.createTimeRange // 时间参数不传递后台
      param.ids = record.id
      downFile(this.url.exportDocUrl, param).then((data) => {
        if (!data) {
          this.$message.warning('文件下载失败')
          this.downLoading = false
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), fileName)
          this.downLoading = false
        } else {
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(url) //释放掉blob对象
          this.downLoading = false
        }
      })
    },
    //内容预览
    handlePrintForm(record) {
      if (record.status != '2') {
        this.$message.error('未答完题目不能预览！')
        return
      }
      // 眼动筛查报告
      if (record.assessType === 'em') {
        this.$refs.emReportFrom.edit(record)
        this.$refs.emReportFrom.title = '内容预览'
      } else {
        this.$refs.ReportForm.edit(record)
        this.$refs.ReportForm.title = '内容预览'
      }
    },
    //批量打印
    batchPrint: function () {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择至少一条记录！')
        return
      } else if (this.selectedRowKeys.length > 30) {
        this.$message.warning('请不要选择超过三十条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        this.$refs.ReportForm.batchPrint(ids)
        this.$refs.ReportForm.title = '批量打印'
      }
    },
    //批量下载
    batchExportDoc(fileName) {
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      if (this.selectedRowKeys.length > 300) {
        this.$message.warning('最大仅可导出300条')
        return
      }
      this.downLoading = true
      if (!fileName || typeof fileName != 'string') {
        fileName = '导出文件'
      }
      let param = {...this.queryParam}
      delete param.createTimeRange // 时间参数不传递后台
      if (this.selectedRowKeys && this.selectedRowKeys.length > 0) {
        param['ids'] = this.selectedRowKeys.join(',')
      }
      downFile(this.url.batchExportDoc, param, 'post').then((data) => {
        if (!data) {
          this.$message.warning('文件下载失败')
          this.downLoading = false
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), fileName + '.zip')
          this.downLoading = false
        } else {
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName + '.zip')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(url) //释放掉blob对象
          this.downLoading = false
        }
      })
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    onClearSelected() {
      this.selectedRowKeys = []
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      this.loadData()
    }
  }
}
</script>
<style lang="less" scoped>
/** Button按钮间距 */
.ant-btn {
  margin-left: 3px
}

.ant-card-body .table-operator {
  margin-bottom: 18px;
}

.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

.anty-row-operator button {
  margin: 0 5px
}

.ant-btn-danger {
  background-color: #ffffff
}

.ant-modal-cust-warp {
  height: 100%
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden
}
</style>
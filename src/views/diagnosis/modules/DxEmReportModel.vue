<template>
  <a-drawer
    :title='title'
    :maskClosable='true'
    :width='900'
    placement='right'
    :closable='true'
    destroyOnClose
    @close='handleCancel'
    :visible='visible'>
    <a-spin :spinning='loading' tip="下载中...">
      <div class="em-btn-box" style="margin-right: 45px;">
        <a-button v-print="'#emPdf'" ghost type='primary' style='margin-right: 10px;'>打印</a-button>
        <a-button ghost type='primary' @click="handleExportPdf">下载</a-button>
      </div>
      
      <div class="em-report-content" id="emPdf">
        <h2 style="text-align: center; align-items: center;">
          <span lang="EN-US" style="font-size: 18.0pt; font-family: 黑体; color: black;" v-if="!isCustomized()">{{ datas.title }}</span>
          <div v-else>
            <img style="width: 100%; height: 85px;" :src="customizedImg" alt="">
          </div>
        </h2>
        <h3 style="text-align: center; align-items: center;">
          <span lang="EN-US">眼动认知评估</span>
          结果分析报告
        </h3>
        <p style="text-align: right; margin-bottom: 0px; margin-top: 0px;">
          <span style="font-size: 12px; text-align: right; background-color: #ffffff;">编号: {{ datas.userNumber }}</span>
        </p>
        <table style="border-collapse: collapse; width: 100%; height: 27px; border-style: hidden; margin-left: auto; margin-right: auto; border-bottom: 1px solid black; border-top: 1px solid black;">
          <tbody>
            <tr style="height: 30px; text-align: left;">
              <td style="width: 29.9841%; height: 27px; text-align: left;"><span style="font-size: 14px; text-align: start;"><span style="letter-spacing: 2em;">姓</span>名: {{ datas.name }}</span></td>
              <td style="width: 24.3917%; height: 27px; text-align: left;"><span style="font-size: 14px; background-color: #ffffff; letter-spacing: 2em;">性</span><span lang="EN-US" style="font-size: 14px; background-color: #ffffff;">别: {{ datas.sex }}</span></td>
              <td style="width: 30.3881%; height: 27px; text-align: left;"><span style="font-size: 14px; background-color: #ffffff; letter-spacing: 2em;">年</span><span lang="EN-US" style="font-size: 14px; background-color: #ffffff;">龄: {{ datas.age }}</span></td>
            </tr>
            <tr>
              <td style="width: 29.9841%; text-align: left;"><span style="font-size: 14px; text-align: start;">婚姻状况: {{ datas.marital }}</span></td>
              <td style="width: 24.3917%; text-align: left;"><span style="font-size: 14px; background-color: #ffffff;"><span style="letter-spacing: 2em;">职</span>业: {{ datas.profession }}</span></td>
              <td style="width: 30.3881%; text-align: left;"><span style="font-size: 14px; background-color: #ffffff;">文化程度: {{ datas.education }}</span></td>
            </tr>
            <tr>
              <td style="width: 29.9841%; text-align: left;"><span style="font-size: 14px; text-align: start;"><span style="letter-spacing: 2em;">科</span>室: {{ datas.departmentName }} </span></td>
              <td style="width: 24.3917%; text-align: left;"><span style="font-size: 14px; background-color: #ffffff;"><span style="letter-spacing: 0.5em; margin-right: -0.1em;">住院</span>号: {{ datas.userNumber }}</span></td>
              <td style="width: 30.3881%; text-align: left;"><span style="font-size: 14px; background-color: #ffffff;"><span style="letter-spacing: 2em;">用</span>时: {{ datas.time }}</span></td>
            </tr>
          </tbody>
        </table>

        <div class="em-point">
          <div class="point-left">
            <div class="point-top" v-if="datas.gotScore">
              <p class="point-title" :style="{left: datas.gotScore <= 90 ? `calc(${datas.gotScore}% - 10px)` : 'none', right: datas.gotScore > 90 ? `calc(${100 - datas.gotScore}% - 10px)` : 'none'}">
                <a-icon v-if="datas.gotScore <= 90" type="environment" :style="{fontSize: '18px', color: datas.colour}" />
                <span>{{ datas.conclusion }}</span>
                <a-icon v-if="datas.gotScore > 90" type="environment" :style="{fontSize: '18px', color: datas.colour}" />
              </p>
              <div class="point-line"></div>
            </div>
            <p class="point-info">健康建议：{{ datas.suggestion }}</p>
          </div>
          <div class="point-right">
            <p class="moca-point">MoCA AI预测分: {{ datas.mocaScoreAi }}</p>
            <p class="moca-detail">
              *量表分数由AI算法预测: 深度分析受试者眼动行为提取相关特征，并结合受试者的年龄、性别、受教育程度等社会学特征通过机器学习模型预测受试者量表分数
            </p>
          </div>
        </div>

        <div class="em-chart">
          <div id="radarChart" class="radar-chart"></div>
          <div class="bar-chart-box">
            <div class="bar-lengen">
              <div class="lengen-item" v-for="item in lengenList" :key="item.quotaName">
                <div class="item-point" :style="{background: getColor(item)}"></div>
                <span class="item-name">{{ item.quotaName }}</span>
              </div>
            </div>
            <div id="barChart" class="bar-chart"></div>
          </div>
        </div>

        <div class="em-content next-page">
          <p class="content-title">眼动轨迹分析</p>
          <p class="content-info">以下为您答题过程中眼球运动的轨迹图</p>
          <div class="content-main">
            <div class="content-item" v-for="(item, index) in datas.imageUrls" :key="index">
              <p class="item-title">第{{ index + 1 }}题</p>
              <img @click="getBigImg(datas.imageDomain + item)" class="item-img" :src="datas.imageDomain + item" />
            </div>
          </div>
        </div>

        <div class="subject-record next-page" style="margin-top: 30px;">
          <div class="record-row">
            <div class="record-title">认知障碍科普</div>
          </div> 
          <div class="subject-desc">
            <span>认知是人脑接受外界信息，经过加工处理，转换成内在的心理活动，从而获取知识或应用知识的过程。它包括记忆、语言、视空间、执行、计算和理解判断等方面。认知障碍指与上述学习记忆以及思维判断有关的大脑高级智能加工过程出现异常， 从而引起严重学习、记忆障碍，同时伴有失语或失用或失认或失行等改变的病理过程<sup>[1]</sup>。</span>
          </div>
        </div>

        <div class="subject-record" style="margin-top: 30px;">
          <div class="record-row">
            <div class="record-title">认知障碍生命历程风险因素</div>
          </div> 
          <div class="img-list">
            <div class="image-text">
              <img src="../../../assets/img/vr_img.jpg" object-fit="contain" style="width: 350px; margin-right: 20px;"> 
              <div class="img-desc">
                <span>认知障碍的风险因素贯穿生命历程中的各个阶段，分年龄阶段进行风险因素的预防以及干预和护理对认知障碍的发生发展起到至关重要的作用。 2020年Lancet委员会制定的《痴呆预防、干预和护理》<sup>[2]</sup>指出，生命历程风险模型(如表1)目前共包含12种风险因素，其中滥用乙醇、创伤 性脑损伤(Traumatic Brain Injury,TBI)和空气污染为新增风险因素，并描述了各风险因素发生的比率，计算出完全消除此风险因素后痴呆减 少的比率即人口归因率(Population Attributable Fraction,PAF)，指在特定时间内，如果某一可控风险因素完全消除，新病例减少的百分比。</span>
              </div>
            </div> 
            <div class="table-box" style="margin-top: 0px;">
              <div class="table-title">痴呆生命历程风险模型（表1）</div> 
              <div class="table-row">
                <div class="table-item table-a">年龄阶段</div> 
                <div class="table-item table-a">风险因素</div> 
                <div class="table-item table-a">风险因数发生率（%）</div> 
                <div class="table-item table-a">PAF（%）</div>
              </div> 
              <div class="table-row" style="background: rgb(238, 246, 255);">
                <div class="table-item table-a">青少年（&lt;45岁）</div> 
                <div class="table-item table-a">缺乏教育或教育水平低下</div> 
                <div class="table-item table-a">40.0</div> 
                <div class="table-item table-a">7.1</div>
              </div> 
              <div class="table-row">
                <div class="table-item table-a">中年期（45~65岁）</div> 
                <div class="table-item table-a">听力损失<br><br>创伤性脑损伤<br><br>高血压<br><br>肥胖（BMI&gt;30）<br><br>乙醇滥用（&gt;21U/周）</div>
                <div class="table-item table-a">31.7<br><br>12.1<br><br>8.9<br><br>11.8<br><br>3.4</div>
                <div class="table-item table-a">8.2<br><br>3.4<br><br>1.9<br><br>0.8<br><br>0.7</div>
              </div> 
              <div class="table-row" style="background: rgb(238, 246, 255);">
                <div class="table-item table-a">老年期（&gt;65岁）</div> 
                <div class="table-item table-a">吸烟<br><br>抑郁<br><br>缺乏体育活动<br><br>社会接触减少<br><br>糖尿病<br><br>空气污染</div> 
                <div class="table-item table-a">27.4<br><br>13.2<br><br>11.0<br><br>17.7<br><br>6.4<br><br>7.5</div> 
                <div class="table-item table-a">5.2<br><br>3.9<br><br>3.5<br><br>1.6<br><br>1.1<br><br>2.3</div>
              </div>
            </div>
          </div> 
          <div class="data-source" style="margin-top: 20px; font-size: 14px; color: rgb(51, 51, 51);">
            <p>资料来源:</p> 
            <p>[1] Kiely, K.M. (2014). Cognitive Function. In: Michalos, A.C. (eds) Encyclopedia of Quality of Life and Well-Being Research. Springer, Dordrecht. https://doi.org/10.1007/978-94-007-0753-5_426</p> 
            <p>[2] Livingston, Gill, et al. "Dementia prevention, intervention, and care: 2020 report of the Lancet Commission." The lancet 396.10248 (2020): 413-446.</p>
          </div>
        </div>

        <table style="border-collapse: collapse; width: 100%;">
          <tbody>
            <tr>
              <td style="width: 34.4262%; border-bottom: 1px solid black;">&nbsp;</td>
              <td style="width: 6%; border-bottom: 1px solid black;"><span style="font-size: 14px; text-align: right;">签字:</span></td><td style="width: 28%;border-bottom: 1px solid black;"><img src="https://zcpm.zhisongkeji.com/mentality-ct/temp/image/signature/jie2ping22024-10-2815.51.35_1730101903709.51.35.png" style="width: 80px;height: 50px;text-align: left;"></td>
              <td style="width: 32.5138%; border-bottom: 1px solid black;"><span style="font-size: 14px; text-align: right;">日期: {{ datas.createTime }}</span></td>
            </tr>
          </tbody>
        </table>
        <p class="MsoNormal" style="text-align: right; margin-bottom: 0px; margin-top: 0px;" align="center">
          <span style="font-size: 14px; text-align: right;">（本报告只作为临床参考）</span>
        </p>
      </div>
    </a-spin>

    <a-modal v-model="imgShow" title="图片预览" okText="关闭" cancelText="" @ok="imgShow = false">
      <img style="width: 1000px" :src="imgUrl" />
    </a-modal>
  </a-drawer>
</template>

<script>
import { getAction } from '@/api/manage'
import JEditor from '@/components/jeecg/JEditor'
import { mapGetters } from 'vuex'
import * as echarts from 'echarts'
import PdfLoader from 'k-htmlpdf'
import { USER_INFO } from '@/store/mutation-types'
import Vue from 'vue'

export default {
  name: 'ReportCommonForm',
  components: {
    JEditor
  },
  data() {
    return {
      title: '操作',
      loading: false,
      drawerWidth: 700,
      visible: false,
      datas: {
        gotScore: null
      },
      isEdit: false,
      editContent: '',
      customizedImg: '',
      lengenList: [],
      url: {
        getReportById: '/vr/report/getReportById/' // 眼动筛查报告
      },
      imgShow: false,
      imgUrl: ''
    }
  },
  methods: {
    ...mapGetters(['userInfo']),
    handleCancel() {
      this.close()
    },
    edit(record) {
      let that = this
      that.resetScreenSize() // 调用此方法,根据屏幕宽度自适应调整抽屉的宽度
      that.visible = true
      that.datas.gotScore = null
      that.getReportById(record.id)
    },

    isCustomized(record) {
      let userInfo = Vue.ls.get(USER_INFO)
      if (userInfo.orgCode == "A06A57A25") {
        this.customizedImg = "https://img.zhisongkeji.com//final/szqhtkyy.png"
        return true
      }
      return false
    },

    handleExportPdf() {
      this.loading = true
      // 滚动到顶部，确保打印内容完整
      document.body.scrollTop = 0; // IE的
      document.documentElement.scrollTop = 0; // 其他
      let dom = document.getElementById("emPdf");
      let pdf = new PdfLoader(dom, "眼动筛查报告", "content-item",'break_page');
      pdf.outPutPdfFn("眼动筛查报告");
      setTimeout(() => {
        this.loading = false
      }, 2000)
    },
    getReportById(ids) {
      getAction(this.url.getReportById + ids).then((res) => {
        if (res.success) {
          this.datas = res.result
          this.$nextTick(() => {
            this.initChart()
          })
        }
      })
    },

    initChart() {
      const radarChart = echarts.init(document.getElementById('radarChart')) // 雷达图
      const barChart = echarts.init(document.getElementById('barChart')) // 柱状图
      this.lengenList = this.datas.answerScores.reduce((accumulator, current) => {
        if (!accumulator.has(current.quotaName)) {
          accumulator.set(current.quotaName, current);
        }
        return accumulator;
      }, new Map()).values();

      const radarOption = {
        color: ['#2260D6'],
        radar: {
          indicator: this.datas.radarData.map(item => {
            return {
              name: item.quotaName,
              max: item.maxScore
            }
          }),
          startAngle: 60,
          splitNumber: 2,
          radius: 85,
          splitArea: {
              areaStyle: {
                color: ['#80D9FE', '#A0C4FF'],
              }
            },
            // axisLine: {
            //   lineStyle: {
            //     color: 'rgba(255, 255, 255, 0.5)'
            //   }
            // },
            axisName: {
              color: '#A5A5A5'
            },
        },
        series: [
          {
            type: 'radar',
            data: [
              {
                value: this.datas.radarData.map(item => item.gotScore),
                areaStyle: {
                  color: "rgba(43, 135, 186, 0.6)"
                }
              }
            ]
          }
        ]
      };

      const barOption = {
        xAxis: {
          type: 'category',
          axisTick: {
            show: false
          },
          data: this.datas.answerScores.map(item => item.orderNo),
        },
        grid: {
          top: '20%',
          left: '20%',
          right: '20%',
          bottom: '20%'
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLine: {
            show: true
          },
          axisLabel: {
            show: false
          }
        },
        label: {
            show: false
          },
        series: [
          {
            data: this.datas.answerScores.map(item => {
              return {
                value: item.gotScore,
                itemStyle: {
                  color: this.getColor(item)
                }
              }
            }),
            type: 'bar',
            barWidth: 14
          }
        ]
      };

      radarChart.setOption(radarOption)
      barChart.setOption(barOption)
    },

    getColor(item) {
      let color = ''
      switch(item.quotaName) {
        case '注意力':
          color = '#2360D6'
          break;
        case '抽象力':
          color = '#7c67ff'
          break;
        case '计算力':
          color = '#e6cc1d'
          break;
        case '执行力':
          color = '#4ec537'
          break;
        case '记忆力':
          color = '#b77619'
          break;
        case '回忆':
          color = '#fc7ac3'
          break;
      }
      return color
    },
    close() {
      this.$emit('close')
      this.visible = false
      this.isEdit = false
    },
    // 根据屏幕变化,设置抽屉尺寸
    resetScreenSize() {
      let screenWidth = document.body.clientWidth
      if (screenWidth < 500) {
        this.drawerWidth = screenWidth
      } else {
        this.drawerWidth = 900
      }
    },
    a4SizeInPixels() {
      var dpi = this.getDPI();
      var width_mm = 210; // A4纸宽度，单位：毫米
      var width_px = this.mmToPixel(width_mm, dpi);
      return { width: width_px };
    },
    mmToPixel(mm, dpi) {
    // 1 inch = 25.4 mm
      var inches = mm / 25.4;
      var pixels = inches * dpi;
      return Math.round(pixels);
    },
    getDPI() {
      var tempDiv = document.createElement("div");
      tempDiv.style.width = "1in";
      tempDiv.style.visibility = "hidden";
      document.body.appendChild(tempDiv);
      var dpi = tempDiv.offsetWidth;
      document.body.removeChild(tempDiv);
      return dpi;
    },

    getBigImg(src) {
      this.imgShow = true
      this.imgUrl = src
    }
  }
}
</script>

<style lang="less" scoped>
.em-btn-box {
  display: flex;
  justify-content: flex-end;
  padding: 20px 0 0 0;
}
.em-report-content {
  padding: 20px 30px;

  .em-point {
    display: flex;
    justify-content: space-between;
    margin-top: 70px;

    .point-left {
      width: 64%;
      padding: 15px 20px;
      background: #f9f9f9;
      -webkit-print-color-adjust:exact;
      -moz-print-color-adjust:exact;
      -ms-print-color-adjust:exact;
      print-color-adjust:exact;

      .point-top {
        position: relative;
        width: 100%;
        padding-top: 30px;

        .point-title {
          display: inline-flex;
          align-items: center;
          position: absolute;
          top: 0;
          font-size: 16px;
          color: #333;
          font-weight: 700;
          margin-bottom: 15px;

          span {
            display: inline-block;
            white-space: nowrap;
            padding: 0 8px;
          }

          // &::after {
          //   content: "";
          //   position: absolute;
          //   left: -28px;
          //   top: -1px;
          //   width: 18px;
          //   height: 22px;
          //   background-image: url(data:image/png;base64,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);
          //   background-size: 100% 100%;
          // }
        }

        .point-line {
          width: 100%;
          height: 15px;
          background: linear-gradient(90deg, #d31515, #df2e04, #ffd800, #03a816, #2dae05);
          border-radius: 18px;
          margin-bottom: 15px;
          -webkit-print-color-adjust:exact;
          -moz-print-color-adjust:exact;
          -ms-print-color-adjust:exact;
          print-color-adjust:exact;
        }
      }

      .point-info {
        font-size: 15px;
        line-height: 24px;
        color: #333;
        margin: 10px 0 0 0;
      }
    }

    .point-right {
      width: 35%;
      padding: 15px 20px;
      background: #f9f9f9;
      -webkit-print-color-adjust:exact;
      -moz-print-color-adjust:exact;
      -ms-print-color-adjust:exact;
      print-color-adjust:exact;

      .moca-point {
        height: 50px;
        position: relative;
        padding: 20px 0 20px 14px;
        font-size: 18px;
        font-family: PingFang SC;
        font-weight: 600;
        color: #333;
        line-height: 22px;
        margin: 0;

        &::after {
          content: '';
          position: absolute;
          top: 20px;
          left: 0;
          width: 4px;
          height: 22px;
          background: #0e388a;
          -webkit-print-color-adjust:exact;
          -moz-print-color-adjust:exact;
          -ms-print-color-adjust:exact;
          print-color-adjust:exact;
        }
      }

      .moca-detail {
        color: #333;
        font-size: 15px;
        line-height: 24px;
        margin: 10px 0 0 0;
      }
    }
  }

  .em-chart {
    margin-top: 80px;
    margin-bottom: 60px;
    display: flex;
    align-items: center;
    // justify-content: space-around;

    .radar-chart {
      width: 50%;
      height: 400px;
    }

    .bar-chart-box {
      position: relative;
      width: 50%;
      height: 420px;

      .bar-chart {
        width: 100%;
        height: 400px;
      }

      .bar-lengen {
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        padding-top: 20px;
        margin-bottom: -20px;

        .lengen-item {
          display: flex;
          align-items: center;
          padding-left: 10px;

          .item-point {
            width: 11px;
            height: 11px;
            -webkit-print-color-adjust:exact;
            -moz-print-color-adjust:exact;
            -ms-print-color-adjust:exact;
            print-color-adjust:exact;
          }

          .item-name {
            color: #999;
            font-size: 13px;
            margin-left: 2px;
          }
        }
      }
    }
  }

  .next-page {
    page-break-before: always;
  }

  .em-content {

    .content-title {
      color: #333;
      font-size: 20px;
      font-weight: 600;
      position: relative;
      padding-left: 10px;
      margin-bottom: 10px;

      &::after {
        content: "";
        width: 4px;
        height: 17px;
        background: #0e388a;
        position: absolute;
        left: 0;
        top: 3px;
        -webkit-print-color-adjust:exact;
        -moz-print-color-adjust:exact;
        -ms-print-color-adjust:exact;
        print-color-adjust:exact;
      }
    }

    .content-info {
      color: #333;
      font-size: 16px;
      margin: 0 0 0 10px;
    }

    .content-main {
      margin-top: 20px;
      display: flex;
      flex-wrap: wrap;

      .content-item {
        margin: 0 10px 20px 10px;
        width: calc(33.3% - 20px);
        background-color: #f9f9f9;
        padding: 20px 10px 0;
        border-radius: 8px;
        -webkit-print-color-adjust:exact;
        -moz-print-color-adjust:exact;
        -ms-print-color-adjust:exact;
        page-break-inside: avoid;
        print-color-adjust:exact;

        .item-title {
          font-size: 18px;
          color: #333;
          margin: 0;
        }

        .item-img {
          width: 100%;
          margin-top: 10px;
        }
      }
    }
  }

  .subject-record {
    .record-row {
      .record-title {
        color: #333;
        font-size: 20px;
        font-weight: 600;
        position: relative;
        padding-left: 10px;
        margin-bottom: 10px;

        &::after {
          content: "";
          width: 4px;
          height: 17px;
          background: #0e388a;
          position: absolute;
          left: 0;
          top: 3px;
          -webkit-print-color-adjust:exact;
          -moz-print-color-adjust:exact;
          -ms-print-color-adjust:exact;
          print-color-adjust:exact;
        }
      }
    }

    .subject-desc {
      font-size: 16px;
      line-height: 22px;
      color: #333;
      margin-top: 20px;
    }

    .img-list {
      .image-text {
        width: 100%;
        padding: 20px;
        background: #f7fbfe;
        box-sizing: border-box;
        margin-top: 30px;
        display: flex;
        align-items: center;
        -webkit-print-color-adjust:exact;
        -moz-print-color-adjust:exact;
        -ms-print-color-adjust:exact;
        print-color-adjust:exact;
      }

      .img-desc {
        font-size: 16px;
        color: #333;
        line-height: 24px;
      }

      .table-box {
        width: 100%;

        .table-title {
          width: 100%;
          height: 60px;
          line-height: 60px;
          text-align: center;
          background-color: #2b51c6;
          color: #fff;
          font-size: 18px;
          font-weight: 700;
          -webkit-print-color-adjust:exact;
          -moz-print-color-adjust:exact;
          -ms-print-color-adjust:exact;
          print-color-adjust:exact;
        }

        .table-row {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: #fbfdff;
          padding: 12px 0;
          -webkit-print-color-adjust:exact;
          -moz-print-color-adjust:exact;
          -ms-print-color-adjust:exact;
          print-color-adjust:exact;

          .table-item {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-size: 16px;
            text-align: center;
            font-weight: 700;
          }
        }
      }
    }
  }
}

/deep/ .ant-drawer-body {
  padding: 0;
}

/deep/ .ant-modal {
  width: fit-content !important;
}
</style>
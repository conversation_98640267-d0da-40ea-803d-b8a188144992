<template>
  <a-modal
    title='继续答题'
    :width='800'
    :visible='visible'
    :confirmLoading='confirmLoading'
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <a-form :form='form'>
        <a-form-item
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
          label='是否推送终端'>
          <a-select style="width: 100%" placeholder="请选择" v-decorator="['type', {rules: [{ required: true, message: '请选择' }]}]" @change="typeChange">
            <a-select-option value="no">否</a-select-option>
            <a-select-option value="yes">是</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
          label='选择推送终端' v-if="model.type == 'yes'">
          <a-select
            style="width: 100%"
            placeholder="请选择推送终端"
            v-decorator="['userIds', validatorRules.userIds]">
            <a-select-option v-for="user in users" :key="user.id">
              {{ user.realname }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'
import { queryUserByCurrentAccount } from '@/api/api'
import { mapGetters } from 'vuex'
export default {
  name: 'DxContinueUnswering',
  data() {
    return {
      title: '操作',
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      measureIds:'',
      doctorId:'',
      psUserId:'',
      users: [],
      validatorRules: {
        userIds: { rules: [{ required: true, message: '请选择推送终端!' }] }
      },
      url:{
        pushTerminalByUsers: '/psychology/psUser/pushTerminalByUsers',
      },
      confirmLoading: false,
      model:{},
      form: this.$form.createForm(this),
    }
  },
  created() {
  },
  methods: {
    ...mapGetters(['nickname', 'userInfo']),
    loadTerminalList() {
      queryUserByCurrentAccount({}).then((res) => {
        if (res.success) {
          this.users = res.result
        } else {
          // console.log(res.message)
        }
      })
    },
    add(psUserId,doctorId,measureIds) {
      this.visible = true;
      this.psUserId = psUserId;
      this.doctorId = doctorId;
      this.measureIds = measureIds;
    },
    close() {
      this.reSetData();
      this.visible = false
    },
    typeChange(value){
      this.model.type = value;
      if (value == 'yes') {
        //加载终端列表
        this.loadTerminalList()
      }
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign({}, values);
          formData.psUserId = this.psUserId;
          formData.measureIds = this.measureIds;
          if(this.model.type == 'no'){
            formData.userIds = this.doctorId;
          }
          getAction(that.url.pushTerminalByUsers, formData).then((res) => {
            if (res.success) {
              this.$notification['success']({
                message: '添加成功',
                duration: 3,
                description: '已在终端推送答题信息'
              })
              this.reSetData()
              this.confirmLoading = false
              this.close()
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
            that.close()
          })
        }
      })
    },
    reSetData() {
      this.model = {};
      this.form = this.$form.createForm(this);
    },
    handleCancel() {
      this.close()
    },
  }
}
</script>

<style lang='less' scoped>

</style>
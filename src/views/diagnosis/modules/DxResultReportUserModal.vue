<template>
  <a-modal
    centered
    title="选择用户"
    :width="1000"
    :visible="table_visible"
    @ok="userModalOK"
    @cancel="table_visible=false"
    cancelText="关闭">
    <a-row :gutter="18">
      <a-col :span="24">
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="24">
              <a-col :span="10">
                <a-form-item label="姓名">
                  <j-input placeholder="请输入姓名" v-model="queryParam.realname"></j-input>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                  <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <a-table
          size="small"
          bordered
          rowKey="id"
          :columns="columns1"
          :dataSource="dataSource1"
          :pagination="ipagination"
          :loading="loading"
          :scroll="{ y: 240 }"
          :rowSelection="{
            type: 'radio',  // 设置为单选模式
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange  // 单选只需要onChange事件即可
          }"
          @change="handleTableChange">
          <template slot="avatarslot" slot-scope="text, record, index">
            <div class="anty-img-wrap">
              <a-avatar shape="square" :src="getAvatarView(record.avatar)" icon="user"/>
            </div>
          </template>
        </a-table>
      </a-col>
    </a-row>
  </a-modal>
</template>

<script>
  import { filterObj } from '@/utils/util'
  import { getAction } from '@/api/manage'
  import JInput from '@/components/jeecg/JInput'

  export default {
    name: 'DxResultReportUserModal',
    components: {
      JInput,
    },
    data() {
      return {
        url: {
          imgerver: window._CONFIG['staticDomainURL'],
          userList: '/sys/user/list'
        },
        table_visible:false,
        // 查询条件
        queryParam: {},
        // 表头
        columns1: [
          {
            title: '姓名',
            align: 'center',
            width: 113,
            dataIndex: 'realname'
          },
          {
            title: '电话',
            align: 'center',
            width: 100,
            dataIndex: 'phone'
          },
          {
            title: '头像',
            align: 'center',
            width: 100,
            dataIndex: 'avatar',
            scopedSlots: { customRender: 'avatarslot' }
          }
        ],
        //数据集
        dataSource1: [],
        // 分页参数
        ipagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
        selectedRowKeys: [],
        loading: false,
      }
    },
    created() {
      this.loadData()
    },
    methods: {
      getAvatarView: function(avatar) {
        return this.url.imgerver + '/' + avatar
      },
      searchQuery() {
        this.loadData(1)
      },
      searchReset() {
        this.queryParam = {}
        this.loadData(1)
      },
      handleTableChange(pagination, filters, sorter) {
        //分页、排序、筛选变化时触发
        //TODO 筛选
        if (Object.keys(sorter).length > 0) {
          this.isorter.column = sorter.field
          this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
        }
        this.ipagination = pagination
        this.loadData()
      },
      loadData(arg) {
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1
        }
        var params = this.getQueryParams()//查询条件
        getAction(this.url.userList, params).then((res) => {
          if (res.success) {
            this.dataSource1 = res.result.records
            this.ipagination.total = res.result.total
          }
        })
      },
      getQueryParams() {
        var param = Object.assign({}, this.queryParam, this.isorter)
        param.pageNo = this.ipagination.current
        param.pageSize = this.ipagination.pageSize
        return filterObj(param)
      },
      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys;
        // 保存选中的行数据
        this.selectedRow = selectedRows.length > 0 ? selectedRows[0] : null;
      },
      userModalOK(){
        this.$emit('selectFinished', this.selectedRow)
          this.table_visible = false;
        },
      add() {
        this.table_visible = true
      },
    }
  }
</script>
<style lang="less" scoped>
</style>
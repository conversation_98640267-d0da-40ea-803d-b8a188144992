<template>
  <a-modal
    :title="title"
    :width="1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="权限类型">
          <a-select placeholder="请选择权限类型" v-decorator="['permissionType', {}]" @change="permissionTypeChange">
            <a-select-option value="1">管理员</a-select-option>
            <a-select-option value="2">部门</a-select-option>
            <a-select-option value="3">个人</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="部门" v-if="model.permissionType == '2' && departTree.length>0">
          <a-tree-select
            v-decorator="['sysOrgCode',{}]"
            style="width: 100%"
            :tree-data="departTree"
            placeholder="请选择部门"
            :replace-fields="{ value: 'orgCode'}"
            allow-clear
            showSearch
            tree-node-filter-prop="title"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
          />
        </a-form-item>

        <a-form-item v-if="model.permissionType == '3'" style="display:none;">
          <a-input v-decorator="['userId', {}]"></a-input>
        </a-form-item>

        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="个人" v-if="model.permissionType == '3'">
          <div class="flex_box">
            <a-input  v-decorator="['userName', { options: { readonly: true } }]"></a-input>
            <a-button style="margin-left:10px;" type="primary" @click="openUser" icon="search">选择用户</a-button>
          </div>
        </a-form-item>

        

        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="选择量表">
          <a-tree-select
            showSearch
            style="width:100%"
            :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
            :treeData="measureTree"
            v-model="model.measureId"
            treeNodeFilterProp="title"
            placeholder="请选择量表">
          </a-tree-select>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="分数范围，前数">
          <a-input-number v-decorator="[ 'scoreRangeBefor', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="分数范围，后数">
          <a-input-number v-decorator="[ 'scoreRangeAfter', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="报告内容(文本)">
          <a-input placeholder="请输入报告内容" v-decorator="['contentStr', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          style="min-height: 360px;">
          <span slot="label">
            报告头部&nbsp;
            <a-tooltip title="模板中的动态数据项目使用 ${} 格式呈现，在诊断报告中会自动替换成具体的报告数据，例如${userName}会被替换为'张三'">
              <a-icon type="question-circle" />
            </a-tooltip>
          </span>
          <j-editor :j-height="360" v-model="model.contentHead" ref="contentHead"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          style="min-height: 600px;">
          <span slot="label">
            报告内容&nbsp;
            <a-tooltip title="模板中的动态数据项目使用 ${} 格式呈现，在诊断报告中会自动替换成具体的报告数据，例如${userName}会被替换为'张三'">
              <a-icon type="question-circle" />
            </a-tooltip>
          </span>
          <j-editor :j-height="600" v-model="model.contentBody" ref="contentBody"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          style="min-height: 200px;">
          <span slot="label">
            报告尾部&nbsp;
            <a-tooltip title="模板中的动态数据项目使用 ${} 格式呈现，在诊断报告中会自动替换成具体的报告数据，例如${userName}会被替换为'张三'">
              <a-icon type="question-circle" />
            </a-tooltip>
          </span>
          <j-editor :j-height="200" v-model="model.contentTail" ref="contentTail"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="报告内容顺序">
          <a-input placeholder="请输入报告内容顺序" v-decorator="['sort', {}]"/>
        </a-form-item>
      </a-form>
    </a-spin>


    <!-- 选择人员 -->
    <dxResultReportUser-modal ref="choseUser" @selectFinished="modalUserOk"></dxResultReportUser-modal>
  </a-modal>
</template>

<script>

  import { getAction, httpAction } from '@/api/manage'
  import { queryDepartTreeList,getUserList } from '@/api/api'
  import pick from 'lodash.pick'
  import JEditor from '@/components/jeecg/JEditor'
  import DxResultReportUserModal from './DxResultReportUserModal'

  export default {
    name: 'DxResultReportModal',
    components: {
      JEditor,
      DxResultReportUserModal
    },
    data() {
      return {
        title: '操作',
        visible: false,
        model: {},
        departTree:[],
        measureTree: [],
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          delFlag: { rules: [{ required: true, message: '请输入删除状态（0，正常，1已删除）!' }] }
        },
        url: {
          add: '/diagnosis/dxResultReport/add',
          edit: '/diagnosis/dxResultReport/edit',
          queryById: '/diagnosis/dxResultReport/queryById',
          treeList: '/psychology/psCategory/queryTreeSelectList',
        },
      }
    },
    created() {
      this.loadTreeData()
    },
    methods: {
      loadTreeData() {
        getAction(this.url.treeList, null).then((res) => {
          if (res.success) {
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              this.measureTree.push(temp)
            }
            this.loading = false
          }
        })
      },
      loadResultReportData(id) {
        getAction(this.url.queryById, { id: id }).then((res) => {
          if (res.success) {
            if (res.result.measureId) {
              res.result.measureId = res.result.measureId + ',' + res.result.categoryId
            }
            this.model = res.result;
          }
        })
      },
      //权限类型选择事件
      permissionTypeChange(value){
        this.model.permissionType = value;
        //获取部门数据
        if(value == 2){
          queryDepartTreeList().then((res) => {
            if (res.success) {
              this.departTree = res.result;
            }
          })
        }
      },
      modalUserOk(data){
        this.form.setFieldsValue({
          userId: data.id,
          userName:data.realname
        });
      },
      openUser(){
        this.$refs.choseUser.add()
      },
      add() {
        this.edit({})
      },
      edit(record) {
        console.log(record);
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'permissionType','scoreRangeBefor', 'scoreRangeAfter', 'contentStr', 'sort'))
          if(this.model.permissionType == '2'){
            queryDepartTreeList().then((res) => {
              if (res.success) {
                this.departTree = res.result;
                this.$nextTick(() => {
                  this.form.setFieldsValue({sysOrgCode:this.model.sysOrgCode})
                });
              }
            })
          }
          if(this.model.permissionType == '3'){
            this.$nextTick(() => {
              this.form.setFieldsValue({
                userId:this.model.userId,
                userName:this.model.userName
              })
            });
          }
          //解决空值问题
          if(!this.model.contentHead){
            this.model.contentHead = '';
          }
          if(!this.model.contentBody){
            this.model.contentBody = '';
          }
          if(!this.model.contentTail){
            this.model.contentTail = '';
          }
        })
        if (this.model.id) {
          this.loadResultReportData(this.model.id)
        }
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            if (formData.measureId) {
              formData.measureId = formData.measureId.split(',')[0]
            }
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>

<style scoped>
.flex_box{
  display: flex;
  align-items: center;
}
</style>
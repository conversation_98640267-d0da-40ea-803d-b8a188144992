<template>
  <div>
    <a-spin :spinning="downLoading" tip="下载中...">
      <a-card :bordered="false">
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="24">
              <a-col :span="6">
                <a-form-item label="测试者编号">
                  <a-input placeholder="请输入测试者编号" v-model="queryParam.userNo"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="测试者姓名">
                  <a-input placeholder="请输入测试者姓名" v-model="queryParam.userName"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="测评时间">
                  <a-range-picker
                    style="width: 100%"
                    v-model="queryParam.createTimeRange"
                    format="YYYY-MM-DD"
                    :placeholder="['开始时间', '结束时间']"
                    @change="onDateChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="8">
                <a-form-item label="测评状态">
                  <j-dict-select-tag v-model="queryParam.status" placeholder="请输入测评状态" dictCode="answer_status"/>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="选择量表">
                  <a-tree-select
                    showSearch
                    style="width:100%"
                    :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
                    :treeData="measureTree"
                    v-model="queryParam.measureId"
                    treeNodeFilterProp="title"
                    placeholder="请选择量表">
                  </a-tree-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="测评分类">
                  <a-select
                    style="width: 100%"
                    placeholder="请选择测评分类"
                    v-model="queryParam.assessType">
                    <a-select-option value="scale">
                      量表评估
                    </a-select-option>
                    <a-select-option value="em">
                      眼动筛查
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <template v-if="toggleSearchStatus">
                <a-col :span="6">
                  <a-form-item label="测试者编号前缀">
                    <a-input placeholder="请输入测试者编号前缀" v-model="queryParam.prefixEncode"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="测试者编号团测前缀">
                    <a-input placeholder="请输入测试者编号团测前缀" v-model="queryParam.userNoLeft"></a-input>
                  </a-form-item>
                </a-col>
                <a-col v-has="'result:form:doctorName'" :span="6">
                  <a-form-item label="操作人">
                    <a-input placeholder="请输入操作人" v-model="queryParam.doctorName"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :md="6" :sm="8">
                  <a-form-item label="预警程度">
                    <j-dict-select-tag v-model="queryParam.degree" placeholder="请选择预警程度" dictCode="result_degree"/>
                  </a-form-item>
                </a-col>
                <a-col v-has="'resultList:departmentName'" :md='6' :sm='8'>
                  <a-form-item label='科室'>
                    <a-input placeholder='请输入科室' v-model='queryParam.departmentName'></a-input>
                  </a-form-item>
                </a-col>
              </template>
              <a-col :span="6">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
              <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
              </a-col>
            </a-row>
          </a-form>
        </div>

        <!-- 操作按钮区域 -->
        <div class="table-operator" v-if="!hasRoleFZD">
          <a-button type="primary" icon="printer" @click="batchPrint">批量打印</a-button>
          <a-button type="primary" icon="arrow-down" @click="batchExportDoc('批量下载报告')">批量下载报告</a-button>
          <!--      <a-button type="primary" icon="download" @click="handleExportDoc">查看报告</a-button>-->
          <a-button v-has="'resultList:export'" v-if="!hasRole3" type="primary" icon="arrow-up" @click="handleExportXls('测评结果')">测评数据导出</a-button>
          <a-button v-has="'resultList:export'" v-if="!hasRole3" type="primary" icon="arrow-up" @click="exportExcelFactorList()">测评因子导出</a-button>
          <a-button v-has="'resultList:export'" v-if="!hasRole3" type="primary" icon="arrow-up" @click="exportExcelReportDetail()">答题详情导出</a-button>
          <a-button v-has="'eyeParam:export'" v-if="!hasRole3" type="primary" icon="arrow-up" @click="exportEyeParam()">眼动参数导出</a-button>
          <a-dropdown v-has="'resultList:delete'" v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="batchDel">
                <a-icon type="delete"/>
                删除
              </a-menu-item>
            </a-menu>
            <a-button style="margin-left: 8px"> 批量操作
              <a-icon type="down"/>
            </a-button>
          </a-dropdown>
        </div>
        <!-- table区域-begin -->
        <div>
          <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
            <i class="anticon anticon-info-circle ant-alert-icon"></i>
            <span>已选择</span>
            <a style="font-weight: 600">
              {{ selectedRowKeys.length }}
            </a>
            <span>项</span>
            <a style="margin-left: 24px" @click="onClearSelected">清空</a>
          </div>

          <a-table
            ref="table"
            size="middle"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
            @change="handleTableChange">
            <span slot="degree" slot-scope="text">
              <a-badge :color="text | statusTypeFilter" :text="text | statusFilter"/>
            </span>
            <span v-if="isShowAuth('resultList:actionParam')" slot="action" slot-scope="text, record">
              <a @click="handlePrintForm(record)">内容预览</a>
              <a-divider v-if="isAssessType(record)" type="vertical"/>
              <a v-if="record.status == '2' && isAssessType(record)" @click="handleExportDoc(record)">报告下载</a>
              <a v-if="record.status != '2' && isAssessType(record)" @click="continueQuestions(record)">继续答题</a>
              <template v-if="!hasRole3 && isAssessType(record)">
                <a-divider type="vertical"/>
                <a-dropdown>
                  <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
                  <a-menu slot="overlay">
                    <a-menu-item v-has="'resultList:detail'">
                      <a @click="handleView(record)">查看</a>
                    </a-menu-item>
                    <a-menu-item v-has="'resultList:edit'">
                      <a @click="handleEdit(record)">编辑</a>
                    </a-menu-item>
                    <a-menu-item v-if="record.signImgPath">
                      <a @click="downUserSign(record)">下载签名</a>
                    </a-menu-item>
                    <a-menu-item v-has="'resultList:export'">
                      <a @click="exportExcelOption(record)">导出答题详情</a>
                    </a-menu-item>
                    <a-menu-item v-has="'resultList:export'">
                      <a @click="exportExcelFactor(record)">导出因子</a>
                    </a-menu-item>
                  </a-menu>
                </a-dropdown>
              </template>
            </span>
            <span v-else slot="action" slot-scope="text, record">
              <a v-if="record.status != '2'" @click="continueQuestions(record)">继续答题</a>
              <a v-if="record.status == '2'">----</a>
            </span>
          </a-table>
        </div>
        <!-- table区域-end -->
        <!-- 表单区域 -->
        <dxResult-modal ref="modalForm" @ok="modalFormOk"/>
      </a-card>
      <!-- 打印表单页 -->
      <report-common-form ref="ReportForm"></report-common-form>
      <!-- 眼动筛查报告 -->
      <dx-em-report-model ref="emReportFrom"></dx-em-report-model>
    </a-spin>
  </div>
</template>
<script>
import Vue from 'vue'
import { USER_ROLE } from '@/store/mutation-types'
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import DxResultModal from './modules/DxResultModal'
import DxEmReportModel from './modules/DxEmReportModel'
import {deleteAction, downFile, getAction} from '@/api/manage'
import ReportCommonForm from '../dashboard/ReportCommonForm'
import {colAuthFilter, disabledAuthFilter, globalShowedAuth} from '@/utils/authFilter'
import JInput from '@comp/jeecg/JInput.vue'

const degreeMap = {
  0: {
    color: '#D9D9D9',
    text: '未答完'
  },
  1: {
    color: 'green',
    text: '正常'
  },
  2: {
    color: 'yellow',
    text: '轻度'
  },
  3: {
    color: 'red',
    text: '中度'
  },
  4: {
    color: 'purple',
    text: '重度'
  },
  5: {
    color: '#D9D9D9',
    text: '未知'
  }
}

export default {
  name: 'DxResultList',
  mixins: [JeecgListMixin],
  components: {
    JInput,
    DxResultModal,
    ReportCommonForm,
    DxEmReportModel
  },
  data() {
    return {
      factorCanClick:true,
      description: '测评结果管理页面',
      measureTree: [],
      measureList: [],
      downLoading: false,
      /* 排序参数 */
      isorter: {
        column: 'updateTime',
        order: 'desc'
      },
      // 表头
      columns: [
        {
          title: '编号',
          align: 'center',
          dataIndex: 'userNo',
          sorter: true
        },
        {
          title: '主试',
          align: 'center',
          dataIndex: 'userName'
        },
        {
          title: '操作人',
          align: 'center',
          dataIndex: 'doctorName'
        },
        {
          title: '科室',
          align: 'center',
          dataIndex: 'departmentName'
        },
        {
          title: '测评分类',
          align: 'center',
          dataIndex: 'assessType',
          customRender: function(text) {
            return text == 'scale' ? '量表评估' : text == 'em' ? '眼动筛查' : ''
          }
        },
        {
          title: '量表',
          align: 'center',
          dataIndex: 'measureName'
        },
        {
          title: '总粗分',
          align: 'center',
          dataIndex: 'totalPoints'
        },
        {
          title: '测评用时',
          align: 'center',
          dataIndex: 'timeStr'
        },
        {
          title: '答题状态',
          align: 'center',
          dataIndex: 'status_dictText'
        },
        {
          title: '预警结果',
          align: 'center',
          dataIndex: 'degree',
          scopedSlots: {customRender: 'degree'}
        },
        {
          title: '修改时间',
          align: 'center',
          dataIndex: 'updateTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: {customRender: 'action'}
        }
      ],
      // 请求参数
      url: {
        list: '/diagnosis/dxResult/list',
        delete: '/diagnosis/dxResult/delete',
        deleteBatch: '/diagnosis/dxResult/deleteBatch',
        exportXlsUrl: 'diagnosis/dxResult/exportXls2',
        batchExportDetailXls: 'diagnosis/dxResult/batchExportDetailXls',
        batchExportDoc: 'diagnosis/dxResult/batchExportDoc',
        importExcelUrl: 'diagnosis/dxResult/importExcel',
        treeList: '/psychology/psCategory/queryTreeSelectList',
        exportDocUrl: 'diagnosis/dxResult/exportSingleDoc',
        goContinueQuestionPage: '/psychology/psUser/goContinueQuestionPage',
        exportOptionXls: 'diagnosis/dxResult/exportOptionXls',
        exportFactorXls: 'diagnosis/dxResult/exportFactor',
        exportVrReport: "/vr/report/exportVrReport"
      },
      hasRole3: Vue.ls.get(USER_ROLE, []).includes('myyyts'),
      hasRoleFZD: Vue.ls.get(USER_ROLE, []).includes('fzddt'),
    }
  },
  filters: {
    statusFilter(type) {
      return degreeMap[type].text
    },
    statusTypeFilter(type) {
      return degreeMap[type].color
    }
  },
  created() {
    if (!this.isShowAuth("resultList:timeStr")) {
      this.columns = this.columns.filter(item => item.dataIndex !== 'timeStr')
    }
    if (!this.isShowAuth("resultList:updateTime")) {
      this.columns = this.columns.filter(item => item.dataIndex !== 'updateTime')
    }
    if (!this.isShowAuth("resultList:departmentName")) {
      this.columns = this.columns.filter(item => item.dataIndex !== 'departmentName')
    }
    this.loadTreeData()
    this.loadData()
    //权限控制
    this.initColumns()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    handleExportXls(fileName, method='get'){
      if(!fileName || typeof fileName != "string"){
        fileName = "导出文件"
      }
      let param = this.getQueryParams();//查询条件
      delete param.createTimeRange; // 时间参数不传递后台
      delete param.pageNo;
      delete param.pageSize;
      if (param.measureId) {
        param.measureId = param.measureId.split(',')[0]
      }
      downFile(this.url.exportXlsUrl,param, method).then((data)=>{
        if (!data) {
          this.$message.warning("文件下载失败")
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), fileName+'.xls')
        }else{
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName+'.xls')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link); //下载完成移除元素
          window.URL.revokeObjectURL(url); //释放掉blob对象
        }
      })
    },
    isDisabledAuth(code) {
      return disabledAuthFilter(code)
    },
    isShowAuth(code) {
      return globalShowedAuth(code)
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams()//查询条件
      delete params.createTimeRange // 时间参数不传递后台
      if (params.measureId) {
        params.measureId = params.measureId.split(',')[0]
      }
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          let result = res.result.records
          this.ipagination.total = res.result.total
          if (this.hasRoleFZD) {
            this.dataSource = []
            for (let i = 0; i < result.length; i++) {
              if (result[i].userName) {
                result[i].userName = this.hideInsurantName(result[i].userName);
              }
              this.dataSource.push(result[i])
            }
          } else {
            this.dataSource = result
          }
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    loadTreeData() {
      getAction(this.url.treeList, null).then((res) => {
        if (res.success) {
          this.measureList = res.result
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            this.measureTree.push(temp)
          }
          this.loading = false
        }
      })
    },
    /*      handleExportDoc() {
            if (this.selectedRowKeys.length <= 0) {
              this.$message.warning('请选择一条记录！')
              return
            } else {
              var ids = ''
              for (var a = 0; a < this.selectedRowKeys.length; a++) {
                ids += this.selectedRowKeys[a] + ','
              }
              let url = `${window._CONFIG['domianURL']}/${this.url.exportDocUrl}?ids=` + ids
              console.log('导出报告地址：' + url)
              window.location.href = url
            }
          },*/
    initDictConfig() {
    },
    handleAdd: function () {
      this.$refs.modalForm.add(this.$route.params.id)
      this.$refs.modalForm.title = '新增'
    },
    handleExportDoc(record) {
      if (record.status != '2') {
        this.$message.error('未答完题目不能查看报告！')
        return
      }
      this.downLoading = true
      var fileName = record.userName + '_' + record.measureName + '.pdf'
      let param = {...this.queryParam}
      delete param.createTimeRange // 时间参数不传递后台
      param.ids = record.id
      downFile(this.url.exportDocUrl, param).then((data) => {
        if (!data) {
          this.$message.warning('文件下载失败')
          this.downLoading = false
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), fileName)
          this.downLoading = false
        } else {
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(url) //释放掉blob对象
          this.downLoading = false
        }
      })
    },
    exportExcelOption(record) {
      if (record.status != '2') {
        this.$message.error('未答完题目不能导出答题数据！')
        return
      }
      let url = `${window._CONFIG['domianURL']}/${this.url.exportOptionXls}?id=` + record.id
      window.location.href = url
    },
    exportExcelFactor(record) {
      if (record.status != '2') {
        this.$message.error('未答完题目不能导出因子分！')
        return
      }
      let fileName = record.userName +'_'+record.measureName+'_因子'
      if (this.factorCanClick){
        this.factorCanClick = false
        this.$message.success("正在导出，请稍后")
        downFile(this.url.exportFactorXls,{id:record.id}, 'get').then((data)=>{
          if (!data) {
            this.$message.warning("文件下载失败")
            return
          }
          if (typeof window.navigator.msSaveBlob !== 'undefined') {
            window.navigator.msSaveBlob(new Blob([data]), fileName+'.xls')
          }else{
            let url = window.URL.createObjectURL(new Blob([data]))
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = url
            link.setAttribute('download', fileName+'.xls')
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link); //下载完成移除元素
            window.URL.revokeObjectURL(url); //释放掉blob对象
          }
        }).finally(() => {
          this.factorCanClick = true
        })
      }
      // let url = `${window._CONFIG['domianURL']}/${this.url.exportFactorXls}?id=` + record.id
      // window.location.href = url
    },
    exportExcelFactorList() {
      if(!this.queryParam.hasOwnProperty("measureId")){
        this.$message.error('必须选择一个量表！')
        return
      }
      let param = this.getQueryParams();//查询条件
      let fileName = '导出';
      this.measureList.forEach(item => {
        item.children.forEach(child=>{
          if(child.value == param.measureId){
            fileName = child.title
          }
        })
      })
      fileName+= '_因子'
      delete param.createTimeRange; // 时间参数不传递后台
      delete param.pageNo;
      delete param.pageSize;
      param.measureId = param.measureId.split(',')[0]
      if (this.factorCanClick) {
        this.factorCanClick = false
        this.$message.success("正在导出，请稍后")
        downFile(this.url.exportFactorXls, param, 'get').then((data) => {
          if (!data) {
            this.$message.warning("文件下载失败")
            return
          }
          if (typeof window.navigator.msSaveBlob !== 'undefined') {
            window.navigator.msSaveBlob(new Blob([data]), fileName + '.xls')
          } else {
            let url = window.URL.createObjectURL(new Blob([data]))
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = url
            link.setAttribute('download', fileName + '.xls')
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link); //下载完成移除元素
            window.URL.revokeObjectURL(url); //释放掉blob对象
          }
        }).finally(() => {
          this.factorCanClick = true
        })
      }
      // let url = `${window._CONFIG['domianURL']}/${this.url.exportFactorXls}?` + paramStr
      // window.location.href = url
    },
    exportExcelReportDetail(fileName) {
      if(!fileName || typeof fileName != "string"){
        fileName = "导出文件"
      }
      let param = this.getQueryParams();//查询条件
      delete param.createTimeRange; // 时间参数不传递后台
      delete param.pageNo;
      delete param.pageSize;
      if (param.measureId) {
        param.measureId = param.measureId.split(',')[0]
      }
      downFile(this.url.batchExportDetailXls,param, 'get').then((data)=>{
        if (!data) {
          this.$message.warning("文件下载失败")
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), fileName+'.xls')
        }else{
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName+'.xls')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link); //下载完成移除元素
          window.URL.revokeObjectURL(url); //释放掉blob对象
        }
      })
    },
    //眼动参数导出
    exportEyeParam(record) {
      let param = this.getQueryParams();//查询条件
      if (param.assessType != "em") {
        this.$message.error('请选择眼动筛查进行导出！')
        return
      }
      let fileName = '导出眼动参数';
      downFile(this.url.exportVrReport, param).then((data) => {
        if (!data) {
          this.$message.warning('文件下载失败')
          this.downLoading = false
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), fileName + '.xls')
          this.downLoading = false
        } else {
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName + ".xls")
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(url) //释放掉blob对象
          this.downLoading = false
        }
      })
    },
    continueQuestions(record) {
      let that = this
      let httpurl = that.url.goContinueQuestionPage
      let formData = {}
      formData.resultId = record.id
      getAction(httpurl, formData).then((res) => {
        if (res.success) {
          this.$notification['success']({
            message: '添加成功',
            duration: 3,
            description: '已在选择的终端推送答题信息'
          })
        } else {
          that.$message.warning(res.message)
        }
      }).finally(() => {
      })
    },
    handlePrintForm(record) {
      if (record.status != '2') {
        this.$message.error('未答完题目不能预览！')
        return
      }

      // 眼动筛查报告
      if (record.assessType === 'em') {
        this.$refs.emReportFrom.edit(record)
        this.$refs.emReportFrom.title = '内容预览'
      } else {
        this.$refs.ReportForm.edit(record)
        this.$refs.ReportForm.title = '内容预览'
      }
    },
    batchPrint: function () {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择至少一条记录！')
        return
      } else if (this.selectedRowKeys.length > 30) {
        this.$message.warning('请不要选择超过三十条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        this.$refs.ReportForm.batchPrint(ids)
        this.$refs.ReportForm.title = '批量打印'
      }
    },
    batchExportDoc(fileName) {
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      if (this.selectedRowKeys.length > 300) {
        this.$message.warning('最大仅可导出300条')
        return
      }
      this.downLoading = true
      if (!fileName || typeof fileName != 'string') {
        fileName = '导出文件'
      }
      let param = {...this.queryParam}
      delete param.createTimeRange // 时间参数不传递后台
      if (this.selectedRowKeys && this.selectedRowKeys.length > 0) {
        param['ids'] = this.selectedRowKeys.join(',')
      }
      downFile(this.url.batchExportDoc, param, 'post').then((data) => {
        if (!data) {
          this.$message.warning('文件下载失败')
          this.downLoading = false
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), fileName + '.zip')
          this.downLoading = false
        } else {
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName + '.zip')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(url) //释放掉blob对象
          this.downLoading = false
        }
      })
    },
    handleView: function (record) {
      this.$refs.modalForm.view(record)
      this.$refs.modalForm.title = '查看'
      this.$refs.modalForm.disableSubmit = true
    },
    initColumns() {
      //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
      this.columns = colAuthFilter(this.columns, 'resultList:')

    },
    onDateChange: function (value, dateString) {
      this.queryParam.startTime = dateString[0]
      this.queryParam.endTime = dateString[1]
    },
    downUserSign(record) {
      let fileName = '测试者签名'
      let url = record.signImgPath
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', fileName + '.png')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link) //下载完成移除元素
      window.URL.revokeObjectURL(url) //释放掉blob对象
    },
    batchDel: function () {
      if (!this.url.deleteBatch) {
        this.$message.error("请设置url.deleteBatch属性!")
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！');
        return;
      } else {
        var ids = "";
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ",";
        }
        var that = this;
        this.$confirm({
          title: "确认删除",
          content: "删除后不可恢复，是否确认删除?",
          okType: 'danger',
          onOk: function () {
            deleteAction(that.url.deleteBatch, {ids: ids}).then((res) => {
              if (res.success) {
                that.$message.success(res.message);
                that.loadData();
                that.onClearSelected();
              } else {
                that.$message.warning(res.message);
              }
            });
          }
        });
      }
    },
    hideInsurantName (val) {
      if (!val || val === '') return ''
      let name = ''
      if (val.length === 2) {
        name = val.substring(0, 1) + '*' // 截取name 字符串截取第一个字符，
      } else if (val.length === 3) {
        name = val.substring(0, 1) + '*' + val.substring(2, 3) // 截取第一个和第三个字符
      } else if (val.length === 4) {
        name = val.substring(0, 2) + '*' + '*' // 4个字隐藏后面两个
      } else if (val.length > 4) {
        name = val.substring(0, 1) // 5个字只显示第一个字
        for (let i = 0; i < val.length - 1; i++) {
          name = name + '*'
        }
      }
      return name
    },
    isAssessType(record) {
      if (!record.assessType || record.assessType === 'scale') {
        return true
      }
      return false
    }
  }
}
</script>
<style lang="less" scoped>
/** Button按钮间距 */
.ant-btn {
  margin-left: 3px
}

.ant-card-body .table-operator {
  margin-bottom: 18px;
}

.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

.anty-row-operator button {
  margin: 0 5px
}

.ant-btn-danger {
  background-color: #ffffff
}

.ant-modal-cust-warp {
  height: 100%
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden
}
</style>
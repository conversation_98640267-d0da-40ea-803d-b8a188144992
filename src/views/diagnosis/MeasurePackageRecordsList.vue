<template>
  <a-card :bordered='false'>
    <!-- 查询区域 -->
    <div class='table-page-search-wrapper'>
      <a-form layout='inline'>
        <a-row :gutter='24'>
          <a-col :md='6' :sm='8'>
            <a-form-item label='医生姓名'>
              <a-input placeholder='请输入医生姓名' v-model='queryParam.doctorName'></a-input>
            </a-form-item>
          </a-col>
          <a-col :md='6' :sm='8'>
            <a-form-item label='套餐名称'>
              <a-input v-model='queryParam.name' placeholder='请输入量表套餐名称'></a-input>
            </a-form-item>
          </a-col>
          <a-col :md='6' :sm='8'>
            <span style='float: left;overflow: hidden;' class='table-page-search-submitButtons'>
              <a-button type='primary' @click='searchQuery' icon='search'>查询</a-button>
              <a-button type='primary' @click='searchReset' icon='reload' style='margin-left: 8px'>重置</a-button>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>
    <!-- 操作按钮区域 -->
    <div class='table-operator'>
      <a-button @click='handleAdd' type='primary' icon='plus'>新增</a-button>
      <a-dropdown v-if='selectedRowKeys.length > 0'>
        <a-menu slot='overlay'>
          <a-menu-item key='1' @click='batchDel'>
            <a-icon type='delete' />
            删除
          </a-menu-item>
        </a-menu>
        <a-button style='margin-left: 8px'> 批量操作
          <a-icon type='down' />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class='ant-alert ant-alert-info' style='margin-bottom: 16px;'>
        <i class='anticon anticon-info-circle ant-alert-icon'></i> 已选择 <a style='font-weight: 600'>{{
          selectedRowKeys.length }}</a>项
        <a style='margin-left: 24px' @click='onClearSelected'>清空</a>
      </div>

      <a-table
        ref='table'
        size='middle'
        bordered
        rowKey='id'
        :columns='columns'
        :dataSource='dataSource'
        :pagination='ipagination'
        :loading='loading'
        :rowSelection='{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}'
        @change='handleTableChange'>

        <span slot='action' slot-scope='text, record'>
          <a @click='handleEdit(record)'>编辑</a>
          <a-divider type="vertical"/>
          <a v-if="hasTTCL_SCLBTCLJ" @click='handleSCLJ(record)'>复制链接</a>
          <a-divider type="vertical" v-if="hasTTCL_SCLBTCLJ"/>
          <a v-if="hasTTCL_SCLBTCLJ" @click='handleCompletionUser(record)'>核查</a>
          <a-divider type="vertical" v-if="hasTTCL_SCLBTCLJ"/>
          <a @click='handleDel(record)'>删除</a>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <wxAuthorizationRecords-modal ref='modalForm' @ok='modalFormOk'></wxAuthorizationRecords-modal>
    <DxCompletionUserModel ref='completionUser'></DxCompletionUserModel>
  </a-card>
</template>

<script>
import WxAuthorizationRecordsModal from './modules/MeasurePackageRecordsModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JInput from '@/components/jeecg/JInput'
import Vue from 'vue'
import { USER_ROLE, USER_NAME } from '@/store/mutation-types'
import DxCompletionUserModel from './modules/DxCompletionUserModel'

export default {
  name: 'MeasurePackageRecordsList',
  mixins: [JeecgListMixin],
  components: {
    JInput,
    WxAuthorizationRecordsModal,
    DxCompletionUserModel
  },
  data() {
    return {
      description: '量表套餐表管理页面',
      // 表头
      columns: [
        {
          title: '医生名称',
          align: 'center',
          dataIndex: 'doctorName'
        },
        {
          title: '名称',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '套餐量表',
          align: 'center',
          dataIndex: 'measureNames',
          width: 600,
          ellipsis: true
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/wx/wxAuthorizationRecords/list2?isTop=1',
        delete: '/wx/wxAuthorizationRecords/delete',
        deleteBatch: '/wx/wxAuthorizationRecords/deleteBatch',
        exportXlsUrl: 'wx/wxAuthorizationRecords/exportXls',
        importExcelUrl: 'wx/wxAuthorizationRecords/importExcel'
      },
      hasTTCL_SCLBTCLJ: Vue.ls.get(USER_ROLE, []).includes('ttcl_sclbtclj'),
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  created() {
  },
  methods: {
    handleDel(record) {
      this.selectedRowKeys = [record.id]
      this.batchDel()
    },
    handleSCLJ(record) {
      let ttclHost = 'https://ttcl.zhisongkeji.com/user/login'
      let username = Vue.ls.get(USER_NAME)
      let recordId = record.id
      let ttclUrl = ttclHost + '?username=' + username + '&recordId=' + recordId
      this.copyToClipboard(ttclUrl)
    },
    handleCompletionUser(record) {
      this.$refs.completionUser.completion(record)
      this.$refs.completionUser.title = '核查未做测评用户编号'
    },
    async copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text);
      } catch (err) {
        console.error('Failed to copy: ', err);
      }
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
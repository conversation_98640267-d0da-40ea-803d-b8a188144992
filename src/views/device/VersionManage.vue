<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">

          <a-col :md="6" :sm="8">
            <a-form-item label="版本名称">
              <a-input placeholder="请输入版本名称" v-model="queryParam.name"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="版本号">
              <a-input placeholder="请输入版本号" v-model="queryParam.appVersion"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary">新增</a-button>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        @change="handleTableChange">

        <span slot="index" slot-scope="text, record, index">
          {{ index + 1 }}
        </span>
        <span slot="action" slot-scope="text, record">
          <span style="display: inline-block;" v-has="'vr:list:push'">
            <a @click="handleDetail(record)">详情</a>
            <a-divider type="vertical"/>
            <a @click="handleEdit(record)">编辑</a>
          </span>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 新增/编辑/详情 -->
    <EditVersion ref="modalForm" @ok="handleModalOk"></EditVersion>
  </a-card>
</template>

<script>
  import EditVersion from './modules/EditVersion.vue'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import { filterObj } from '@/utils/util';
  import { getAction } from '@/api/manage'

  export default {
    name: 'VersionManage',
    mixins: [JeecgListMixin],
    components: {
      EditVersion
    },
    data() {
      return {
        description: 'vr版本管理',
        // 表头
        columns: [
          {
            title: '序号',
            align: 'center',
            dataIndex: 'index',
            scopedSlots: { customRender: 'index' }
          },
          {
            title: '版本名称',
            align: 'center',
            dataIndex: 'name'
          },
          {
            title: '版本号',
            align: 'center',
            dataIndex: 'appVersion'
          },
          {
            title: '创建时间',
            align: 'center',
            dataIndex: 'createTime',
          },
          {
            title: '备注',
            align: 'center',
            dataIndex: 'description'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: '/admin/com/appVersion/list',
        }
      }
    },
    methods: {
      getQueryParams() {
        //获取查询条件
        let sqp = {}
        if(this.superQueryParams){
          sqp['superQueryParams']=encodeURI(this.superQueryParams)
        }
        var param = Object.assign(sqp, this.queryParam);
        param.pageNo = this.ipagination.current;
        param.pageSize = this.ipagination.pageSize;
        return filterObj(param);
      },
      loadData(arg) {
        if(!this.url.list){
          this.$message.error("请设置url.list属性!")
          return
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1;
        }
        var params = this.getQueryParams();//查询条件
        this.loading = true;
        getAction(this.url.list, params).then((res) => {
          if (res.success) {
            this.dataSource = res.result.records;
            this.ipagination.total = res.result.total;
          }
          if(res.code===510){
            this.$message.warning(res.message)
          }
          this.loading = false;
        })
      },

      handleModalOk () {
        this.loadData();
      }
    }
  }
</script>
<style lang="less" scoped>
  /** Button按钮间距 */
  .ant-btn {
    margin-left: 3px
  }

  .ant-card-body .table-operator {
    margin-bottom: 18px;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .anty-row-operator button {
    margin: 0 5px
  }

  .ant-btn-danger {
    background-color: #ffffff
  }

  .ant-modal-cust-warp {
    height: 100%
  }

  .ant-modal-cust-warp .ant-modal-body {
    height: calc(100% - 110px) !important;
    overflow-y: auto
  }

  .ant-modal-cust-warp .ant-modal-content {
    height: 90% !important;
    overflow-y: hidden
  }

  .text-online {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #55C41E;
    margin-right: 12px;
  }

  .text-unline {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #df2e04;
    margin-right: 12px;
  }
</style>
<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">

          <a-col :md="6" :sm="8">
            <a-form-item label="设备名称">
              <a-input placeholder="请输入设备名称" v-model="queryParam.deviceName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="SN号">
              <a-input placeholder="请输入SN号" v-model="queryParam.sn"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="用户名">
              <a-input placeholder="请输入用户名" v-model="queryParam.patientName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary">新增设备</a-button>
      <!-- <a-button @click="handleScreenCast" type="primary">投屏</a-button> -->
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        @change="handleTableChange">

        <span slot="online" slot-scope="text, record">
          <span :class="record.online ? 'text-online' : 'text-unline'"></span>
          <span>{{ record.online ? '在线' : '离线' }}</span>
        </span>
        <span slot="action" slot-scope="text, record">
          <!-- 系统管理员 -->
          <span v-has="'vr:list:stop'">
            <a-popconfirm
              :title="`确定要停用设备吗?`"
              @confirm="handleStop(record.sn)">
              <a>设备停用</a>
            </a-popconfirm>
            <a-divider type="vertical"/>
          </span>
          <!-- 医院管理员 -->
          <span style="display: inline-block;" v-has="'vr:list:push'">
            <a @click="handleScreenCast(record)">投屏</a>
            <a-divider type="vertical" v-if="record.patientId"/>
            <a @click="handlePush(record)" v-if="record.patientId">眼动推送</a>
            <a-divider type="vertical" v-if="record.patientId"/>
            <a @click="handlePushProgram(record)" v-if="record.patientId">方案推送</a>
            <a-divider type="vertical" v-if="record.patientId"/>
            <a-popconfirm v-if="record.patientId"
              :title="`确定要结束训练吗?`"
              @confirm="handleFinish(record.patientId)">
              <a>结束训练</a>
            </a-popconfirm>
            <a-divider type="vertical"/>
            <a-dropdown>
              <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
              <a-menu slot="overlay">
                <a-menu-item>
                  <a @click="handleEdit(record)">编辑</a>
                </a-menu-item>
                <a-menu-item v-has="'vr:list:delete'">
                  <a-popconfirm 
                    :title="`确定要删除这个设备吗?`"
                    @confirm="handleDelete(record.id)">
                    <a>删除设备</a>
                  </a-popconfirm>
                </a-menu-item>
                <a-menu-item>
                  <a @click="handleSelectUser(record.id)">切换患者</a>
                </a-menu-item>
                <a-menu-item>
                  <a @click="handleAddUser(record.id)">新增患者</a>
                </a-menu-item>
                <a-menu-item v-if="record.patientId">
                  <a @click="handleContinue(record)">继续训练</a>
                </a-menu-item>
                <!-- <a-menu-item v-if="record.online && record.locked">
                  <a-popconfirm
                    :title="`确定要解锁这个设备吗?`"
                    @confirm="handleUnlock(record.sn)">
                    <a>解锁设备</a>
                  </a-popconfirm>
                </a-menu-item> -->
                <!-- v-if="record.online" -->
              </a-menu>
            </a-dropdown>
          </span>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 新增/编辑设备 -->
    <edit-device-modal ref="editModal" @ok="handleModalOk"></edit-device-modal>
    <!-- 选择用户 -->
    <select-user-modal ref="selectUserModal" @ok="handleModalOk"></select-user-modal>
    <!-- 新增用户 -->
    <add-user-modal ref="addUserModal" @ok="handleModalOk"></add-user-modal>
    <!-- 推送方案 -->
    <push-program-modal ref="pushProgramModal" @ok="handleModalOk"></push-program-modal>
    <!-- 继续训练 -->
    <continue-training-modal ref="continueTrainingModal" @ok="handleModalOk"></continue-training-modal>
    <!-- 屏幕共享 -->
    <screen-cast-modal ref="screenCastModal" @ok="handleModalOk"></screen-cast-modal>
  </a-card>
</template>

<script>
  import EditDeviceModal from './modules/EditDeviceModal'
  import SelectUserModal from './modules/SelectUserModal'
  import AddUserModal from './modules/AddUserModal.vue'
  import PushProgramModal from './modules/PushProgramModal.vue'
  import ContinueTrainingModal from './modules/ContinueTrainingModal.vue'
  import ScreenCastModal from './modules/ScreenCastModal.vue'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import { httpAction } from '@/api/manage'

  export default {
    name: 'PsCategoryList',
    mixins: [JeecgListMixin],
    components: {
      EditDeviceModal,
      SelectUserModal,
      AddUserModal,
      PushProgramModal,
      ContinueTrainingModal,
      ScreenCastModal
    },
    data() {
      return {
        description: '量表类别管理页面',
        // 表头
        columns: [
          {
            title: 'SN号',
            align: 'center',
            dataIndex: 'sn'
          },
          {
            title: '设备名称',
            align: 'center',
            dataIndex: 'name'
          },
          {
            title: '用户名',
            align: 'center',
            dataIndex: 'patientName'
          },
          // 医生
          {
            title: '用户性别',
            align: 'center',
            dataIndex: 'sex',
            customRender: function(text) {
              return text == 1 ? '男' : text == 2 ? '女' : ''
            }
          },
          {
            title: '用户年龄',
            align: 'center',
            dataIndex: 'age'
          },
          {
            title: '是否在线',
            align: 'center',
            dataIndex: 'online',
            scopedSlots: { customRender: 'online' }
          },
          // {
          //   title: '是否锁定',
          //   align: 'center',
          //   dataIndex: 'locked',
          //   customRender: function(text) {
          //     return text ? '是' : '否'
          //   }
          // },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: '/vr/device/list',
          delete: '/vr/device/delete/',
          finfish: '/vr/patient/stopTrain/',
          unlock: '/vr/device/unlock/',
          stop: '/vr/device/stop/',
          pushAssess: '/vr/patient/pushAssess/'
        }
      }
    },
    methods: {
      handleEdit: function (record) {
        this.$refs.editModal.edit(record);
        this.$refs.editModal.title = "编辑";
        this.$refs.editModal.disableSubmit = false;
      },
      handleAdd: function () {
        this.$refs.editModal.add();
        this.$refs.editModal.title = "新增";
        this.$refs.editModal.disableSubmit = false;
      },
      handleModalOk () {
        this.loadData();
      },

      handleDelete(id) {
        httpAction(this.url.delete + id, {}, 'post').then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.searchReset()
          } else {
            this.$message.warning(res.message)
          }
        })
      },

      handleSelectUser(id) {
        this.$refs.selectUserModal.handleShow(id);
        this.$refs.selectUserModal.title = "切换用户";
        this.$refs.selectUserModal.disableSubmit = false;
      },

      handleAddUser(id) {
        this.$refs.addUserModal.edit({deviceId: id});
        this.$refs.addUserModal.title = "新增用户";
        this.$refs.addUserModal.disableSubmit = false;
      },

      handlePushProgram(record) {
        this.$refs.pushProgramModal.handleShow(record);
        this.$refs.pushProgramModal.title = "下发方案";
      },

      handleContinue(record) {
        this.$refs.continueTrainingModal.handleShow(record);
        this.$refs.continueTrainingModal.title = "继续训练";
      },

      handleScreenCast(record) {
        this.$router.push({ path: '/device/screen', query: { id: record.id }})
        // this.$refs.screenCastModal.handleShow(record);
        // this.$refs.screenCastModal.title = "投屏";
      },
      handlePush(record) {
        // 眼动推送
        httpAction(this.url.pushAssess + record.id, {}, 'post').then((res) => {
          if (res.success) {
            this.$message.success('眼动报告推送成功')
            // this.searchReset()
          } else {
            this.$message.warning(res.message)
          }
        })
      },

      handleStop(id) {
        // TODO: 停用
        httpAction(this.url.stop + id, {}, 'post').then((res) => {
          if (res.success) {
            this.$message.success('设备停用成功')
            this.searchReset()
          } else {
            this.$message.warning(res.message)
          }
        })
      },

      handleUnlock (id) {
        // TODO: 解锁
        httpAction(this.url.unlock + id, {}, 'post').then((res) => {
          if (res.success) {
            this.$message.success('解锁成功')
            this.searchReset()
          } else {
            this.$message.warning(res.message)
          }
        })
      },

      handleFinish(id) {
        // TODO: 结束训练
        httpAction(this.url.finfish + id, {}, 'post').then((res) => {
          if (res.success) {
            this.$message.success('结束训练成功')
            this.searchReset()
          } else {
            this.$message.warning(res.message)
          }
        })
      }
    }
  }
</script>
<style lang="less" scoped>
  /** Button按钮间距 */
  .ant-btn {
    margin-left: 3px
  }

  .ant-card-body .table-operator {
    margin-bottom: 18px;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .anty-row-operator button {
    margin: 0 5px
  }

  .ant-btn-danger {
    background-color: #ffffff
  }

  .ant-modal-cust-warp {
    height: 100%
  }

  .ant-modal-cust-warp .ant-modal-body {
    height: calc(100% - 110px) !important;
    overflow-y: auto
  }

  .ant-modal-cust-warp .ant-modal-content {
    height: 90% !important;
    overflow-y: hidden
  }

  .text-online {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #55C41E;
    margin-right: 12px;
  }

  .text-unline {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #df2e04;
    margin-right: 12px;
  }
</style>
<template>
  <a-card :bordered="false">
    <a-spin :spinning="confirmLoading">
      <div class="screen-content">
        <div class="screen-top">
          <a-icon type="left" @click="handlePre(itemIndex)" />
          <div class="content-main">
            <div class="main-info">
              <div class="info-top">
                <p class="top-title">患者信息</p>
                <div class="top-content">
                  <div class="top-content-left">
                    <p>姓名：
                      <a-tooltip placement="top">
                        <template slot="title">
                          <span>{{ detail.patientName }}</span>
                        </template>
                        <span class="top-name">{{ detail.patientName }}</span>
                      </a-tooltip>
                    </p>
                    <p>性别：{{ detail.sex == 1 ? '男' : detail.sex == 2 ? '女' : '' }}</p>
                    <p>年龄：{{ detail.age }}</p>
                  </div>
                  <div class="top-content-right">
                    <a-button type="primary" shape="round" @click="handleSelectUser">
                      切换用户
                      <a-icon type="caret-down" />
                    </a-button>
                  </div>
                </div>
              </div>

              <div class="info-bottom">
                <a-button v-if=" detail.patientId" type="primary" shape="round" :style="{width: '100%', marginBottom: '10px'}" @click="handlePush">
                  <img class="icon-img" src="../../assets/img/icon_1.png" />
                  眼动推送
                </a-button>
                <a-button-group>
                  <a-button :type="chooseBtn === 1 ? 'primary' : 'default'" @click="chooseBtn = 1">方案推送</a-button>
                  <a-button :type="chooseBtn === 2 ? 'primary' : 'default'" @click="chooseBtn = 2">继续训练</a-button>
                </a-button-group>

                <div v-if="chooseBtn === 1" class="bottom-content">
                  <div class="bottom-content-top">
                    <p>已选择：</p>
                    <div class="right-item" v-for="(item) in chooseList" :key="item.value">
                      <span class="item-name">{{ item.title }}</span>
                      <a-icon class="item-icon" type="close" @click="handleDel(item)" />
                    </div>
                    <a-button type="link" @click="addPackage">推送</a-button>
                  </div>
                  <a-tree
                    :checkable="true"
                    @check="onCheck"
                    :selectedKeys=[]
                    :checkedKeys="checkedKeys"
                    :treeData="treeData"
                    :checkStrictly="true" />
                </div>

                <div v-else class="bottom-content">
                  <template v-if="packageData.length">
                    <div class="content-item" v-for="item in packageData" :key="item.id">
                      <div class="item-left">
                        <p class="item-time">{{ item.pushDate }}</p>
                        <p class="item-name" v-for="i in item.trainInfo" :key="i.id">{{ i.trainName }}</p>
                      </div>
                      <a-button type="link" @click="handleContinue(item)">
                        继续训练
                      </a-button>
                    </div>
                  </template>

                  <p v-else>当前患者暂无可继续训练方案</p>
                </div>
                <!-- <a-button @click="handlePushProgram">方案推送</a-button>
                <a-button @click="handleContinue">继续训练</a-button> -->
              </div>

              <div class="bottom-footer">
                <div class="footer-top" v-if=" detail.patientId">
                  <a-button @click="handleRecalibrate" shape="round">
                    <img class="icon-img" src="../../assets/img/icon_3.png" />
                    眼动校准
                  </a-button>
                  <a-button @click="handleReset" shape="round">
                    <img class="icon-img" src="../../assets/img/icon_4.png" />
                    重置视角
                  </a-button>
                </div>

                <a-popconfirm
                  :title="`确定要结束训练吗?`"
                  @confirm="handleFinish">
                  <a-button shape="round" style="width: 100%;">
                    <img class="icon-img" src="../../assets/img/icon_2.png" />
                    结束训练
                  </a-button>
                </a-popconfirm>
              </div>
            </div>

            <div class="main-screen-wraper" style="width: 100%;">
              <div class="main-screen">
                <iframe :src="'http://' + detail.screenUrl" frameborder="0" style="width: 100%; height: 100%;"></iframe>
                <div class="power">
                  <img class="power-left" :src="require(`../../assets/img/power_left_${!detail.leftHandlePower || Number(detail.leftHandlePower) <=  0 ? '0' : detail.leftHandlePower}.png`)" />
                  <img class="power-img" :src="require(`../../assets/img/power_${!detail.power || Number(detail.power) <= 0 ? '0' : Math.round(detail.power / 20) || 1}.png`)" />
                  <img class="power-right" :src="require(`../../assets/img/power_right_${!detail.rightHandlePower || Number(detail.rightHandlePower) <= 0 ? '0' : detail.rightHandlePower}.png`)" />
                </div>
                <!-- <a-icon class="destop-icon" type="desktop" @click="openNewPage" /> -->
                <svg v-has="'device:screen:cast'" @click="openNewPage" t="1743664172704" class="icon destop-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4606" id="mx_n_1743664172704" width="200" height="200"><path d="M527.104 697.770667l161.792 161.792a21.333333 21.333333 0 0 1-15.061333 36.437333H350.165333a21.333333 21.333333 0 0 1-15.061333-36.437333l161.792-161.792a21.333333 21.333333 0 0 1 30.208 0zM810.666667 128a128 128 0 0 1 127.786666 120.490667L938.666667 256v426.666667a128 128 0 0 1-120.490667 127.786666L810.666667 810.666667h-110.293334l-85.333333-85.333334H810.666667a42.666667 42.666667 0 0 0 42.368-37.674666L853.333333 682.666667V256a42.666667 42.666667 0 0 0-37.674666-42.368L810.666667 213.333333H213.333333a42.666667 42.666667 0 0 0-42.368 37.674667L170.666667 256v426.666667a42.666667 42.666667 0 0 0 37.674666 42.368L213.333333 725.333333h195.584l-85.333333 85.333334H213.333333a128 128 0 0 1-127.786666-120.490667L85.333333 682.666667V256a128 128 0 0 1 120.490667-127.786667L213.333333 128h597.333334z" fill="#448ef7" p-id="4607"></path></svg>
                <!-- <a-icon v-if="!detail.locked" type="lock" @click="handleUnlock(1)" /> -->
              <!-- <a-icon v-if="detail.locked" type="unlock" @click="handleUnlock()" /> -->
              </div>
            </div>
          </div>
          <a-icon type="right" @click="handleNext(itemIndex)" />
        </div>

        <div class="screen-bottom">
          <template v-for="(item, index) in dataList">
            <img v-if="itemIndex !== index" :key="item.id" class="bottom-item" src="../../assets/img/device_1.png" @click="chooseItem(item)" />
            <img v-else :key="item.id + 'choose'" class="bottom-item" src="../../assets/img/device_2.png" @click="chooseItem(item)" />
          </template>
          
          <!-- <div class="bottom-item" :class="[itemIndex === index && 'choose-item1']" :key="item.id" @click="chooseItem(item)">
            <div class="item-none"></div>
            <p class="item-name">{{ item.name }}</p>
          </div> -->
        </div>
      </div>
    </a-spin>

    <!-- 选择用户 -->
    <select-user-modal ref="selectUserModal" @ok="handleModalOk"></select-user-modal>
    <!-- 推送方案 -->
    <push-program-modal ref="pushProgramModal"></push-program-modal>
    <!-- 继续训练 -->
    <continue-training-modal ref="continueTrainingModal"></continue-training-modal>
  </a-card>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import SelectUserModal from './modules/SelectUserModal'
import PushProgramModal from './modules/PushProgramModal.vue'
import ContinueTrainingModal from './modules/ContinueTrainingModal.vue'

export default {
  name: 'ScreenCast',

  components: {
    SelectUserModal,
    PushProgramModal,
    ContinueTrainingModal,
  },
  data() {
    return {
      title: '更换用户',
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      dataList: [],
      itemIndex: 0,
      detail: {},
      url: {
        list: '/vr/device/list',
        finfish: '/vr/patient/stopTrain/',
        unlock: '/vr/device/unlock/',
        findPackageData: '/vr/patient/findPackageData/',
        doTraining: '/vr/patient/pushPackage/doTraining',
        treeList: '/train/cogTrainPackage/queryTreeSelectList',
        cognizeRecommend: '/psychology/psUser/cognizeRecommend',
        add: '/train/cogTrainPackage/add',
        pushPackage: '/vr/patient/pushPackage/',
        pushAssess: '/vr/patient/pushAssess/',
        resetAngle: '/vr/device/resetAngle/',
        recalibrate: '/vr/device/recalibrate/'
      },
      chooseBtn: 1,
      treeData: [],
      chooseList: [],
      checkedKeys: [],
      packageData: [],
    }
  },
  created() {
    this.getData()
  },
  methods: {
    openNewPage() {
      window.open('http://' + this.detail.screenUrl)
    },
    getData() {
      httpAction(this.url.list, {pageNo: 1, pageSize: 9999}, 'get').then((res) => {
        if (res.success) {
          this.dataList = res.result.records
          this.itemIndex = this.dataList.findIndex(item => item.id === this.$route.query.id)
          this.detail = this.dataList[this.itemIndex]
          this.findPackageData()
          this.loadTreeData()
        } else {
          this.$message.warning(res.message)
        }
      })
    },

    chooseItem(item) {
      if (!item.online) return
      this.detail = item
      this.itemIndex = this.dataList.findIndex(item => item.id === this.detail.id)
      this.findPackageData()
      this.loadTreeData()
    },

    close() {
      this.$emit('close')
      this.visible = false
    },

    handlePush() {
      // 眼动推送
      httpAction(this.url.pushAssess + this.detail.id, {}, 'post').then((res) => {
        if (res.success) {
          this.$message.success('眼动评估推送成功')
        } else {
          this.$message.warning(res.message)
        }
      })
    },

    handleCancel() {
      this.close()
    },

    handleSelectUser() {
      this.$refs.selectUserModal.handleShow(this.detail.id);
      this.$refs.selectUserModal.title = "切换用户";
      this.$refs.selectUserModal.disableSubmit = false;
    },

    handleModalOk() {
      this.getData()
      this.findPackageData()
      this.loadTreeData()
    },

    handlePushProgram() {
      this.$refs.pushProgramModal.handleShow(this.detail);
      this.$refs.pushProgramModal.title = "下发方案";
    },

    // 继续训练
    handleContinue(item) {
      const params = {
        deviceId: this.detail.id,
        packageId: item.packageId,
        trainIds: item.trainInfo.filter(i => !i.finished).map(i => i.trainId)
      }
      httpAction(this.url.doTraining, params, 'post').then((res) => {
        if (res.success) {
          this.dataList = res.result || []
        } else {
          this.$message.warning(res.message)
        }
      })
    },

    handlePre(index) {
      if (index <= 0) return

      index--
      const data = this.dataList[index]
      if (!data.online) {
        this.handlePre(index)
      } else {
        this.detail = data
        this.itemIndex = index
        this.findPackageData()
        this.loadTreeData()
      }
    },

    handleNext(index) {
      if (this.itemIndex >= this.dataList.length - 1) return

      index++
      const data = this.dataList[index]
      if (!data.online) {
        this.handleNext(index)
      } else {
        this.detail = data
        this.itemIndex = index
        this.findPackageData()
        this.loadTreeData()
      }
    },

    handleUnlock (type) {
      // 解锁
      httpAction(this.url.unlock + this.detail.sn, {}, 'post').then((res) => {
        if (res.success) {
          this.detail.locked = false
          this.$message.success('锁定成功')
        } else {
          this.$message.warning(res.message)
        }
      })
    },

    handleFinish() {
      // 结束训练
      httpAction(this.url.finfish + this.detail.patientId, {}, 'post').then((res) => {
        if (res.success) {
          this.$message.success('结束训练成功')
          this.handleModalOk()
        } else {
          this.$message.warning(res.message)
        }
      })
    },

    // 重置视角
    handleReset() {
      httpAction(this.url.resetAngle + this.detail.sn, {}, 'post').then((res) => {
        if (res.success) {
          this.$message.success('重置视角成功')
        } else {
          this.$message.warning(res.message)
        }
      })
    },

    // 眼动校准
    handleRecalibrate() {
      httpAction(this.url.recalibrate + this.detail.sn, {}, 'post').then((res) => {
        if (res.success) {
          this.$message.success('眼动校准成功')
        } else {
          this.$message.warning(res.message)
        }
      })
    },

    findPackageData() {
      httpAction(this.url.findPackageData + this.detail.patientId, {}, 'get').then((res) => {
        if (res.success) {
          this.packageData = res.result || []
        } else {
          this.$message.warning(res.message)
        }
      })
    },

    loadTreeData() {
      getAction(this.url.treeList, {trainType: 'vr'}).then((res) => {
        if (res.success) {
          for (let i = 0; i < res.result.length; i++) {
            res.result[i].disableCheckbox = true
          }

          this.treeData = res.result
        }
      })
      this.cognizeRecommend()
    },

    // 查询患者初始化游戏
    cognizeRecommend() {
      if (!this.detail.patientId) {
        this.chooseList = []
        this.checkedKeys = []
        return
      }
      getAction(this.url.cognizeRecommend, {trainType: 'vr', id: this.detail.patientId}).then((res) => {
        if (res.success) {

          this.chooseList = res.result.map((item) => {
            return {
              title: item.name,
              value: item.id
            }
          })
          this.checkedKeys = res.result.map(item => item.id)
        }
      })
    },

    onCheck(checkedKeys, info) {
      console.log('onCheck', checkedKeys, info)
      this.checkedKeys = checkedKeys.checked
      this.chooseList = []
      info.checkedNodes.forEach(item => {
        this.chooseList.push({
          title: item.data.props.title,
          value: item.data.key
        })
      })

      this.chooseList = Array.from(new Set(this.chooseList.map(JSON.stringify)), JSON.parse)
    },

    handleDel(item) {
      this.chooseList = this.chooseList.filter((i) => i.value !== item.value)
      this.checkedKeys = this.checkedKeys.filter((i) => i !== item.value)
    },

    // 添加方案、推送方案
    addPackage() {
      const that = this
      if (!this.checkedKeys.length) return
      // 触发表单验证
      httpAction(this.url.add, {trainType: 'vr', trainIds: this.checkedKeys.join(',')}, 'post').then((res) => {
        if (res.success) {
          httpAction(this.url.pushPackage + this.detail.id + '/' + res.result.id, {deviceId: this.detail.id, packageId: res.result.id}, 'post').then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        } else {
          that.$message.warning(res.message)
        }
      }).finally(() => {
        that.confirmLoading = false
      })
      
    },
  }
}
</script>

<style lang="less" scoped>
.screen-content {
  .screen-top {
    display: flex;
    align-items: center;

    .anticon-left, .anticon-right {
      font-size: 24px;
      margin: 12px;
      cursor: pointer;
    }

    .content-main {
      flex: 1;
      display: flex;

      .main-info {
        width: 480px;
        height: 800px;
        margin-right: 20px;
        display: flex;
        flex-direction: column;
        // justify-content: space-between;
        padding: 20px;
        background: #1890ff;
        border-radius: 12px;

        .info-top {
          .top-title {
            padding-left: 20px;
            font-size: 16px;
            font-weight: 600;
            color: #fff;
          }

          .top-content {
            display: flex;
            padding: 20px;
            background: #fff;
            border-radius: 12px;

            .top-content-left {
              flex: 1;

              p {
                font-size: 16px;
                margin: 0;
                padding: 2px 0;
                display: flex;
                align-items: center;
              }

              .top-name {
                width: 100px;
                display: inline-block;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }

            .top-content-right {
              flex: 1;
              display: flex;
              flex-direction: column;
            }
          }
        }

        .info-bottom {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 20px;
          background: #fff;
          border-radius: 12px;
          margin-top: 20px;
          overflow: hidden;

          .bottom-content {
            flex: 1;
            width: 100%;
            padding: 12px 0;
            overflow: auto;

            .ant-btn-link {
              width: fit-content;
            }

            .bottom-content-top {
              display: flex;
              flex-wrap: wrap;
              align-items: center;

              p {
                line-height: 42px;
                margin: 0;
              }

              .right-item {
                height: 32px;
                padding: 0 8px;
                margin: 4px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                background: #F6FFED;

                .item-name {
                  font-size: 16px;
                  line-height: 20px;
                  color: #55C41E;
                }

                .item-icon {
                  font-size: 16px;
                  line-height: 20px;
                  color: #55C41E;
                }
              }
            }

            p {
              line-height: 42px;
              margin: 0;
            }

            .content-item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 12px 20px;
              margin-bottom: 20px;
              border: 1px solid #ccc;
              border-radius: 8px;

              .item-time {
                font-size: 16px;
                line-height: 28px;
                color: #333;
                margin: 0;
              }

              .item-name {
                font-size: 16px;
                line-height: 28px;
                color: #aaa;
                margin: 0;
              }
            }
          }
        }

        .bottom-footer {
          padding-top: 18px;
          width: 100%;

          .footer-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 18px;
          }
        }
      }

      .main-screen {
        position: relative;
        flex: 1;
        height: 800px;
        // background: red;
        border-radius: 8px;

        .power {
          position: absolute;
          top: 30px;
          right: 12px;
          padding: 12px 20px;
          border-radius: 10px;
          background: rgba(0, 0, 0, 0.35);
          z-index: 1;

          .power-left {
            height: 26px;
            margin: 10px 4px 0 0;
          }

          .power-img {
            height: 40px;
          }

          .power-right {
            height: 26px;
            margin: 10px 0 0 4px;
          }
        }

        .item-screen {
          width: 100%;
          height: 100%;
        }

        .anticon {
          position: absolute;
          top: 15px;
          left: 20px;
          background: rgba(255, 255, 255, 0.5);
          width: 36px;
          height: 36px;
          line-height: 36px;
          border-radius: 50%;
          cursor: pointer;
        }

        .destop-icon {
          position: absolute;
          top: 15px;
          left: 20px;
          width: 40px;
          height: 40px;
          // font-size: 36px;
          // color: #4AF21D;
          z-index: 1;
          cursor: pointer;
        }
      }

      .screen-lock {
        filter: blur(6px);
        backdrop-filter: blur(6px);
      }
    }
  }

  .ant-btn-group {
    border-radius: 21px;
    overflow: hidden;
  }

  .ant-btn-group > .ant-btn:last-child:not(:first-child) {
    border-radius: 0 21px 21px 0;
  }

  .ant-btn-group > .ant-btn:first-child:not(:last-child) {
    border-radius: 21px 0 0 21px;
  }

  .ant-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 42px;
    font-size: 16px;
    font-weight: 600;
    line-height: 40px;
    text-align: center;
  }

  .ant-btn-background-ghost {
    margin-bottom: 20px;
  }

  .screen-bottom {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    overflow-x: auto;
    padding-left: 450px;

    .bottom-item {
      width: 46px;
      height: 46px;
      margin: 0 28px;
      cursor: pointer;

      .item-img {
        width: 120px;
        height: 80px;
        border-radius: 4px;
      }

      .item-none {
        width: 120px;
        height: 80px;
        border-radius: 4px;
        background: #ddd;
      }

      // .choose-item {
      //   background: #1890ff;
      // }

      .item-name {
        padding: 12px 0;
        margin: 0;
        text-align: center;
      }
    }

    .choose-item1 {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 12px;
        left: 12px;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #55C41E;
      }
    }
  }

  .icon-img {
    // width: 24px;
    height: 24px;
    margin-right: 8px;
  }
}
</style>
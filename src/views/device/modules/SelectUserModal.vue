<template>
   <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="更换用户">
          <a-auto-complete
            option-label-prop='name'
            v-model='model.name'
            @select='onChangeName'
            @search='handleSearchName'
          >
            <a-input placeholder='请输入姓名' v-decorator="['name', validatorRules.userNumber ]"
                      autocomplete='off'>
            </a-input>
            <template slot='dataSource'>
              <a-select-option v-for='item in nameList' :key='item.value' :value='item.value'>
                <p>{{ item.text }} {{ item.name }}</p>
              </a-select-option>
            </template>
          </a-auto-complete>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { getAction, httpAction } from '@/api/manage'
  import { globalShowedAuth } from '@/utils/authFilter'

  export default {
    name: 'SelectUserModal',
    data() {
      return {
        title: '更换用户',
        visible: false,
        model: {
          name: '',
          deviceId: null,
          patientId: null
        },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        isArticle: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        nameList: [],
        validatorRules: {
          name: { rules: [{ required: true, message: '请输入姓名!' }]},
        },
        url: {
          findNameList: '/psychology/psUser/findNameList',
          changePatient: '/vr/device/changePatient'
        }
      }
    },
    created() {
      this.handleSearchName('')
    },
    methods: {
      handleShow(id) {
        this.model.deviceId = id
        this.model.name = null
        this.visible = true
      },
      onChangeName(value) {
        if (!value) return
        this.model.patientId = value
        this.$nextTick(() => {
          const name = this.nameList.filter(item => item.value === value)[0].name
          this.model.name = name
        })
      },

      handleSearchName(value) {
        let type = 1
        if (globalShowedAuth('user:search')) {
          type = 2
        }
        getAction(this.url.findNameList, { name: value, type: type }).then((res) => {
          if (res.success) {
            this.nameList = res.result || []
          } else {
            this.$message.error(res.message)
          }
        })
      },

      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          console.log('err', err)
          if (!err) {
            that.confirmLoading = true
            let httpurl = this.url.changePatient
            let method = 'post'
            httpAction(httpurl, this.model, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>

<style scoped>

</style>
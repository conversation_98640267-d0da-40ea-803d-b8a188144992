<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    :okButtonProps="{ props: {disabled: disableSubmit} }"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="版本名称">
          <a-input placeholder="请输入版本名称" v-decorator="['name', validatorRules.name ]" :disabled="disableSubmit"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="版本号">
          <a-input placeholder="请输入版本号" v-decorator="['appVersion', validatorRules.appVersion ]" :disabled="disableSubmit"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="地址">
          <a-input placeholder="请输入地址" v-decorator="['appUrl', validatorRules.appUrl ]" :disabled="disableSubmit"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="升级部门">
          <a-radio-group v-decorator="['status', {initialValue: model.status, rules: [{ required: true, message: '请选择升级部门!' }]}]" :disabled="disableSubmit">
            <a-radio :value="1">全部升级</a-radio>
            <a-radio :value="2">部分升级</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="选择部门" v-if="form.getFieldValue('status')===2" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sysOrgIds">
          <j-select-depart v-decorator="['sysOrgIds', {initialValue: model.sysOrgIds, rules: [{ required: true, message: '请选择部门!' }]}]" :trigger-change="true" :multi="true" :disabled="disableSubmit"></j-select-depart>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="备注">
          <a-input placeholder="请输入备注" v-decorator="['description']" type="textarea" :disabled="disableSubmit" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import JSelectDepart from '@/components/jeecgbiz/JSelectDepart'

  export default {
    name: 'EditVersion',
    components: {
      JSelectDepart
    },
    data() {
      return {
        disableSubmit: false,
        title: '操作',
        visible: false,
        imeiDisabled: false,
        model: {
          status: 1,
          sysOrgIds: []
        },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          name: { rules: [{ required: true, message: '请输入版本名称!' }]},
          appVersion: { rules: [{ required: true, message: '请输入版本号!' }]},
          appUrl: { rules: [{ required: true, message: '请输入地址!' }]},
          status: { rules: [{ required: true, message: '请选择升级部门!' }]},
        },
        url: {
          add: '/admin/com/appVersion/save',
          edit: '/admin/com/appVersion/update'
        }
      }
    },
    created() {
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.model.sysOrgIds = this.model.sysOrgIds === 'all' ? '' : this.model.sysOrgIds
        this.model.status = this.model.sysOrgIds ? 2 : 1
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'name', 'appUrl', 'appVersion', 'description'))
        })
      },

      close() {
        this.$emit('close')
        this.disableSubmit = false
        this.visible = false
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = 'post'
            if (!this.model.id) {
              httpurl += this.url.add
            } else {
              httpurl += this.url.edit
            }
            let formData = Object.assign(this.model, values)
            if (typeof formData.sysOrgIds === 'string') formData.sysOrgIds = formData.sysOrgIds.split(',')
            delete formData.status
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>

<style scoped lang="less">
/deep/ .ant-radio-inner::after {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
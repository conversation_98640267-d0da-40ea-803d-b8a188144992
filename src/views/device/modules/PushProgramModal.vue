<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <div class="program-content">
        <div class="content-left">
          <a-tree
            :checkable="true"
            @check="onCheck"
            :selectedKeys=[]
            :checkedKeys="checkedKeys"
            :treeData="treeData"
            :checkStrictly="true"
            @expand="onExpand" />
        </div>

        <div class="content-right">
          <p class="right-title">已选游戏</p>
          <div class="right-item" :class="[index % 2 ? 'item-2' : 'item-1']" v-for="(item, index) in chooseList" :key="item.value">
            <span class="item-name">{{ item.title }}</span>
            <a-icon class="item-icon" type="delete" @click="handleDel(item)" />
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'
import pick from 'lodash.pick'
import { globalShowedAuth } from '@/utils/authFilter'

export default {
  name: 'PushProgramModal',
  data() {
    return {
      title: '',
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      detail: {},
      treeData: [],
      chooseList: [],
      checkedKeys: [],
      url: {
        treeList: '/train/cogTrainPackage/queryTreeSelectList',
        cognizeRecommend: '/psychology/psUser/cognizeRecommend',
        add: '/train/cogTrainPackage/add',
        pushPackage: '/vr/patient/pushPackage/',
      }
    }
  },
  created() {
  },
  methods: {
    handleShow(detail) {
      this.visible = true
      this.detail = detail
    },

    loadTreeData() {
      getAction(this.url.treeList, {trainType: 'vr'}).then((res) => {
        if (res.success) {
          for (let i = 0; i < res.result.length; i++) {
            res.result[i].disableCheckbox = true
          }

          this.treeData = res.result
        }
      })
      getAction(this.url.cognizeRecommend, {trainType: 'vr', id: this.detail.patientId}).then((res) => {
        if (res.success) {

          this.chooseList = res.result.map((item) => {
            return {
              title: item.name,
              value: item.id
            }
          })
          this.checkedKeys = res.result.map(item => item.id)
        }
      })
    },

    onCheck(checkedKeys, info) {
      this.checkedKeys = checkedKeys.checked
      this.chooseList = []
      info.checkedNodes.forEach(item => {
        this.chooseList.push({
          title: item.data.props.title,
          value: item.data.key
        })
      })

      this.chooseList = Array.from(new Set(this.chooseList.map(JSON.stringify)), JSON.parse)
    },

    onExpand(expandedKeys) {
      this.iExpandedKeys = expandedKeys
      this.autoExpandParent = false
    },

    handleDel(item) {
      this.chooseList = this.chooseList.filter((i) => i.value !== item.value)
      this.checkedKeys = this.checkedKeys.filter((i) => i !== item.value)
    },

    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      if (!this.checkedKeys.length) {
        this.close()
        return
      }
      const that = this
      // 触发表单验证
      httpAction(this.url.add, {trainType: 'vr', trainIds: this.checkedKeys.join(',')}, 'post').then((res) => {
        if (res.success) {
          httpAction(this.url.pushPackage + this.detail.id + '/' + res.result.id, {deviceId: this.detail.id, packageId: res.result.id}, 'post').then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
            that.close()
          })
        } else {
          that.$message.warning(res.message)
        }
      }).finally(() => {
        that.confirmLoading = false
        that.close()
      })
      
    },
    handleCancel() {
      this.close()
    }
  },

  watch: {
    visible(val) {
      if (val) {
        this.checkedKeys = []
        this.chooseList =[]
        this.loadTreeData()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.program-content {
  display: flex;

  .content-left {
    flex: 1;
    height: 680px;
    overflow: auto;
    padding: 40px 20px;
    margin-right: 20px;
    max-height: 700px;
    border: 1px solid #eee;
    border-radius: 8px;
  }

  .content-right {
    flex: 1;
    padding: 40px 20px;
    max-height: 700px;
    overflow: hidden;
    border: 1px solid #eee;
    border-radius: 8px;

    .right-item {
      padding: 12px 20px;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .item-name {
        font-size: 14px;
      }

      .item-icon {
        font-size: 16px;
        color: #0F8EE9;
      }
    }

    .item-1 {
      background: #F6FFED;
      
      .item-name {
        color: #55C41E;
      }
    }

    .item-2 {
      background: #FFF7E6;

      .item-name {
        color: #FA8C16;
      }
    }
  }
}
</style>
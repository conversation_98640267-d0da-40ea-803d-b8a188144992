<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    :footer="null"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <div class="training-content">
        <template v-if="dataList.length">
          <div class="content-item" v-for="item in dataList" :key="item.id">
            <div class="item-left">
              <p class="item-time">{{ item.pushDate }}</p>
              <p class="item-name" v-for="i in item.trainInfo" :key="i.id">{{ i.trainName }}</p>
            </div>
            <a-button type="link" @click="handleContinue(item)">
              继续训练
            </a-button>
          </div>
        </template>

        <p v-else>当前患者暂无可继续训练方案</p>
      </div>
    </a-spin>
  </a-modal>
</template>

<script>
import { getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { globalShowedAuth } from '@/utils/authFilter'
import { httpAction } from '@/api/manage'
import { watch } from 'less';

export default {
  name: 'ContinueTrainingModal',
  data() {
    return {
      title: '更换用户',
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      detail: {},
      dataList: [],
      url: {
        findPackageData: '/vr/patient/findPackageData/',
        doTraining: '/vr/patient/pushPackage/doTraining'
      }
    }
  },
  created() {
  },
  methods: {
    handleShow(detail) {
      this.visible = true
      this.detail = detail
    },

    getData() {
      httpAction(this.url.findPackageData + this.detail.patientId, {}, 'get').then((res) => {
        if (res.success) {
          this.dataList = res.result || []
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    
    handleContinue(item) {
      const params = {
        deviceId: this.detail.id,
        packageId: item.packageId,
        trainIds: item.trainInfo.filter(i => !i.finished).map(i => i.trainId)
      }
      httpAction(this.url.doTraining, params, 'post').then((res) => {
        if (res.success) {
          this.dataList = res.result || []
          this.close()
        } else {
          this.$message.warning(res.message)
        }
      })
    },

    close() {
      this.$emit('close')
      this.visible = false
    },

    handleCancel() {
      this.close()
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getData()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.training-content {
  .content-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    margin-bottom: 20px;
    border: 1px solid #ccc;
    border-radius: 8px;

    .item-time {
      font-size: 16px;
      line-height: 28px;
      color: #333;
      margin: 0;
    }

    .item-name {
      font-size: 16px;
      line-height: 28px;
      color: #aaa;
      margin: 0;
    }
  }
}

/deep/ .ant-modal-body {
  height: 800px;
  overflow: auto;
}
</style>
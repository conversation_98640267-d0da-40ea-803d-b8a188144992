<template>
  <a-modal
    :title="title"
    :width="1500"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :footer="null"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <div class="screen-content">
        <div class="screen-top">
          <a-icon type="left" @click="handlePre(itemIndex)" />
          <div class="content-main">
            <div class="main-info">
              <div class="info-top">
                <p class="top-title">患者信息</p>
                <div class="top-content">
                  <div class="top-content-left">
                    <p>姓名：{{ detail.patientName }}</p>
                    <p>性别：{{ detail.sex }}</p>
                    <p>年龄：{{ detail.age }}</p>
                  </div>
                  <div class="top-content-right">
                    <a-button type="primary" ghost @click="handleSelectUser">
                      切换用户
                      <a-icon type="caret-down" />
                    </a-button>
                  </div>
                </div>
              </div>

              <div class="info-bottom">
                <a-button-group>
                  <a-button :type="chooseBtn === 1 ? 'primary' : 'default'" @click="chooseBtn = 1">方案推送</a-button>
                  <a-button :type="chooseBtn === 2 ? 'primary' : 'default'" @click="chooseBtn = 2">继续训练</a-button>
                </a-button-group>

                <div v-if="chooseBtn === 1" class="bottom-content">
                  <div class="bottom-content-top">
                    <p>已选择：</p>
                    <div class="right-item" v-for="(item) in chooseList" :key="item.value">
                      <span class="item-name">{{ item.title }}</span>
                      <a-icon class="item-icon" type="close" @click="handleDel(item)" />
                    </div>
                    <a-button type="link" @click="addPackage">推送</a-button>
                  </div>
                  <a-tree
                    :checkable="true"
                    @check="onCheck"
                    :selectedKeys=[]
                    :checkedKeys="checkedKeys"
                    :treeData="treeData"
                    :checkStrictly="true" />
                </div>

                <div v-else class="bottom-content">
                  <template v-if="packageData.length">
                    <div class="content-item" v-for="item in packageData" :key="item.id">
                      <div class="item-left">
                        <p class="item-time">{{ item.pushDate }}</p>
                        <p class="item-name" v-for="i in item.trainInfo" :key="i.id">{{ i.trainName }}</p>
                      </div>
                      <a-button type="link" @click="handleContinue(item)">
                        继续训练
                      </a-button>
                    </div>
                  </template>

                  <p v-else>当前患者暂无可继续训练方案</p>
                </div>
                <!-- <a-button @click="handlePushProgram">方案推送</a-button>
                <a-button @click="handleContinue">继续训练</a-button> -->
                <a-popconfirm
                  :title="`确定要结束训练吗?`"
                  @confirm="handleFinish">
                  <a-button>结束训练</a-button>
                </a-popconfirm>
              </div>
            </div>

            <div class="main-screen-wraper">
              <div class="main-screen">
                <iframe :src="'http://' + detail.screenUrl" frameborder="0" style="width: 100%; height: 100%;"></iframe>
                <!-- <a-icon v-if="!detail.locked" type="lock" @click="handleUnlock(1)" /> -->
              <a-icon v-if="detail.locked" type="unlock" @click="handleUnlock()" />
              </div>
              <!-- <div class="screen-bottom">
                <div class="bottom-item" :class="[itemIndex === index && 'choose-item']" v-for="(item, index) in dataList" :key="item.id" @click="chooseItem(item)">
                  <div v-if="!item.online" class="item-none"></div>
                  <video
                    v-else
                    class="item-img"
                    controls
                    autoplay
                    preload="auto"
                    :src="item.screenUrl"
                    ref="myVideo"
                    custom-cache="false"
                  >
                  </video>
                  <p class="item-name">{{ item.name }}</p>
                </div>
              </div> -->
            </div>
          </div>
          <a-icon type="right" @click="handleNext(itemIndex)" />
        </div>

        <div class="screen-bottom">
          <div class="bottom-item" :class="[itemIndex === index && 'choose-item']" v-for="(item, index) in dataList" :key="item.id" @click="chooseItem(item)">
            <div v-if="!item.online" class="item-none"></div>
            <p class="item-name">{{ item.name }}</p>
          </div>
        </div>
      </div>
    </a-spin>

    <!-- 选择用户 -->
    <select-user-modal ref="selectUserModal" @ok="handleModalOk"></select-user-modal>
    <!-- 推送方案 -->
    <push-program-modal ref="pushProgramModal"></push-program-modal>
    <!-- 继续训练 -->
    <continue-training-modal ref="continueTrainingModal"></continue-training-modal>
  </a-modal>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import SelectUserModal from './SelectUserModal'
import PushProgramModal from './PushProgramModal.vue'
import ContinueTrainingModal from './ContinueTrainingModal.vue'

export default {
  name: 'ScreenCastModal',

  components: {
    SelectUserModal,
    PushProgramModal,
    ContinueTrainingModal,
  },
  data() {
    return {
      title: '更换用户',
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      dataList: [],
      itemIndex: 0,
      detail: {
        // id: '',
        // imei: '',
        // locked: false,
        // name: '',
        // online: false,
        // patientId: null,
        // patientName: '',
        // screenUrl: '',
        // sex: '',
        // sn: ''
      },
      url: {
        list: '/vr/device/list',
        finfish: '/vr/patient/stopTrain/',
        unlock: '/vr/device/unlock/',
        findPackageData: '/vr/patient/findPackageData/',
        doTraining: '/vr/patient/pushPackage/doTraining',
        treeList: '/train/cogTrainPackage/queryTreeSelectList',
        cognizeRecommend: '/psychology/psUser/cognizeRecommend',
        add: '/train/cogTrainPackage/add',
        pushPackage: '/vr/patient/pushPackage/',
      },
      chooseBtn: 1,
      treeData: [],
      chooseList: [],
      checkedKeys: [],
      packageData: [],
    }
  },
  created() {
  },
  methods: {
    handleShow(detail) {
      this.visible = true
      this.detail = detail
    },

    getData() {
      httpAction(this.url.list, {pageNo: 1, pageSize: 9999}, 'get').then((res) => {
        if (res.success) {
          this.dataList = res.result.records
          this.itemIndex = this.dataList.findIndex(item => item.id === this.detail.id)
          this.detail = this.dataList[this.itemIndex]
        } else {
          this.$message.warning(res.message)
        }
      })
    },

    chooseItem(item) {
      if (!item.online) return
      this.detail = item
      this.itemIndex = this.dataList.findIndex(item => item.id === this.detail.id)
      this.findPackageData()
      this.loadTreeData()
    },

    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          // that.confirmLoading = true
          // let httpurl = ''
          // let method = ''
          // if (!this.model.id) {
          //   httpurl += this.url.add
          //   method = 'post'
          // } else {
          //   httpurl += this.url.edit
          //   method = 'put'
          // }
          // let formData = Object.assign(this.model, values)
          // formData.isArticle = that.isArticle ? 1 : 0
          // httpAction(httpurl, formData, method).then((res) => {
          //   if (res.success) {
          //     that.$message.success(res.message)
          //     that.$emit('ok')
          //   } else {
          //     that.$message.warning(res.message)
          //   }
          // }).finally(() => {
          //   that.confirmLoading = false
          //   that.close()
          // })
        }
      })
    },
    handleCancel() {
      this.close()
    },

    handleSelectUser() {
      this.$refs.selectUserModal.handleShow(this.detail.id);
      this.$refs.selectUserModal.title = "切换用户";
      this.$refs.selectUserModal.disableSubmit = false;
    },

    handleModalOk() {
      this.getData()
      this.findPackageData()
      this.loadTreeData()
    },

    handlePushProgram() {
      this.$refs.pushProgramModal.handleShow(this.detail);
      this.$refs.pushProgramModal.title = "下发方案";
    },

    // 继续训练
    handleContinue(item) {
      const params = {
        deviceId: this.detail.id,
        packageId: item.packageId,
        trainIds: item.trainInfo.filter(i => !i.finished).map(i => i.trainId)
      }
      httpAction(this.url.doTraining, params, 'post').then((res) => {
        if (res.success) {
          this.dataList = res.result || []
        } else {
          this.$message.warning(res.message)
        }
      })
    },

    handlePre(index) {
      if (index <= 0) return

      index--
      const data = this.dataList[index]
      if (!data.online) {
        this.handlePre(index)
      } else {
        this.detail = data
        this.itemIndex = index
        this.findPackageData()
        this.loadTreeData()
      }
    },

    handleNext(index) {
      if (this.itemIndex >= this.dataList.length - 1) return

      index++
      const data = this.dataList[index]
      if (!data.online) {
        this.handleNext(index)
      } else {
        this.detail = data
        this.itemIndex = index
        this.findPackageData()
        this.loadTreeData()
      }
    },

    handleUnlock (type) {
      // TODO: 解锁
      httpAction(this.url.unlock + this.detail.id, {}, 'post').then((res) => {
        if (res.success) {
          this.detail.locked = false
          this.$message.success('锁定成功')
        } else {
          this.$message.warning(res.message)
        }
      })
    },

    handleFinish() {
      // TODO: 结束训练
      httpAction(this.url.finfish + this.detail.patientId, {}, 'post').then((res) => {
        if (res.success) {
          this.$message.success('结束训练成功')
          this.handleModalOk()
        } else {
          this.$message.warning(res.message)
        }
      })
    },

    findPackageData() {
      httpAction(this.url.findPackageData + this.detail.patientId, {}, 'get').then((res) => {
        if (res.success) {
          this.packageData = res.result || []
        } else {
          this.$message.warning(res.message)
        }
      })
    },

    loadTreeData() {
      getAction(this.url.treeList, {trainType: 'vr'}).then((res) => {
        if (res.success) {
          for (let i = 0; i < res.result.length; i++) {
            res.result[i].disableCheckbox = true
          }

          this.treeData = res.result
        }
      })
      getAction(this.url.cognizeRecommend, {trainType: 'vr'}).then((res) => {
        if (res.success) {

          this.chooseList = res.result.map((item) => {
            return {
              title: item.name,
              value: item.id
            }
          })
          this.checkedKeys = res.result.map(item => item.id)
        }
      })
    },

    onCheck(checkedKeys, info) {
      console.log('onCheck', checkedKeys, info)
      this.checkedKeys = checkedKeys.checked
      this.chooseList = []
      info.checkedNodes.forEach(item => {
        this.chooseList.push({
          title: item.data.props.title,
          value: item.data.key
        })
      })

      this.chooseList = Array.from(new Set(this.chooseList.map(JSON.stringify)), JSON.parse)
    },

    handleDel(item) {
      this.chooseList = this.chooseList.filter((i) => i.value !== item.value)
      this.checkedKeys = this.checkedKeys.filter((i) => i !== item.value)
    },

    // 添加方案、推送方案
    addPackage() {
      const that = this
      // 触发表单验证
      httpAction(this.url.add, {trainType: 'vr', trainIds: this.checkedKeys.join(',')}, 'post').then((res) => {
        if (res.success) {
          httpAction(this.url.pushPackage + this.detail.id + '/' + res.result.id, {deviceId: this.detail.id, packageId: res.result.id}, 'post').then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        } else {
          that.$message.warning(res.message)
        }
      }).finally(() => {
        that.confirmLoading = false
      })
      
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.chooseBtn = 1
        this.chooseList = []
        this.checkedKeys = []
        this.getData()
        this.findPackageData()
        this.loadTreeData()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.screen-content {
  .screen-top {
    display: flex;
    align-items: center;

    .anticon-left, .anticon-right {
      font-size: 24px;
      margin: 12px;
      cursor: pointer;
    }

    .content-main {
      flex: 1;
      display: flex;

      .main-info {
        width: 450px;
        margin-right: 20px;
        display: flex;
        flex-direction: column;
        // justify-content: space-between;
        padding: 20px;
        background: #1890ff;
        border-radius: 12px;

        .info-top {
          .top-title {
            padding-left: 20px;
            font-size: 20px;
            font-weight: 600;
            color: #fff;
          }

          .top-content {
            display: flex;
            padding: 20px;
            background: #fff;
            border-radius: 12px;

            .top-content-left {
              flex: 1;

              p {
                font-size: 20px;
                margin: 0;
                padding: 2px 0;
              }
            }

            .top-content-right {
              flex: 1;
              display: flex;
              flex-direction: column;
            }
          }
        }

        .info-bottom {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 20px;
          background: #fff;
          border-radius: 12px;
          margin-top: 20px;

          .bottom-content {
            flex: 1;
            width: 100%;
            padding: 12px 0;
            overflow: auto;

            .ant-btn-link {
              width: fit-content;
            }

            .bottom-content-top {
              display: flex;
              flex-wrap: wrap;
              align-items: center;

              p {
                line-height: 42px;
                margin: 0;
              }

              .right-item {
                height: 32px;
                padding: 0 8px;
                margin: 4px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                background: #F6FFED;

                .item-name {
                  font-size: 14px;
                  line-height: 20px;
                  color: #55C41E;
                }

                .item-icon {
                  font-size: 16px;
                  line-height: 20px;
                  color: #55C41E;
                }
              }
            }

            p {
              line-height: 42px;
              margin: 0;
            }

            .content-item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 12px 20px;
              margin-bottom: 20px;
              border: 1px solid #ccc;
              border-radius: 8px;

              .item-time {
                font-size: 16px;
                line-height: 28px;
                color: #333;
                margin: 0;
              }

              .item-name {
                font-size: 16px;
                line-height: 28px;
                color: #aaa;
                margin: 0;
              }
            }
          }
        }
      }

      .main-screen {
        position: relative;
        flex: 1;
        height: 800px;
        // background: red;
        border-radius: 8px;

        .item-screen {
          width: 100%;
          height: 100%;
        }

        .anticon {
          position: absolute;
          top: 15px;
          left: 20px;
          background: rgba(255, 255, 255, 0.5);
          width: 36px;
          height: 36px;
          line-height: 36px;
          border-radius: 50%;
          cursor: pointer;
        }
      }

      .screen-lock {
        filter: blur(6px);
        backdrop-filter: blur(6px);
      }
    }
  }

  .ant-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 42px;
    font-size: 16px;
    font-weight: 600;
    line-height: 40px;
    text-align: center;
  }

  .ant-btn-background-ghost {
    margin-bottom: 20px;
  }

  .screen-bottom {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    overflow-x: auto;

    .bottom-item {
      padding: 4px;
      margin: 0 6px;
      cursor: pointer;

      .item-img {
        width: 120px;
        height: 80px;
        border-radius: 4px;
      }

      .item-none {
        width: 120px;
        height: 80px;
        border-radius: 4px;
        background: #eee;
      }

      .item-name {
        padding: 12px 0;
        margin: 0;
        text-align: center;
      }
    }

    .choose-item {
      background: #eee;
      border-radius: 4px;
    }
  }
}
</style>
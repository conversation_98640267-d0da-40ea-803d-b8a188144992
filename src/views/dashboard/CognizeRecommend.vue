<template>
  <a-modal
    :title="title"
    :width="device === 'desktop'?800:620"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel">
    <template slot="footer">
      <a-select v-has="'user:form:isTerminal'" style="width:150px;margin-right: 10px;" placeholder="是否推送其他终端"
                @change="terminalHandleChange">
        <a-select-option :value="0">否</a-select-option>
        <a-select-option :value="1">是</a-select-option>
      </a-select>
      <a-select
        v-if="isPushTerminal == 1"
        style="width:150px;margin-right: 10px;"
        placeholder="请选择推送终端"
        @change="handleChange">
        <a-select-option v-for="user in users" :key="user.id">
          {{ user.realname }}
        </a-select-option>
      </a-select>
      <a-button key="back" @click="handleCancel">
        关闭
      </a-button>
      <a-button key="submit" type="primary" @click="handleOk">
        确定
      </a-button>
    </template>
    <a-list
      class="demo-loadmore-list"
      :loading="confirmLoading"
      item-layout="horizontal"
      :data-source="data">
      <a-list-item slot="renderItem" slot-scope="item, index">
        <a-list-item-meta
          :description="item.categoryName">
          <a slot="title" href="javascript:void(0);">{{ item.name }}</a>
        </a-list-item-meta>
        <a slot="actions" @click="removeCognizeList(item.id,index)">删除</a>
      </a-list-item>
    </a-list>
  </a-modal>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import ACol from 'ant-design-vue/es/grid/Col'
import moment from 'moment'
import { queryUserByCurrentAccount } from '@/api/api'
import ARow from 'ant-design-vue/es/grid/Row'
import { mixinDevice } from '@/utils/mixin.js'

export default {
  name: 'CognizeRecommend',
  components: { ARow, ACol },
  mixins: [mixinDevice],
  data() {
    return {
      title: '操作',
      visible: false,
      model: {
        userNumber: ''
      },
      users: [],
      terminalId: '',
      userId: '',
      isPushTerminal: 0,
      data: [],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      url: {
        cognizeRecommend: '/psychology/psUser/cognizeRecommend',
        startTerminalTrain: '/psychology/psUser/startTerminalTrain',
        startTrain2: '/psychology/psUser/startTrain2'
      }
    }
  },
  created() {
  },
  methods: {
    moment,
    loadUserId(userId) {
      this.userId = userId
      this.visible = true
      this.loadCognizeRecommend()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      if (this.isPushTerminal == 0) {
        this.pushAnswerPage()
      } else {
        let that = this
        let httpurl = that.url.startTerminalTrain
        let formData = {}
        formData.id = this.userId
        formData.trainIds = this.data.map((e) => {
          return e.id
        })
        if (this.terminalId) {
          formData.terminalId = this.terminalId
        }
        postAction(httpurl, formData).then((res) => {
          if (res.success) {
            this.$notification['success']({
              message: '添加成功',
              duration: 3,
              description: '已在选择的终端推送认知训练'
            })
            that.confirmLoading = false
            that.close()
          } else {
            that.$message.error(res.message)
          }
        })
      }
    },
    handleCancel() {
      this.close()
    },
    terminalHandleChange(value) {
      this.isPushTerminal = value
      if (value == 1) {
        //加载终端列表
        this.loadTerminalList()
      }
    },
    loadTerminalList() {
      queryUserByCurrentAccount({}).then((res) => {
        if (res.success) {
          this.users = res.result
        } else {
          // console.log(res.message)
        }
      })
    },
    loadCognizeRecommend() {
      let that = this
      that.confirmLoading = true
      getAction(this.url.cognizeRecommend, { id: this.userId }).then((res) => {
        if (res.success) {
          that.data = res.result
        } else {
          that.$message.error(res.message)
        }
        that.confirmLoading = false
      })
    },
    removeCognizeList(id, index) {
      this.data.splice(index, 1)
    },
    pushAnswerPage() {
      let that = this
      let httpurl = that.url.startTrain2
      let paramData = {}
      paramData.trainIds = this.data.map((e) => {
        return e.id
      })
      paramData.id = that.userId
      if (this.terminalId) {
        paramData.terminalId = this.terminalId
      }
      postAction(httpurl, paramData).then((res) => {
        if (res.success) {
          this.$notification['success']({
            message: '添加成功',
            duration: 3,
            description: '已在选择的终端推送答题信息'
          })
          that.confirmLoading = false
          that.close()
        } else {
          that.$message.error(res.message)
        }
      })
    },
    handleChange(value) {
      this.terminalId = value
    }
  }
}
</script>

<style scoped>
/deep/ .ant-model-content {
  position: relative;
  background-color: #fff;
  background-clip: padding-box;
  border: 0;
  border-radius: 2px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  pointer-events: auto;
  max-width: 1000px;
  display: inline-block;
}
</style>
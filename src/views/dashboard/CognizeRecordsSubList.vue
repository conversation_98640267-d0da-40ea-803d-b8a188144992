<template>
  <div>
    <a-card :bordered="false" style="height:612px;overflow:auto;" :bodyStyle="style">
      <a-skeleton :loading="loading" style="padding-left:16px;margin-top: 5px;" active>
        <div style="background: #fff;padding-left:16px;height: 100%; margin-top: 5px">
          <!-- 树-->
          <template>
            <a-list item-layout="horizontal" style="width:100%;" :data-source="trainRecordsList">
              <a-list-item slot="renderItem" class="listContent" slot-scope="item">
                <a-row>
                  <a-col :span="18">
                    <a-list-item-meta>
                      <a slot="title">套餐名称:{{ item.name }}</a>
                      <span>{{ item.trainNames }}</span>
                    </a-list-item-meta>
                  </a-col>
                  <a-col :span="6">
                    <a-button key="submit" type="primary" @click="submitSelected(item)">
                      开始答题
                    </a-button>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="24">
                    <div v-for="(text, index) in item.trainNames.split(',')" :key="index">
                      {{ text }}
                      <a-button v-if="text.length > 0" type="link" style="color: red; padding: 0 5px;" @click="deleteTrain(item, index)">x</a-button>
                    </div>
                  </a-col>
                </a-row>
              </a-list-item>
            </a-list>
          </template>
        </div>
      </a-skeleton>
    </a-card>
    <!-- 表单区域 -->
    <UserForm ref="UserForm" :type="2" @ok="modalFormOk"/>
  </div>
</template>

<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import {searchTrainRecord} from '@/api/api'
import UserForm from './UserForm2'

export default {
  name: 'CognizeRecordsSubList',
  mixins: [JeecgListMixin],
  components: {
    UserForm
  },
  data() {
    return {
      description: '训练列表页面',
      loading: true,
      style: {
        'padding-left': '0px',
        'padding-top': '0px'
      },
      trainRecordsList: [],
      checkIds: []
    }
  },
  methods: {
    loadData() {
      var that = this
      that.trainRecordsList = []
      that.loading = true
      searchTrainRecord({pageNo: 1, pageSize: 1000}).then((res) => {
        if (res.success) {
          that.trainRecordsList = res.result.records
        }
      }).finally(() => {
        this.loading = false
      })
    },
    onClearSelected() {
      this.selectedKeys = []
      this.checkIds = []
    },
    deleteTrain(item, index) {
      let trainNames = item.trainNames.split(',')
      let trainIds = item.trainIds.split(',')
      trainNames.splice(index, 1)
      trainIds.splice(index, 1)
      
      item.trainNames = trainNames.join(',')
      item.trainIds = trainIds.join(',')
      console.log(item)
    },
    submitSelected(item) {
      if (item.trainIds) {
        //保存
        let trainIds = item.trainIds
        this.$refs.UserForm.loadtrainIds(trainIds.split(","), item.id)
        this.$refs.UserForm.title = '添加测试者信息'
      } else {
        this.$message.warning("训练套餐中未包含任何训练!")
      }
    },
  }
}
</script>
<style lang="less" scoped>
.ant-card-bordered {
  border: 0px solid #e8e8e8
}

.listContent {
  border: 1px solid #e8e8e8 !important;
  margin-bottom: 5px;
  border-radius: 10px;
  padding-left: 5px;
  padding-right: 5px;
}
.ant-list-item {
  align-items: stretch;
  flex-direction: column;
}
</style>
<template>
  <div>
    <a-tabs class="tabs" default-active-key="1">
      <a-tab-pane key="1" tab="心理评估" force-render>
        <measure-sub-list/>
      </a-tab-pane>
      <a-tab-pane key="2" tab="量表套餐" :disabled="hasRoleFZD">
        <auth-records-sub-list></auth-records-sub-list>
      </a-tab-pane>
      <a-tab-pane class="tab-wrapper" v-if="isShowAuth('home:list:cognize:action')" key="3" tab="认知训练">
        <cognize-sub-list :tagShow.sync="tagShow" @changeTagStatus="changeTagStatus" />
      </a-tab-pane>
      <a-tab-pane class="tab-wrapper" v-if="isShowAuth('home:list:cognize:action')" key="4" tab="训练套餐">
        <cognize-records-sub-list />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>
import MeasureSubList from '@views/dashboard/MeasureSubList'
import CognizeSubList from '@views/dashboard/CognizeSubList'
import AuthRecordsSubList from '@views/dashboard/AuthRecordsSubList'
import CognizeRecordsSubList from '@/views/dashboard/CognizeRecordsSubList'
import {globalShowedAuth} from '@/utils/authFilter'
import Vue from 'vue'
import { USER_ROLE } from '@/store/mutation-types'

export default {
  name: 'MeasureList',
  components: {
    MeasureSubList,
    CognizeSubList,
    AuthRecordsSubList,
    CognizeRecordsSubList
  },
  data() {
    return {
      tagShow: false,
      hasRoleFZD: Vue.ls.get(USER_ROLE, []).includes('fzddt'),
    }
  },
  computed: {},
  created() {
  },
  methods: {
    isShowAuth(code) {
      return globalShowedAuth(code)
    },

    changeTagStatus() {
      this.tagShow = !this.tagShow
      this.$emit('changeTagShow', this.tagShow)
    }
  }
}
</script>
<style lang="less" scoped>
.ant-card-bordered {
  border: 0px solid #e8e8e8
}

/deep/ .ant-card-body {
  padding-top: 5px;
  padding-left: 10px;
}

.tabs {
  position: relative;
  margin-bottom: 0px;
  background: #FFFFFF;
  padding-left: 10px;
}

/deep/ .ant-tabs-tab {
  margin: 0 !important;
}
</style>
<template>
  <div class="game-menu">
    <a-collapse v-model="activeKey" :bordered="false">
      <template #expandIcon="props">
        <a-icon type="caret-right" :rotate="props.isActive ? 90 : 0" />
      </template>
      <a-collapse-panel v-for="item in menuData" :key="item.key" :header="item.title">
        <div :class="['item-wrapper', checkKeys.includes(it.key) && 'choose-item']" v-for="it in item.children" :key="it.key" @click="chooseItem(it)">
          <img class="item-img" :src="it.icon" />
          <p class="item-title">{{ it.title }}</p>
          <div class="item-diff">
            <img class="item-icon" v-for="item in Number(it.difficulty)" :key="item" src="/static/game_assets/icon/star.png" />
            <!-- <span class="item-num">{{ it.difficulty }}</span> -->
          </div>
        </div>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<script>
import Vue from 'vue'
import { USER_ROLE } from '@/store/mutation-types'
export default {
  data() {
    return {
      activeKey: '',
      menuData: [],
      checkKeys: [],
      titles: [],
      hasRoleFZD: Vue.ls.get(USER_ROLE, []).includes('fzddt'),
    }
  },

  mounted() {
    this.$bus.$on('setMenu', data => {
      this.menuData = data
      this.activeKey = this.menuData && this.menuData[0] && this.menuData[0].key
    })

    this.$bus.$on('setKeys', keys => {
      this.checkKeys = keys
    })
  },

  methods: {
    chooseItem(item) {
      if (this.hasRoleFZD) return
      if (this.checkKeys.includes(item.key)) {
        this.checkKeys = this.checkKeys.filter(it => it !== item.key)
        this.titles = this.titles.filter(it => it !== item.title)
      } else {
        this.checkKeys.push(item.key)
        this.titles.push(item.title)
      }

      this.$bus.$emit('setNodes', this.checkKeys, this.titles)
    }
  }
}
</script>

<style lang="less">
.game-menu {
  height: 1300px;
  overflow: auto;

  .ant-collapse-borderless {
    background: #fff;

    .ant-collapse-item {
      border: none;
    }
  }

  .ant-collapse-content-box {
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;

    .item-wrapper {
      width: 154px;
      padding: 10px 12px;
      margin: 20px;
      text-align: center;
      cursor: pointer;

      .item-img {
        width: 80px;
        height: 80px;
      }

      .item-title {
        margin-bottom: 0;
        padding: 8px 0;
        font-size: 12px;
      }

      .item-diff {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: center;

        .item-icon {
          width: 30px;
          height: 30px;
        }
      }
    }

    .choose-item {
      background: #fafafa;
    }
  }
}
</style>
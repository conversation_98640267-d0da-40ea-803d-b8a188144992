<template>
  <div class="ddst">
    <div class="ddst-button">
      <div :class="['button-item', chooseBtn === item && 'choose-button' ]" v-for="item in ddstDetail.ddstAgeList" :key="item" @click="setChoose(item)">{{item}}</div>
    </div>

    <div class="ddst-content">
      <div class="content-item" v-for="(item, index) in questions" :key="index + 'item'">
        <p class="item-name">{{item.subsectionTitle}}</p>

        <div class="item-question" v-for="(ques, index) in item.list" :key="ques.id">
          <p class="question-name">{{ index + 1 }}. {{ ques.title }}</p>

          <div class="question-answer">
            <div
              class="answer-item"
              v-for="(answer) in ques.psOptions"
              :key="answer.id"
              :id="answer.id"
              @click="chooseItem(ques, answer, true)"
            >
              <a-button :class="[ques.answer === answer.title && 'choose-btn']" type="primary" size="large">{{ answer.title }}</a-button>
              <span class="item-text">{{ answer.content }}</span>
            </div>
          </div>
        </div>

        <div class="optional-question">
          <p class="title">以下题目任选三道作答</p>
          <div class="item-question" v-for="(ques, index) in item.optionalList" :key="ques.id">
            <p class="question-name">{{ index + 1 }}. {{ ques.title }}</p>

            <div class="question-answer">
              <div
                class="answer-item"
                :class="[item.disabled && !ques.answer && 'disable-item']"
                v-for="(answer) in ques.psOptions"
                :key="answer.id"
                :id="answer.id"
                @click="chooseItem(ques, answer, false, item)"
              >
                <a-button :class="[ques.answer === answer.title && 'choose-btn']" type="primary" size="large" :disabled="item.disabled && !ques.answer">{{ answer.title }}</a-button>
                <span class="item-text">{{ answer.content }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <a-button class="confirm-btn" type="primary" size="small" @click="sumbit">提交</a-button>
  </div>
</template>

<script>
export default {
  name: 'DDST',

  props: {
    questionList: {
      type: Array,
      default: () => []
    },

    ddstDetail: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      chooseBtn: '',
      questions: [],
      result: [],
    }
  },

  methods: {
    initData() {
      this.result = [],
      this.questions = []
    },

    setChoose(item) {
      this.initData()
      this.chooseBtn = item
      let questionKeys = []
      let optionalKeys = []
      for (let i in this.ddstDetail.ddstTitel2AgeMap) {
        if (this.ddstDetail.ddstTitel2AgeMap[i].includes(item)) {
          questionKeys.push(i)
        }

        if (this.ddstDetail.ddstAge2SortMap[item] > this.ddstDetail.ddstAge2SortMap[this.ddstDetail.ddstTitel2AgeMap[i][this.ddstDetail.ddstTitel2AgeMap[i].length - 1]]) {
          optionalKeys.push(i)
        }
      }
      
      const copyQuestionList = JSON.parse(JSON.stringify(this.questionList))
      const questionList = copyQuestionList.filter(item => questionKeys.includes(item.titleExplain))
      const optionalList = copyQuestionList.filter(item => optionalKeys.includes(item.titleExplain))
      questionList.forEach(item => {
        const questionItem = this.questions.filter(it => it.subsectionTitle === item.subsectionTitle)
        if (questionItem.length) {
          questionItem[0].list.push(item)
        } else {
          this.questions.push({
            subsectionTitle: item.subsectionTitle,
            disabled: false,
            list: [item],
            optionalList: [],
            optionalResult: []
          })
        }
      })

      optionalList.forEach(item => {
        const questionItem = this.questions.filter(it => it.subsectionTitle === item.subsectionTitle)
        if (questionItem.length) {
          questionItem[0].optionalList.push(item)
        } else {
          this.questions.push({
            subsectionTitle: item.subsectionTitle,
            disabled: false,
            list: [],
            optionalList: [item],
            optionalResult: []
          })
        }
      })
    },

    chooseItem(ques, item, thresholdQuestion, quesItem) {
      if (quesItem && quesItem.disabled && !ques.answer) return

      if (ques.answer === item.title) {
        ques.answer = ''
      } else {
        ques.answer = item.title
      }

      const hasItem = this.result.filter(it => it.questionId === ques.id)
      if (hasItem.length) {
        if (hasItem[0].optionTitle === item.title) {
          this.result = this.result.filter(it => it.questionId !== ques.id)
        } else {
          hasItem[0].optionTitle = item.title
        }
      } else {
        this.result.push({
          optionTitle: item.title,
          optionId: item.id,
          questionId: ques.id,
          questionSubsectionTitle: ques.subsectionTitle,
          questionTitle: ques.title,
          questionTitleExplain: ques.titleExplain,
          thresholdQuestion
        })
      }

      if (!thresholdQuestion) {
        const question = this.questions.filter(it => it.subsectionTitle === ques.subsectionTitle)[0]
        const resultItem = question.optionalResult.filter(res => res.id === ques.id)
        if (resultItem.length) {
          if (resultItem[0].title === item.title) {
            question.optionalResult = question.optionalResult.filter(res => res.id !== ques.id)
          } else {
            resultItem[0].title = item.title
          }
        } else {
          question.optionalResult.push({
            id: ques.id,
            title: item.title
          })
        }

        question.disabled = question.optionalResult.length >= 3
      }
    },

    sumbit() {
      let length1 = 0
      let length2 = 0
      let resLength1 = 0
      let resLength2 = 0
      this.questions.forEach(item => {
        length1 += item.list
        length2 += (item.optionalList.length < 3 ? item.optionalList.length : 3)
      })
      resLength1 = this.result.filter(item => item.thresholdQuestion).length
      resLength2 = this.result.filter(item => !item.thresholdQuestion).length

      if (length1 > resLength1 || length2 > resLength2) {
        this.$message.warning('题目未答完，无法提交', 5)
        return
      }
      this.$emit('commit', {result: this.result})
    }
  },

  watch: {
    'ddstDetail.ddstAge': {
      handler(newValue) {
        this.setChoose(newValue)
      },
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.ddst {
  text-align: center;
  .ddst-button {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    background: #F4F7FF;
    border-radius: 12px;
    overflow: hidden;

    .button-item {
      padding: 5px 12px;
      // margin: 6px;
      font-size: 14px;
      color: #333;
      // background: #F5F5F5;
      // border: 1px solid #999;
      // border-radius: 12px;
      cursor: pointer;
    }

    .choose-button {
      color: #2878FF;
      // background: #2878FF;
      // border: 1px solid #2878FF;
    }
  }

  .ddst-content {
    display: flex;
    padding-top: 20px;

    .content-item {
      flex: 1;
      padding: 0 8px;
      border-right: 1px dashed #eee;

      .item-name {
        font-size: 14px;
        text-align: center;
      }

      .item-question {
        padding: 10px 0;

        .question-name {
          font-size: 12px;
          text-align: left;
        }

        .question-answer {
          display: flex;
          flex-wrap: wrap;

          .answer-item {
            display: flex;
            padding: 10px;

            .ant-btn {
              height: 28px;
              line-height: 28px;
              margin-right: 10px;
              font-size: 12px;
              color: #1890ff;
              background: #fff;
              border: 1px solid #1890ff;
            }

            .choose-btn {
              color: #fff;
              background: #1890ff;
            }

            .item-text {
              display: inline-block;
              font-size: 12px;
              line-height: 28px;
              cursor: pointer;
            }
          }

          .disable-item {
            color: #aaa;
            cursor: not-allowed;

            .ant-btn {
              color: #aaa;
              background: #f5f5f5;
              border-color: #d9d9d9;
            }
          }
        }
      }

      .optional-question {
        margin-top: 20px;
        padding: 0 12px;
        background: #eee;
        border-radius: 8px;

        .title {
          padding-top: 22px;
        }
      }
    }

    .content-item:last-child {
      border: none;
    }
  }

  .confirm-btn {
    height: 32px;
    margin-top: 20px;
    line-height: 32px;
  }
}
</style>

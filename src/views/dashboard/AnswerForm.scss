.interact-canvas{
  .interact-canvas-box{
    display: flex;
    position: relative;
    height: 550px;

    .interact-canvas-image{
      height: 480px;
      position: absolute;
      right: 80%;
      // left: -500px;
    }
    .interact-canvas-content{
      left: 50%;
      position: absolute;
    }
  }
  .interact-canvas-clear-btn{
    // border: ;
    background-color: #448ef7;
    color: #fff;
    width: min-content;
    padding: 10px;
    white-space: nowrap;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 2;
  }
  .interact_type_canvas {
    box-shadow: 0px 1px 10px #777;
    max-width: 480px;
    max-height: 480px;
  }
  .values{
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 0;
    left: 30%;
    .label{
      white-space: nowrap;
    }
  }
}
.interact-number-box{
  height: 550px;
  .interact-buttons{
    margin-bottom: 100px;
  }
  .interact-answer{
    padding-left: 50px;
  }
}
.interact-answer-action {
  border-radius: 20px;
  padding: 7px 15px;
  border: 1px solid #dcdfe6;
  margin-left: 20px;
}
.interact-answer-input {
  width: 45px;
  height: 43px;
  border-bottom: 2px solid #979797;
  display: inline-block;
  margin: 10px 19px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.interact-answer-label {
}
.interact-answer {
  display: flex;
  align-items: center;
}
.interact-buttons {
  display: flex;
  flex-wrap: wrap;
  width: 420px;
}
.interact-button {
  border: 0;
  width: 79px;
  height: 79px;
  background: linear-gradient(315deg, #86e720, #78c824);
  margin: 20px 30px;
  border-radius: 50%;
  color: #fff;
  font-size: 36px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.interact-button:hover {
  background: linear-gradient(315deg, #488d00, #63c100);
}
#components-layout-demo-top .logo {
  width: 120px;
  height: 31px;
  /*background: rgba(255, 255, 255, .2);*/
  margin: 16px 24px 16px 0;
  float: left;
}

.answer-header {
  background: #4495d1;
  height: 100px;
}

.mearsure-title {
  right: 0;
  /*width: 1080px;*/
  /*height: 20px;*/
  text-align: right;
  color: #fff;
  font-size: 20px;
  font-weight: bold;
}

.description {
  margin: 50px auto 0;
  width: 750px;
  line-height: 45px;
  font-size: 28px;
  color: #333333;
  overflow: hidden;
  font-family: '楷体', Arial;
  text-indent: 56px;
}

.description2 {
  margin: 50px auto 0;
  width: 950px;
  line-height: 45px;
  font-size: 28px;
  color: #333333;
  overflow: hidden;
  font-family: '楷体', Arial;
  text-indent: 56px;
}

.descriptionMobile {
  margin: 50px auto 0;
  line-height: 45px;
  font-size: 28px;
  color: #333333;
  overflow: hidden;
  font-family: '楷体', Arial;
  text-indent: 56px;
}

.descriptionImg {
  margin-left: 30%;
  width: 750px;
  height: 508px;
  overflow: hidden;
}

.footer {
  height: 100px;
  width: 100%;
  /*position: fixed;*/
  /*bottom: 30%;*/
  padding: 0 30%;
}

.timer {
  color: rgba(0, 0, 0, 0.85);
  font-size: 24px;
  font-family: Tahoma, 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
    'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  float: right;
  margin-right: 15px;
  margin-left: 15px;
}

.optionDiv {
  /*float: left;*/
  height: 100px;
  /*width: calc(25% - 20px);*/
  margin-right: 30px;
  /*left: 25%;*/
  position: relative;
  min-width: var(--textLength);
}

.optionDiv > img {
  border: 2px solid #e8e8e8;
  background-color: #ededed;
}

.optionDiv span {
  white-space: nowrap;
  line-height: 40px;
  font-family: '楷体', Arial;
  font-size: 25px;
}

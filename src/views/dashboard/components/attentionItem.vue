<template>
  <div class="attention-item-view">
    <a-back-top />
    <p class="digital-breadth-title" v-html="answerTitle[(questionNum - 1)]"></p>

    <div class="attention-random-view">
      <div :class="['random-num', item.choose && 'has-choose']" v-for="(item, index) of randomNumbers" :key="index" @click="chooseItem(item, index)">{{ item.number }}</div>
    </div>
    <!-- <div class="footer-btn" v-if="isShowNextPage">
      <a-button v-if="questionNum == 5" style="height: 30px" @click="nextPage" type="primary">提交</a-button>
      <a-button v-else style="height: 30px" @click="nextPage" type="primary">
        下一题
        <a-icon type="right"/>
      </a-button>
    </div> -->
  </div>
</template>

<script>
import {postAction} from '@/api/manage'
export default {
  name: 'AttentionItem',

  props: {
    userId: {
      type: String,
      default: ''
    },

    measure: {
      type: Object,
      default: () => {}
    },
    
    resultId: {
      type: String,
      default: ''
    },

    time: {
      type: String | Number,
      default: ''
    },
  },

  data() {
    return {
      answerTitle: [
        '第一部分: 划去3. 在屏幕上只要出现3, 就将这个3划去',
        '第二部分: 划去3前面的数. 在屏幕上只要出现3, 就将这个3前面的一个数字划去,如: 43就将数字4划去',
        '第三部分: 划去3前面的7. 在屏幕上只要连续出现73,就将7划去,如: 573 就将7划去',
        '第四部分: 划去3,7之间的数. 在屏幕上只要出现3*7或7*3, 就将*划去,如: 397 就将9划去,723就将2划去, 773就将第2个7划去',
        '第五部分: 划去3,7之间的偶数. 在屏幕上只要出现3*7或7*3, 且*为偶数,就将*划去,如: 367将6划去, 783将8划去'
      ],
      timer: null,

      questionNum: 1,
      randomNumbers: [], // 初始化空数组
      answerIndexs: [], // 正确答案数字位置
      correctIndexs: [], // 选择正确数组位置
      wrongIndexs: [], // 选择错误数组位置
      heightIndex: 0, //选择最高的位置

      loading: false,
      // isShowNextPage: false,

      url: '/diagnosis/dxResult/uploadResultForZYLCY',
      resultArray: [],
    }
  },
  mounted() {
    this.generateRandomNumbers()
  },
  methods: {
    generateRandomNumbers1() {
      // 初始化状态变量
      this.randomNumbers = [];
      this.answerIndexs = [];
      this.correctIndexs = [];
      this.wrongIndexs = [];
      let randomArr = [];
      let correctArr = [];

      // 生成64组数据
      for(let part = 0; part < 64; part++) {
        let randomArrPart = [];
        // 取0-24随机数，作为位置
        let randomPosition = Math.floor(Math.random() * 25);

        // 生成24个随机数字（不包含3）
        for (let i = 0; i < 24; i++) {
          let randomNum = this.getRandomNone3();
          randomArrPart.push({
            number: randomNum,
            choose: false,
          });
        }

        // 插入数字3
        let num1 = { number: 3, choose: false };
        randomArrPart.splice(randomPosition, 0, num1);

        // 验证数组长度是否符合预期
        if (randomArrPart.length !== 25) {
          console.error(`Unexpected array length: ${randomArrPart.length} at part ${part}`);
        }

        // 添加到结果数组
        randomArr.push(...randomArrPart);
        correctArr.push(randomPosition + 25 * part);
      }

      // 异步更新DOM
      this.$nextTick(() => {
        this.randomNumbers = randomArr;
        this.answerIndexs = correctArr;
        this.refreshTimer();
      });
    },
    generateRandomNumbers2() {
      // 初始化状态变量
      this.randomNumbers = [];
      this.answerIndexs = [];
      this.correctIndexs = [];
      this.wrongIndexs = [];
      let randomArr = [];
      let correctArr = [];

      // 生成64组数据
      for(let part = 0; part < 64; part++) {
        let randomArrPart = [];
        // 取0-23随机数，作为位置
        let randomPosition = Math.floor(Math.random() * 24);

        // 生成24个随机数字（不包含3）
        for (let i = 0; i < 24; i++) {
          let randomNum = this.getRandomNone3();
          randomArrPart.push({
            number: randomNum,
            choose: false,
          });
        }

        // 插入数字3
        // 确保插入位置有效（randomPosition + 1 不超过数组长度）
        const insertPosition = Math.min(randomPosition + 1, randomArrPart.length);
        let num1 = { number: 3, choose: false };
        randomArrPart.splice(insertPosition, 0, num1);

        // 验证数组长度是否符合预期
        if (randomArrPart.length !== 25) {
          console.error(`Unexpected array length: ${randomArrPart.length} at part ${part}`);
        }

        // 添加到结果数组
        randomArr.push(...randomArrPart);
        correctArr.push(randomPosition + 25 * part);
      }

      // 异步更新DOM
      this.$nextTick(() => {
        this.randomNumbers = randomArr;
        this.answerIndexs = correctArr;
        this.refreshTimer();
      });
    },
    generateRandomNumbers3() {
      // 初始化状态变量
      this.randomNumbers = [];
      this.answerIndexs = [];
      this.correctIndexs = [];
      this.wrongIndexs = [];
      let randomArr = [];
      let correctArr = [];

      // 生成32组数据
      for(let part = 0; part < 32; part++) {
        let randomArrPart = [];
        // 取0-47随机数，作为位置
        let randomPosition = Math.floor(Math.random() * 48);

        // 生成48个随机数字，确保7和3不相邻
        for (let i = 0; i < 48; i++) {
          let randomNum = Math.floor(Math.random() * 10);
          let index = i - 1;

          // 检查前一个数字是否为7，避免形成73的模式
          if (i > 0) {
            if (randomArrPart[index].number === 7) {
              randomNum = this.getRandomNone3();
            }
          } else if (i === 0 && part > 0) {
            // 检查前一组数据的最后一个数字
            const prevPartIndex = part * 50 + index;

            if (prevPartIndex >= 0 && prevPartIndex < randomArr.length) {
              if (randomArr[prevPartIndex].number === 7) {
                randomNum = this.getRandomNone3();
              }
            }
          }

          randomArrPart.push({
            number: randomNum,
            choose: false,
          });
        }

        // 插入符合条件的73序列
        let num1 = { number: 7, choose: false };
        let num2 = { number: 3, choose: false };
        randomArrPart.splice(randomPosition, 0, ...[num1, num2]);

        // 验证数组长度是否符合预期
        if (randomArrPart.length !== 50) {
          console.error(`Unexpected array length: ${randomArrPart.length} at part ${part}`);
        }

        // 添加到结果数组
        randomArr.push(...randomArrPart);
        correctArr.push(randomPosition + 50 * part);
      }

      // 异步更新DOM
      this.$nextTick(() => {
        this.randomNumbers = randomArr;
        this.answerIndexs = correctArr;
        this.refreshTimer();
      });
    },
    generateRandomNumbers4() {
      // 初始化状态变量
      this.randomNumbers = [];
      this.answerIndexs = [];
      this.correctIndexs = [];
      this.wrongIndexs = [];
      let randomArr = [];
      let correctArr = [];

      // 生成64组数据
      for(let part = 0; part < 64; part++) {
        let randomArrPart = [];
        // 取0-21随机数，作为位置
        let randomPosition = Math.floor(Math.random() * 22);

        // 生成22个随机数字
        for (let i = 0; i < 22; i++) {
          let randomNum = Math.floor(Math.random() * 10);
          let index = i - 2;

          // 检查前两个数字是否为7或3，避免形成3*7或7*3的模式
          if (i > 1) {
            if (randomArrPart[index].number === 7) {
              randomNum = this.getRandomNone3();
            } else if (randomArrPart[index].number === 3) {
              randomNum = this.getRandomNone7();
            }
          } else if ((i === 0 || i === 1) && part > 0) {
            // 检查前一组数据的对应位置
            const prevPartIndex = part * 25 + index;

            if (prevPartIndex >= 0 && prevPartIndex < randomArr.length) {
              if (randomArr[prevPartIndex].number === 7) {
                randomNum = this.getRandomNone3();
              } else if (randomArr[prevPartIndex].number === 3) {
                randomNum = this.getRandomNone7();
              }
            }
          }

          randomArrPart.push({
            number: randomNum,
            choose: false,
          });
        }

        // 生成符合条件的3*7或7*3序列
        let random37 = this.getRandom37();
        let random73 = this.getRandom73(random37);
        let randomNone37 = this.getRandomNone37();
        let num1 = { number: random37, choose: false };
        let num2 = { number: randomNone37, choose: false };
        let num3 = { number: random73, choose: false };

        // 插入符合条件的序列
        randomArrPart.splice(randomPosition, 0, ...[num1, num2, num3]);

        // 修正新增3*7或7*3前后2位数字，避免连续出现特殊模式
        if (randomPosition < 2) {
          if (part > 0) {
            const prevIndex = part * 25 + randomPosition - 2;
            if (prevIndex >= 0 && prevIndex < randomArr.length) {
              randomArr[prevIndex].number = this.getRandomNone37();
            }

            if (randomPosition + 4 < randomArrPart.length) {
              randomArrPart[randomPosition + 4].number = this.getRandomNone37();
            }
          }
        } else if (randomPosition >= 2 && randomPosition < 20) {
          // 确保索引在有效范围内
          if (randomPosition - 2 >= 0) {
            randomArrPart[randomPosition - 2].number = this.getRandomNone37();
          }

          if (randomPosition + 4 < randomArrPart.length) {
            randomArrPart[randomPosition + 4].number = this.getRandomNone37();
          }
        }

        // 验证数组长度是否符合预期
        if (randomArrPart.length !== 25) {
          console.error(`Unexpected array length: ${randomArrPart.length} at part ${part}`);
        }

        // 添加到结果数组
        randomArr.push(...randomArrPart);
        correctArr.push(randomPosition + 1 + 25 * part);
      }

      // 异步更新DOM
      this.$nextTick(() => {
        this.randomNumbers = randomArr;
        this.answerIndexs = correctArr;
        this.refreshTimer();
      });
    },
    /**
     * 生成随机数字序列，满足特定条件（3*7或7*3，其中*为偶数）
     */
    generateRandomNumbers5() {
      // 初始化状态变量
      this.randomNumbers = [];
      this.answerIndexs = [];
      this.correctIndexs = [];
      this.wrongIndexs = [];
      let randomArr = [];
      let correctArr = [];

      // 生成32组数据
      for(let part = 0; part < 32; part++) {
        let randomArrPart = [];
        // 取0-46随机数，作为位置
        let randomPosition = Math.floor(Math.random() * 47);

        // 生成47个随机数字
        for (let i = 0; i < 47; i++) {
          let randomNum = Math.floor(Math.random() * 10);
          let index2 = i - 2;
          let index1 = i - 1;

          // 检查前两个数字是否满足3*7或7*3的模式（*为偶数）
          if (i > 1) {
            if (randomArrPart[index2].number === 7 && this.isEvenNum(randomArrPart[index1].number)) {
              randomNum = this.getRandomNone3();
            } else if (randomArrPart[index2].number === 3 && this.isEvenNum(randomArrPart[index1].number)) {
              randomNum = this.getRandomNone7();
            }
          } else if (i === 0 && part > 0) {
            // 检查前一组数据的最后两个数字
            const prevPartIndex2 = part * 50 - 2;
            const prevPartIndex1 = part * 50 - 1;

            if (prevPartIndex2 >= 0 && prevPartIndex1 >= 0 &&
              prevPartIndex2 < randomArr.length && prevPartIndex1 < randomArr.length) {
              if (randomArr[prevPartIndex2].number === 7 && this.isEvenNum(randomArr[prevPartIndex1].number)) {
                randomNum = this.getRandomNone3();
              } else if (randomArr[prevPartIndex2].number === 3 && this.isEvenNum(randomArr[prevPartIndex1].number)) {
                randomNum = this.getRandomNone7();
              }
            }
          } else if (i === 1 && part > 0) {
            // 检查前一组数据的最后一个数字和当前组的第一个数字
            const prevPartIndex2 = part * 50 - 1;

            if (prevPartIndex2 >= 0 && prevPartIndex2 < randomArr.length) {
              if (randomArr[prevPartIndex2].number === 7 && this.isEvenNum(randomArrPart[index1].number)) {
                randomNum = this.getRandomNone3();
              } else if (randomArr[prevPartIndex2].number === 3 && this.isEvenNum(randomArrPart[index1].number)) {
                randomNum = this.getRandomNone7();
              }
            }
          }

          randomArrPart.push({
            number: randomNum,
            choose: false,
          });
        }

        // 生成符合条件的3*7或7*3序列
        let random37 = this.getRandom37();
        let random73 = this.getRandom73(random37);
        let randomEven = this.getRandomEven();
        let num1 = { number: random37, choose: false };
        let num2 = { number: randomEven, choose: false };
        let num3 = { number: random73, choose: false };

        // 插入符合条件的序列
        randomArrPart.splice(randomPosition, 0, ...[num1, num2, num3]);

        // 修正新增3*7或7*3前后2位数字，避免连续出现特殊模式
        if (randomPosition < 2) {
          if (part > 0) {
            const prevIndex = part * 50 + randomPosition - 2;
            if (prevIndex >= 0 && prevIndex < randomArr.length) {
              randomArr[prevIndex].number = this.getRandomNone37();
            }

            if (randomPosition + 4 < randomArrPart.length) {
              randomArrPart[randomPosition + 4].number = this.getRandomNone37();
            }
          }
        } else if (randomPosition >= 2 && randomPosition < 45) {
          // 确保索引在有效范围内
          if (randomPosition - 2 >= 0) {
            randomArrPart[randomPosition - 2].number = this.getRandomNone37();
          }

          if (randomPosition + 4 < randomArrPart.length) {
            randomArrPart[randomPosition + 4].number = this.getRandomNone37();
          }
        }

        // 验证数组长度是否符合预期
        if (randomArrPart.length !== 50) {
          console.error(`Unexpected array length: ${randomArrPart.length} at part ${part}`);
        }

        // 添加到结果数组
        randomArr.push(...randomArrPart);
        correctArr.push(randomPosition + 1 + 50 * part);
      }

      // 异步更新DOM
      this.$nextTick(() => {
        this.randomNumbers = randomArr;
        this.answerIndexs = correctArr;
        this.refreshTimer();
      });
    },
    generateRandomNumbers() {
      if (this.questionNum == 1) {
        this.generateRandomNumbers1()
      } else if (this.questionNum == 2) {
        this.generateRandomNumbers2()
      } else if (this.questionNum == 3) {
        this.generateRandomNumbers3()
      } else if (this.questionNum == 4) {
        this.generateRandomNumbers4()
      } else if (this.questionNum == 5) {
        this.generateRandomNumbers5()
      }
    },

    chooseItem(item, index) {
      item.choose = !item.choose
      if (item.choose == false) {
        // 取消选中，从数组移除
        let correctI = this.correctIndexs.indexOf(index)
        let wrongI = this.wrongIndexs.indexOf(index)
        if (correctI != -1) {
          this.correctIndexs.splice(correctI, 1)
        }
        if (wrongI != -1) {
          this.wrongIndexs.splice(wrongI, 1)
        }
        return
      }
      // 赋值选择最高位置
      if (index > this.heightIndex) {
        this.heightIndex = index
      }
      if (this.answerIndexs.indexOf(index) === -1) {
        this.wrongIndexs.push(index)
      } else {
        this.correctIndexs.push(index)
      }
    },
// 1. 修改refreshTimer方法，针对性处理第五部分
    refreshTimer() {
      let that = this;
      // 清除旧定时器，避免重复
      if (this.timer) clearTimeout(this.timer);

      this.timer = setTimeout(() => {
        let content = '该部分已结束，点击进行下一部分';
        const isLastPart = that.questionNum >= 5;
        if (isLastPart) {
          content = '第五部分已结束，点击完成进行提交';
        }

        // 存储提示框实例
        const infoInstance = that.$info({
          title: '提示',
          content: content,
          okText: '确定',
          keyboard: false,
          onOk() {
            that.nextPage();
          }
        });

        // ******** 仅对第五部分添加自动提交逻辑 ********
        if (isLastPart) {
          // 提示用户即将自动提交
          content += '（10秒后将自动提交，无需操作）';
          infoInstance.update({ content }); // 更新提示内容

          // 10秒后强制提交（无论用户是否点击）
          const forceCommitTimer = setTimeout(() => {
            // 关闭提示框（如果存在）
            if (infoInstance) infoInstance.close();
            // 直接触发提交（跳过nextPage，避免中间逻辑干扰）
            that.commit();
          }, 10000);

          // 防止提交后定时器仍执行
          that.$once('hook:beforeDestroy', () => {
            clearTimeout(forceCommitTimer);
          });
        }
      }, 180000); // 3分钟超时
    },

// 2. 强化nextPage中第五部分的提交可靠性
    nextPage() {
      // 清除当前定时器
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }

      // 收集当前部分数据
      let omitNum = this.calculateOmitNum();
      this.resultArray.push({
        questionNum: this.questionNum,
        totalNum: 1600,
        correctNum: this.correctIndexs.length,
        wrongNum: this.wrongIndexs.length,
        omitNum: omitNum,
      });

      // 第五部分直接提交（移除多余逻辑，确保提交优先）
      if (this.questionNum >= 5) {
        // 强制提交，忽略中间状态
        this.commit();
        return;
      }

      // 其他部分保持原有逻辑
      this.questionNum++;
      this.heightIndex = 0;
      this.$emit('pageChange', this.questionNum + '/5');
      this.generateRandomNumbers();
    },

// 3. 优化commit方法，确保提交成功
    commit() {
      if (this.loading) return; // 防止重复提交

      this.loading = true;
      const params = {
        userId: this.userId,
        measureId: this.measure.id,
        resultId: this.resultId,
        time: new Date() - this.time, // 计算总耗时
        result: this.resultArray,
      };

      // 无论接口成功/失败，都标记流程结束（避免死循环）
      postAction(this.url, params).then(res => {
        if (res.success) {
          this.$emit('ok'); // 通知父组件提交成功
          this.$message.success('提交成功');
        } else {
          this.$message.error('提交失败，已自动重试');
          // 失败时重试一次（极端情况保险）
          setTimeout(() => postAction(this.url, params), 1000);
        }
      }).catch(() => {
        this.$message.error('网络异常，已自动保存结果');
        // 网络异常时，尝试本地存储（如果有需求）
        localStorage.setItem('emergencySubmit', JSON.stringify(params));
      }).finally(() => {
        this.loading = false;
      });
    },

// 4. 组件销毁前强制检查第五部分状态
    beforeDestroy() {
      // 如果是第五部分且未提交，强制提交
      if (this.questionNum >= 5 && this.resultArray.length < 5) {
        this.commit();
      }
      // 清理定时器
      if (this.timer) clearTimeout(this.timer);
    },

    /**
     * 计算漏答 (计算位置为开始到最高选择处即0-heightIndex)
     */
    calculateOmitNum() {
      // 获取最高选择处之前的所有正确答案
      console.log('heightIndex=====', this.heightIndex)

      let heightAnswerIndexs = []
      this.answerIndexs.forEach((element, index) => {
        if (element < this.heightIndex) {
          heightAnswerIndexs.push(element)
        }
      });
      let omitNum = heightAnswerIndexs.length - this.correctIndexs.length
      if (this.correctIndexs.indexOf(this.heightIndex) === -1) {
        return omitNum
      } else {
        return omitNum + 1
      }
      
    },

    // 随机3或7
    getRandom37() {
      let arr = [3, 7]
      let randomNum = Math.floor(Math.random() * 2) // 生成范围在0到1之间的随机数
      return arr[randomNum]
    },
    // 反向取3,7 即num=3输出7；num=7输出3
    getRandom73(num) {
      if (num == 3) {
        return 7
      }
      return 3
    },
    // 非3随机数
    getRandomNone3() {
      let randomNum = Math.floor(Math.random() * 10) // 生成范围在0到9之间的随机数
      if (randomNum == 3) {
        return this.getRandomNone3()
      }
      return randomNum
    },
    // 非7随机数
    getRandomNone7() {
      let randomNum = Math.floor(Math.random() * 10) // 生成范围在0到9之间的随机数
      if (randomNum == 7) {
        return this.getRandomNone7()
      }
      return randomNum
    },
    // 非3非7随机数
    getRandomNone37() {
      let randomNum = Math.floor(Math.random() * 10) // 生成范围在0到9之间的随机数
      if (randomNum == 3 || randomNum == 7) {
        return this.getRandomNone37()
      }
      return randomNum
    },
    // 偶数随机数
    getRandomEven() {
      let randomNum = Math.floor(Math.random() * 5)
      let evenArr = [0, 2, 4, 6, 8]
      return evenArr[randomNum]
    },
    // 奇数随机数
    getRandomOdd() {
      let randomNum = Math.floor(Math.random() * 5)
      let oddArr = [1, 3, 5, 7, 9]
      return oddArr[randomNum]
    },
    isEvenNum(num) {
      let evenArr = [0, 2, 4, 6, 8]
      if (evenArr.indexOf(num) === -1) {
        return false
      }
      return true
    }
  },
  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style lang="scss">
.attention-item-view {
  padding-left: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .digital-breadth-title {
    width: 100%;
    font-size: 28px;
    padding-bottom: 20px;
    margin: 0;
  }

  .attention-random-view {
    display: flex;
    flex-wrap: wrap;
    padding: 0 50px 50px 50px;
    overflow: auto;

    .random-num {
      font-size: 36px;
      padding: 0 2px;
    }
    .has-choose {
      color: red;
      text-decoration: line-through;
    }
  }
  .footer-btn {
    padding: 20px 0;
    .no-choose {
      cursor: not-allowed;
    }
  }
  
}
</style>
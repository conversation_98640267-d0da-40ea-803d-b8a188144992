<template>
  <div class="iva-item">
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>

    <div class="iva-top">
      <p class="top-label">倒计时统计</p>
      <p class="top-info">
        <span class="info1">距离本组结束:{{ formattedTime }}</span>
        <span class="info2">正确:{{ correct }}</span>
        <span class="info2">错误:{{ error }}</span>
        <span class="info2">遗漏:{{ miss }}</span>
        <span class="info3">反映时长:{{ reflectTime }}(MS)</span>
      </p>
    </div>

    <div class="iva-content">
      <p class="content-label" v-if="(level === 1 && (questionList[questionIndex] === undefined || questionList[questionIndex] !== undefined && !questionList[questionIndex].length)) || (level === 3 && (questionList[questionIndex] === undefined || questionList[questionIndex] && questionList[questionIndex].type === 'V'))">
        显示数字为<span class="num">4</span>时，按鼠标左键点击数字
      </p>
      <p class="content-label" v-else-if="(level === 1 && questionList[questionIndex] !== undefined && questionList[questionIndex].length)">
        在<span class="num">4</span>上，按鼠标左键点击数字
      </p>
      <p class="content-label" v-else-if="(level === 2 || (level === 3 && questionList[questionIndex] !== undefined && questionList[questionIndex].type === 'A'))">
        当听到数字<span class="num">4</span>时，按鼠标左键点击靶台
      </p>
      <!-- <p class="content-label" v-else-if="level === 3">
        当听到的数字和显示的数字相同时，按鼠标左键点击数字
      </p> -->

      <div v-if="(level === 1 && questionList[questionIndex] !== undefined && !questionList[questionIndex].length) || 
                 (level === 3 && questionList[questionIndex] && questionList[questionIndex].type === 'V')" 
                 class="big-num" @click="handleClick()">{{ questionList[questionIndex].num || questionList[questionIndex] }}</div>
      <template v-if="(level === 1 && questionList[questionIndex] !== undefined && questionList[questionIndex].length)">
        <div class="small-num" v-for="item in questionList[questionIndex]" :key="item" @click="handleClick(item)">{{ item }}</div>
      </template>
      <img v-if="(level === 2 || (level === 3 && questionList[questionIndex] !== undefined && questionList[questionIndex].type === 'A'))" class="img" src="../../../assets/img/target_platform.png" @click="handleClick()" >
    </div>

    <a-button v-if="!formattedTime" class="start-btn" type="primary" @click="handleStart">开始</a-button>
  </div>
</template>

<script>
import api from '@/game_models/utils/common'

export default {
  name: 'IVAItem',

  props: {
    ivaMode: {
      type: String,
      default: ''
    },
  },

  data() {
    return {
      level: 0, // 第一关：视觉 第二关：听觉 第三关：视听
      type: 0, // 0-单数字 1-多数字
      minutes: 6.25, // 初始分钟数
      formattedTime: '', // 格式化后的时间
      correct: 0, // 正确次数
      error: 0, // 错误次数
      miss: 0, // 遗漏次数
      startTime: 0,
      endTime: 0,
      reflectTime: 0, // 反映时长
      title: '',
      targetNum: 0, // 目标数
      randomNum: 0, // 随机数
      randomArray: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], // 随机数组
      questionList: [],
      questionIndex: 0,
      choice: '', // 作答情况
      answer: [],

      musicUrl: '',
      isPlay: false,
      isClick: false,

      intervalId: null,
      timer: null,
    }
  },

  mounted() {
    this.level = this.ivaMode === 'V' ? 0 : this.ivaMode === 'A' ? 1 : 2
    this.level++
    this.initData()

    // 全量表
    // this.start()
  },

  destroyed() {
    clearInterval(this.timer)
    clearInterval(this.intervalId)
  },

  methods: {
    handleStart() {
      this.startTimer()
      this.startProcess()
    },
    
    start() {
      this.level++
      this.initData()

      if (this.level > 3) {
        this.submit()
      } else if (this.level > 1) {
        this.$confirm({
          title: this.level === 2 ?'视觉注意力测试完成，即将进入听觉注意力测试，点进“确认”开始' : '听觉注意力测试完成，即将进入视听觉组合注意力测试，点进“确认”开始',
          onOk: () => {
            clearInterval(this.timer)
            this.startTimer()
            this.startProcess()
          },
          onCancel() {
          },
          // class: 'test',
        });
      } else {
        this.startTimer()
        this.startProcess()
      }
    },
    // 6.25分钟倒计时
    startTimer() {
      let minutes = this.level >= 3 ? 12.5 : 6.25; // 初始分钟数
      let seconds = minutes * 60 - 1;
      this.formattedTime = this.level >= 3 ? '12:30' : '06:15';
      this.intervalId = setInterval(() => {
        minutes = Math.floor(seconds / 60);

        // 格式化输出，如果分钟或秒数小于10，则前面加0
        this.formattedTime = `${minutes < 10 ? '0' + minutes : minutes}:${(seconds % 60) < 10 ? '0' + (seconds % 60) : (seconds % 60)}`;

        seconds--

        if (seconds < 0) { // 当时间到达0时清除计时器
          clearInterval(this.intervalId);
        }
      }, 1000);
    },

    initData() {
      this.questionList = []
      this.questionIndex = 0
      if (this.level === 1) {
        for(let i = 0; i < 5; i++) {
          let targetList = [] // 目标组
          let interfererList = [] // 干扰组

          for(let j = 0; j < 50; j++) {
            if (j < 12) {
              targetList.push(4)
            } else if (j < 21) {
              const list = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
              targetList.push(api.shuffle(list))
            } else if (j < 25) {
              const list = [0, 1, 2, 3, 5, 6, 7, 8, 9]
              targetList.push(api.getRandomArray(list, 1)[0])
            } else if (j < 28) {
              interfererList.push(4)
            } else if (j < 29) {
              const list = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
              interfererList.push(api.shuffle(list))
            } else {
              const list = [0, 1, 2, 3, 5, 6, 7, 8, 9]
              interfererList.push(api.getRandomArray(list, 1)[0])
            }
          }
          
          const allList = api.shuffle(targetList).concat(api.shuffle(interfererList))
          this.questionList = this.questionList.concat(allList)
        }
      } else if (this.level === 2) {
        for(let i = 0; i < 5; i++) {
          let targetList = [] // 目标组
          let interfererList = [] // 干扰组

          for(let j = 0; j < 50; j++) {
            if (j < 21) {
              targetList.push(4)
            } else if (j < 25) {
              const list = [0, 1, 2, 3, 5, 6, 7, 8, 9]
              targetList.push(api.getRandomArray(list, 1)[0])
            } else if (j < 29) {
              interfererList.push(4)
            } else {
              const list = [0, 1, 2, 3, 5, 6, 7, 8, 9]
              interfererList.push(api.getRandomArray(list, 1)[0])
            }
          }
          
          const allList = api.shuffle(targetList).concat(api.shuffle(interfererList))
          this.questionList = this.questionList.concat(allList)
        }
      } else {
        for(let i = 0; i < 5; i++) {
          let numList = []
          let targetList = [] // 目标组
          let interfererList = [] // 干扰组
          let targetType = [] // 目标类型
          let interfererType = [] // 干扰类型

          for(let j = 0; j < 100; j++) {
            if (j < 42) {
              targetList.push(4)
            } else if (j < 50) {
              const list = [0, 1, 2, 3, 5, 6, 7, 8, 9]
              targetList.push(api.getRandomArray(list, 1)[0])
            } else if (j < 58) {
              interfererList.push(4)
            } else {
              const list = [0, 1, 2, 3, 5, 6, 7, 8, 9]
              interfererList.push(api.getRandomArray(list, 1)[0])
            }

            if (j < 25) {
              targetType.push('V')
            } else if (j < 50) {
              targetType.push('A')
            } else if (j < 75) {
              interfererType.push('V')
            } else {
              interfererType.push('A')
            }
          }
          
          const allNumList = api.shuffle(targetList).concat(api.shuffle(interfererList))
          const allTypeList = api.shuffle(targetType).concat(api.shuffle(interfererType))
          for(let k = 0; k < 100; k++) {
            numList.push({
              num: allNumList[k],
              type: allTypeList[k]
            })
          }

          this.questionList = this.questionList.concat(numList)
        }
      }
    },

    startProcess() {
      if (this.level === 2 || (this.level === 3 && this.questionList[this.questionIndex].type === 'A')) {
          this.playAudio(`/static/game_assets/audio/game22/audio_${this.questionList[this.questionIndex].num || this.questionList[this.questionIndex]}.mp3`)
        }
      this.timer = setInterval(() => {
        if (this.level <= 2 && (this.questionList[this.questionIndex].length || this.questionList[this.questionIndex] === 4 || this.choice)) {
          if (!this.choice) {
            this.miss++
          }
          
          this.answer.push(
            {
              questionNum: this.questionIndex,      //非必填，暂时未用的： 题号（1，2，3，4，5，6，7，8...等，如果碰到干扰类型的题，用户没点击的话，题号可能会跳过）
              set: Math.ceil((this.questionIndex + 1) / 50),              //必填，  测试组，一共5组（1，2，3，4，5）
              blockType: (Math.ceil((this.questionIndex + 1) / 25) % 2) ? 'F' : 'R',	   //必填，  亚组类型(F-目标模块;R-干扰模块)
              mode: this.level === 1 ? 'V' : 'A',		   //必填，  模式（A-听觉模式，V-视觉模式）
              // value: this.questionList[this.questionIndex],            //非必填，显示的数值(1或2)
              reactionTime: this.reflectTime || 1500,   //必填，  点击反应时间，毫秒ms
              choice: this.choice ? Number(this.choice) : 2 		   //必填，  作答情况（0-错误，1-正确，2-遗漏）
            }
          )
        } else if (this.level === 3 && (this.questionList[this.questionIndex].num === 4 || this.choice)) {
          if (!this.choice) {
            this.miss++
          }
          
          this.answer.push(
            {
              questionNum: this.questionIndex,      //非必填，暂时未用的： 题号（1，2，3，4，5，6，7，8...等，如果碰到干扰类型的题，用户没点击的话，题号可能会跳过）
              set: Math.ceil((this.questionIndex + 1) / 50),              //必填，  测试组，一共5组（1，2，3，4，5）
              blockType: (Math.ceil((this.questionIndex + 1) / 25) % 2) ? 'F' : 'R',	   //必填，  亚组类型(F-目标模块;R-干扰模块)
              mode: this.questionList[this.questionIndex].type,		   //必填，  模式（A-听觉模式，V-视觉模式）
              // value: this.questionList[this.questionIndex].num,            //非必填，显示的数值(1或2)
              reactionTime: this.reflectTime || 1500,   //必填，  点击反应时间，毫秒ms
              choice: this.choice ? Number(this.choice) : 2 		   //必填，  作答情况（0-错误，1-正确，2-遗漏）
            }
          )
        }

        if (this.questionIndex >= this.questionList.length - 1) {
          clearInterval(this.timer)
          this.submit()
          // 全量表
          // this.start()
        }

        this.reflectTime = 0
        this.choice = ''

        this.isClick = false
        const index = this.questionIndex + 1
        this.questionIndex = -1
        if (this.level === 2 || (this.level === 3 && this.questionList[index].type === 'A')) {
          this.playAudio(`/static/game_assets/audio/game22/audio_${this.questionList[index].num || this.questionList[index]}.mp3`)
          this.startTime = new Date().getTime()
          this.questionIndex = index
        } else {
          setTimeout(() => {
            this.startTime = new Date().getTime()
            this.questionIndex = index 
          }, 50)
        }
      }, 1500)
    },

    handleClick(num) {
      if (this.isClick) return
      this.isClick = true
      this.endTime = new Date().getTime()
      const randomNum = num || this.questionList[this.questionIndex].num || this.questionList[this.questionIndex]
      this.reflectTime = this.endTime - this.startTime
      this.choice = randomNum === 4 ? '1' : '0'
      if (this.choice === '0') {
        this.error++
      } else if (this.choice === '1') {
        this.correct++
      } 
    },  

    playAudio(url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$nextTick(() => {
        this.$refs.music.play()
        this.isPlay = true
      })
    },

    pauseAudio() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnded() {
      this.isPlay = false
    },

    submit() {
      this.$emit('submit', {result: this.answer})
    }
  }
}
</script>

<style lang="scss" scoped>
.iva-item {
  display: flex;
  flex-direction: column;
  padding: 0 100px;

  .iva-top {
    position: relative;
    padding: 40px 0;
    display: flex;
    justify-content: center;
    border: 1px solid #ccc;
    border-radius: 12px;

    .top-label {
      position: absolute;
      top: -14px;
      left: 40px;
      padding: 0 8px;
      font-size: 16px;
      line-height: 28px;
      background: #fff;
    }

    .top-info {
      margin: 0;
      font-size: 16px;
      line-height: 28px;
      color: red;

      .info1, .info3 {
        display: inline-block;
        width: 180px;
      }

      .info2 {
        display: inline-block;
        width: 90px;
      }
    }
  }

  .iva-content {
    position: relative;
    height: 200px;
    padding: 20px 0;
    margin-top: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #ccc;
    border-radius: 12px;

    .content-label {
      position: absolute;
      top: -14px;
      left: 40px;
      padding: 0 8px;
      font-size: 16px;
      line-height: 28px;
      background: #fff;

      .num {
        color: red;
      }
    }

    .big-num {
      position: relative;
      width: 120px;
      height: 120px;
      text-align: center;
      line-height: 120px;
      font-size: 84px;
      font-weight: 600;
      background: red;
      color: #fff;
      cursor: pointer;

      &::after {
        position: absolute;
        top: -2px;
        left: -2px;
        content: '';
        width: 126px;
        height: 126px;
        border: 1px solid #ccc;
      }
    }

    .small-num {
      position: relative;
      width: 80px;
      height: 80px;
      margin: 20px 10px;
      text-align: center;
      line-height: 80px;
      font-size: 54px;
      font-weight: 600;
      background: red;
      color: #fff;
      cursor: pointer;

      &::after {
        position: absolute;
        top: -2px;
        left: -2px;
        content: '';
        width: 86px;
        height: 86px;
        border: 1px solid #ccc;
      }
    }

    .img {
      width: 160px;
      cursor: pointer;
    }
  }

  .start-btn {
    margin: 20px auto;
  }
}
</style>
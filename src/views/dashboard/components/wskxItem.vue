<template>
  <div class="wskx-item">
    <div class="page-bg"></div>

    <div class="game-content">
      <div class="title">
        <div class="text">威斯康星卡片测验量表</div>
        <div class="right">
          <div class="top-item">
            <a-icon type="ordered-list"/>
            <span>{{ (ra == 128 ? 128 :(ra+1)) + '/' + 128 }}</span>
          </div>

          <div class="top-item">
            <a-icon type="clock-circle" theme="twoTone"/>
            <span>{{ timer }}</span>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="top">
          <div class="title">卡片区（反应卡片）</div>
          <img class="img" :src="imgSrc" @load="handleMainImageLoad" @error="handleMainImageError" />
        </div>

        <div class="bottom">
          <div class="title">选择区（刺激卡片）</div>
          <div class="bottom-content">
            <div
              v-for="(option, index) in answerOptions"
              :key="index"
              :class="['item', answerList[ra] === option && 'choose-item']"
            >
              <div v-if="imageStatus[option] === 'loading'" class="loading-placeholder">
                <span class="loading-spinner">加载中...</span>
              </div>
              <div v-else-if="imageStatus[option] === 'error'" class="error-placeholder">
                <span>加载失败</span>
                <button class="retry-btn" @click="retryLoadImage(option)">重试</button>
              </div>
              <img
                v-else
                class="img"
                :src="getImageUrl(option)"
                @click="chooseItem(option)"
                @load="handleImageLoad(option)"
                @error="handleImageError(option)"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="mask" v-if="isShowCorrect || isShowError">
        <div class="center">
          <div class="center-content">
            <img v-show="isShowCorrect" class="icon" src="/static/wskx/sucess.png" />
            <img v-show="isShowError" class="icon" src="/static/wskx/error.png" />
            <p class="text">{{ isShowCorrect ? '正确' : isShowError ? '错误' : '' }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import api from '../../../game_models/utils/common'
import {postAction} from '@/api/manage'

export default {
  name: 'WskxItem',

  props: {
    userId: {
      type: String,
      default: ''
    },

    measure: {
      type: Object,
      default: () => {}
    },

    resultId: {
      type: String,
      default: ''
    },

    time: {
      type: String | Number,
      default: ''
    },

    timer: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      answerOptions: ['1_1_1', '2_3_2', '4_2_3', '3_4_4'],
      imageStatus: {
        '1_1_1': 'loading',
        '2_3_2': 'loading',
        '4_2_3': 'loading',
        '3_4_4': 'loading'
      },
      imageRetryCount: {
        '1_1_1': 0,
        '2_3_2': 0,
        '4_2_3': 0,
        '3_4_4': 0
      },
      imageCache: {}, // 缓存图片URL
      type: 0, // 训练模式 0-颜色 1-形状 2-数量
      questionList: [], // 题目列表
      answerList: [],
      imgName: '',
      imgSrc: '',
      mainImageStatus: 'loading',
      correctNum: 0, // 连续答对数量
      rfs: [],

      ra: 0, // 题目序号 总应答数
      cc: 0, // 完成分类数
      rc: 0, // 正确应答数
      re: 0, // 错误应答数
      rcpRatio: 0, // 正确应答百分比
      rf: 0, // 完成第一个分类所需应答数
      rfRatio: 0, // 概念化水平百分数 连续完成3-10个正确应答的总数，占总应答数的百分比
      pe: 0, // 持续性应答数
      rpe: 0, // 持续性错误数
      nrpe: 0, // 非持续性错误 总错误数-持续性错误数
      rpeRatio: 0, // 持续性错误的百分数 总错误数与持续性错误数之差
      fm: 0, // 不能维持完整分类数 连续完成5－9个正确应答的次数

      isShowCorrect: false,
      isShowError: false,
      showNext: false,
      loading: false,

      url: '/diagnosis/dxResult/uploadResultForWCST',
      imgerver: window._CONFIG['domianURL'],
      imageUrl: '',
      imageLoadTimers: {}, // 记录每个图片的加载超时计时器
      imageRetryTimers: {}, // 记录重试计时器
      maxRetries: 3, // 最大重试次数
    }
  },

  computed: {
    // 计算并格式化当前图片URL
    formattedImageUrl() {
      if (!this.imageUrl || this.imageUrl === 'https://undefined') {
        return 'https://img.zhisongkeji.com';
      }
      return this.imageUrl.replace(/\/$/, '');
    }
  },

  watch: {
    // 监听ra变化，更新主图片
    ra() {
      this.updateMainImage();
    }
  },

  mounted() {
    this.initImageUrl();
    this.setData();
    this.updateMainImage();
    this.preloadImages(); // 预加载图片但不触发重复请求
  },

  methods: {
    // 初始化图片URL
    initImageUrl() {
      try {
        this.imageUrl = this.formattedImageUrl;
        console.log('基础图片URL:', this.imageUrl);
        // 初始化图片缓存
        this.answerOptions.forEach(option => {
          this.imageCache[option] = `${this.imageUrl}/static/wskx/${option}.png`;
        });
      } catch (error) {
        console.error('图片URL初始化失败:', error);
        this.imageUrl = 'https://img.zhisongkeji.com';
      }
    },

    // 更新主图片
    updateMainImage() {
      this.imgName = this.questionList[this.ra];
      this.imgSrc = `${this.imageUrl}/static/wskx/${this.imgName}.png`;
      this.mainImageStatus = 'loading';

      // 预加载主图片
      const img = new Image();
      img.onload = () => this.mainImageStatus = 'loaded';
      img.onerror = () => this.mainImageStatus = 'error';
      img.src = this.imgSrc;
    },

    // 预加载所有图片
    preloadImages() {
      this.answerOptions.forEach(option => {
        const img = new Image();
        img.onload = () => this.handleImageLoad(option);
        img.onerror = () => this.handleImageError(option);
        img.src = this.imageCache[option];
      });
    },

    // 获取图片URL（仅在需要时添加时间戳）
    getImageUrl(option) {
      // 仅在重试时添加时间戳，避免正常渲染时重复请求
      if (this.imageRetryCount[option] > 0) {
        return `${this.imageCache[option]}?t=${Date.now()}`;
      }
      return this.imageCache[option];
    },

    // 处理图片加载成功
    handleImageLoad(option) {
      console.log(`图片加载成功: ${option}`);
      clearTimeout(this.imageLoadTimers[option]);
      this.imageStatus[option] = 'loaded';
      this.imageRetryCount[option] = 0;
    },

    // 处理图片加载失败
    handleImageError(option) {
      console.error(`图片加载失败: ${option}`);
      clearTimeout(this.imageLoadTimers[option]);

      if (this.imageRetryCount[option] < this.maxRetries) {
        // 自动重试，每次重试增加时间戳
        this.imageRetryCount[option]++;
        this.imageRetryTimers[option] = setTimeout(() => {
          console.log(`尝试重新加载图片: ${option} (第${this.imageRetryCount[option]}次)`);
          this.loadImage(option, true);
        }, 1000 * this.imageRetryCount[option]);
      } else {
        this.imageStatus[option] = 'error';
      }
    },

    // 处理主图片加载成功
    handleMainImageLoad() {
      this.mainImageStatus = 'loaded';
    },

    // 处理主图片加载失败
    handleMainImageError() {
      this.mainImageStatus = 'error';
      console.error(`主图片加载失败: ${this.imgName}`);
    },

    // 手动重试加载图片
    retryLoadImage(option) {
      this.imageStatus[option] = 'loading';
      this.loadImage(option, true);
    },

    // 加载图片（仅在重试时调用）
    loadImage(option, isRetry = false) {
      if (!isRetry) return; // 避免非重试场景下的重复加载

      const img = new Image();
      img.onload = () => this.handleImageLoad(option);
      img.onerror = () => this.handleImageError(option);
      img.src = this.getImageUrl(option);
    },

    // 刷新选项
    refreshOptions() {
      this.answerOptions.forEach(option => {
        this.imageStatus[option] = 'loading';
        this.loadImage(option, true);
      });
    },

    // 设置数据
    setData() {
      for(let i = 1; i <= 4; i++) {
        for(let j = 1; j <= 4; j++) {
          for(let k = 1; k <= 4; k++) {
            const item = i + '_' + j + '_' + k
            this.questionList.push(item)
          }
        }
      }

      this.questionList = this.questionList.concat(this.questionList)
      this.questionList = api.shuffle(this.questionList)
      this.startProcess()
    },

    // 开始处理
    startProcess() {
      if (this.correctNum >= 10) {
        this.type = (this.type + 1) % 3
        this.correctNum = 0
      }
    },

    // 选择项目
    chooseItem(item) {
      this.answerList.splice(this.ra, 0, item)
      const answer = item.split('_')
      const question = this.questionList[this.ra].split('_')

      if (answer[this.type] === question[this.type]) {
        this.rc++
        this.correctNum++
        if (this.correctNum >= 3) this.rfRatio++
        this.isShowCorrect = true
      } else {
        this.re++
        if (this.correctNum >= 5) this.fm++
        this.isShowError = true

        let type0 = false
        let type1 = false
        let type2 = false
        if (answer[0] === question[0]) {
          type0 = true
        } else if (answer[1] === question[1]) {
          type1 = true
        } else if (answer[2] === question[2]) {
          type2 = true
        }

        if (this.ra) {
          const perAnswer = this.answerList[this.ra - 1].split('_')
          const perQuestion = this.questionList[this.ra - 1].split('_')
          let perType0 = false
          let perType1 = false
          let perType2 = false
          if (perAnswer[0] === perQuestion[0]) {
            perType0 = true
          } else if (perAnswer[1] === perQuestion[1]) {
            perType1 = true
          } else if (perAnswer[2] === perQuestion[2]) {
            perType2 = true
          }

          let lastType0 = false
          let lastType1 = false
          let lastType2 = false
          if (this.ra > 1) {
            const lastAnswer = this.answerList[this.ra - 2].split('_')
            const lastQuestion = this.questionList[this.ra - 2].split('_')
            if (lastAnswer[0] === lastQuestion[0]) {
              lastType0 = true
            } else if (lastAnswer[1] === lastQuestion[1]) {
              lastType1 = true
            } else if (lastAnswer[2] === lastQuestion[2]) {
              lastType2 = true
            }
          }

          if ((type0 && perType0 || type1 && perType1 || type2 && perType2) && !this.correctNum) {
            this.pe++
            if (this.ra > 1 && !(type0 && lastType0 || type1 && lastType1 || type2 && lastType2)) this.pe++
            if (((this.type - 1) % 3 === 0 && type0) || ((this.type - 1) % 3 === 1 && type1) || ((this.type - 1) % 3 === 2 && type2)) {
              this.rpe++
              if (this.ra > 1 && !(type0 && lastType0 || type1 && lastType1 || type2 && lastType2)) this.rpe++
            }
          }

          this.correctNum = 0
        }
      }

      setTimeout(() => {
        this.handleClose()
      }, 1000)
    },

    // 处理关闭
    handleClose() {
      this.isShowCorrect = false
      this.isShowError = false
      this.ra++
      if (this.cc >= 6 || this.ra >= 128) {
        this.commit()
        return
      }
      this.startProcess()
      console.log('1--------1111', this.correctNum, this.type, this.ra, this.cc, this.rc, this.re, this.rcpRatio,
        this.rf, this.rfRatio, this.pe, this.rpe, this.nrpe, this.rpeRatio, this.fm)
    },

    // 提交结果
    commit() {
      if (this.loading) return

      let ll = 0
      this.rfs.forEach(item => {
        if (!ll) ll = (item - 10) / item
        ll = ll - (item - 10) / item
      })
      ll = ll / this.rfs.length
      if (!ll && ll != 0){
        ll = -11
      }
      this.loading = true
      const params = {
        userId: this.userId,
        measureId: this.measure.id,
        resultId: this.resultId,
        time: new Date() - this.time,
        result: {
          ra: this.ra,			// 总应答数（Response Administered ，Ra）
          cc: this.cc,			 // 完成分类数（Categories Completed Cc）
          rc: this.rc,			 // 正确应答数（RC）
          re: this.re,			 //错误应答数（Re）
          rcpRatio: (this.rc / this.ra).toFixed(2) * 100,	// 正确应答百分比（RCP）
          rf: this.rfs.length ? this.rfs[0] : 0,			 //完成第一个分类所需应答数（Rf，TCFC）
          rfRatio: (this.rfRatio / this.ra) .toFixed(2) * 100,	 //概念化水平百分数（Rf%）
          pe: this.pe,			 //持续性应答数（Perseverative Errors， PE）
          rpe: this.rpe,			 // 持续性错误数（Rpe）
          nrpe: this.re - this.rpe,			 // 非持续性错误（nRpe）
          rpeRatio: (this.rpe / this.ra).toFixed(2) * 100,	// 持续性错误的百分数（Rpe%）
          fm: this.fm,			 // 不能维持完整分类数（Fm）
          ll                 // 学习到学会
        }
      }

      postAction(this.url, params).then(res => {
        if (res.success) {
          this.$emit('ok')
        }
      }).finally(() => {
        this.loading = false
      })
    }
  },

  beforeDestroy() {
    // 清理计时器
    Object.values(this.imageLoadTimers).forEach(timer => clearTimeout(timer));
    Object.values(this.imageRetryTimers).forEach(timer => clearTimeout(timer));
  }
}
</script>

<style lang="scss" scoped>
.wskx-item {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/wskx/bg.png");
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
      width: 100%;
      position: relative;
      display: flex;
      justify-content: space-between;

      .text {
        font-size: 28px;
        line-height: 44px;
        color: #0055A6;
      }

      .right {
        display: inline-flex;
        align-items: center;

        .top-item {
          display: inline-flex;
          align-items: center;
          padding-left: 30px;
          font-size: 28px;
          line-height: 44px;

          .anticon {
            font-size: 28px;
            margin-right: 10px;
          }
        }
      }
    }

    .content {
      position: relative;
      padding: 25px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .top {
        width: 330px;
        padding: 30px 0 50px 0;
        text-align: center;

        .title {
          width: 330px;
          padding: 0 15px 15px 25px;
          text-align: center;
          font-size: 32px;
          line-height: 50px;
          color: #0055A6;
        }

        .img {
          width: 330px;
          border-radius: 12px;
        }
      }

      .bottom {
        display: flex;
        flex-direction: column;
        text-align: center;
        align-items: center;
        padding: 20px 25px 30px 25px;
        background: #A5C8EB;
        border-radius: 16px;
        border: 1px solid #FFFFFF;

        .title {
          text-align: center;
          padding-left: 0;
          padding-bottom: 20px;
          font-size: 32px;
          line-height: 50px;
          color: #0055A6;
        }

        .bottom-content {
          min-width: 1200px;
          display: flex;
          justify-content: space-between;
          padding-bottom: 20px;

          .item {
            margin: 0 20px;
            width: 260px;
            padding: 5px;
            border-radius: 12px;
            background: #fff;
            transition: all 0.3s;
            cursor: pointer;

            .img {
              width: 250px;
              border-radius: 12px;
            }

            &:hover:not(.choose-item) {
              transform: translateY(-5px);
              box-shadow: 0 4px 12px rgba(0, 85, 166, 0.2);
            }
          }

          .choose-item {
            background: #0055A6;
            transform: translateY(-5px);
            box-shadow: 0 4px 12px rgba(0, 85, 166, 0.4);
          }

          .loading-placeholder, .error-placeholder {
            width: 100%;
            height: 250px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #f0f0f0;
            border-radius: 12px;
            font-size: 16px;
          }

          .loading-spinner {
            display: inline-block;
            position: relative;
            width: 20px;
            height: 20px;
            margin-right: 8px;

            &::after {
              content: '';
              display: block;
              width: 16px;
              height: 16px;
              border: 2px solid #0055A6;
              border-radius: 50%;
              border-top-color: transparent;
              animation: spin 1s linear infinite;
            }
          }

          .error-placeholder {
            color: #f5222d;

            .retry-btn {
              margin-top: 10px;
              padding: 4px 12px;
              background-color: #0055A6;
              color: white;
              border: none;
              border-radius: 4px;
              cursor: pointer;
              transition: all 0.3s;

              &:hover {
                background-color: #004080;
              }
            }
          }
        }
      }
    }

    .mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      display: flex;
      justify-content: center;

      .center {
        position: absolute;
        top: 400px;
        background: #fff;
        border-radius: 12px;
        padding: 60px 80px 40px 80px;

        .center-content {
          display: flex;
          align-items: center;
          height: 50px;
          padding-bottom: 20px;

          .icon {
            height: 40px;
          }

          .text {
            margin: 0;
            font-size: 36px;
            padding-left: 15px;
          }
        }
      }
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}
</style>
<template>
  <div class="connect-item">
    <p class="connect-title" v-html="question.title"></p>
    <p v-if="question.titleExplain && isControl && isSynchronize" class="connect-title">题目解释：{{ question.titleExplain }}</p>

    <div class="connect-main">
      <!-- <div class="clear-btn" @click="revoke">撤销</div> -->
       <div class="clear-btn" @click="submit({})">提交</div>
      <div class="confirm-btn"  v-if="isTerminal && isSynchronize" @click="getImg">确定</div>

      <div class="connect-img" :class="[questionIndex % 2 && 'connect-img-big']" ref="connectImg">
        <img class="img" v-if="isControl && isSynchronize && imgUrl" :src="getUrl(imgUrl)" />
        <template v-else>
          <template v-if="questionIndex % 2 === 0">
            <canvas id="mycanvas" width="1000" height="460"></canvas>
            <img :class="['main-item-icon', `main-item-start-${questionIndex}`]" src="../../../assets/img/img_start.png" />
            <img :class="['main-item-icon', `main-item-end-${questionIndex}`]" src="../../../assets/img/img_end.png" />
            <div
              :class="['main-item', `main-item-${questionIndex}-${item}`, choose.includes(item) && 'choose-item', item % 2 ? 'main-item-square' : 'main-item-round']"
              v-for="item in questionIndex ? 15 : 8"
              :key="item"
              :ref="`item${item}`"
              @click="clickItem(item)"
            >
              {{ questionIndex ? Math.floor(item / 2 + 1) : item  }}
            </div>
          </template>
          <template v-else>
            <canvas id="mycanvas" width="1000" height="1200"></canvas>
            <img :class="['main-item-icon', `main-item-start-${questionIndex}`]" src="../../../assets/img/img_start.png" />
            <img :class="['main-item-icon', `main-item-end-${questionIndex}`]" src="../../../assets/img/img_end.png" />
            <div
              :class="['main-item', `main-item-${questionIndex}-${item}`, choose.includes(item) && 'choose-item', item % 2 ? 'main-item-square' : 'main-item-round']"
              v-for="item in questionIndex === 3 ? 49 : 25"
              :key="item"
              :ref="`item${item}`"
              @click="clickItem(item)"
            >
              {{ questionIndex === 3 ? Math.floor(item / 2 + 1) : item  }}
            </div>
          </template>
        </template>
      </div>
    </div>

    <div class="connect-item-answer" v-if="!(isTerminal && isSynchronize)">
      <div
        class="answer-item"
        v-for="(item, index) in question.psOptions"
        :key="index"
        :id="item.id"
        @click="chooseItem(item)"
      >
        <a-button type="primary" :class="[(question.answer === item.title || question.optionId === item.id) && 'choose-btn']" size="large">{{ item.title }}</a-button>
        <span v-if="item.type == 1" class="item-text">{{ item.content }}</span>
        <img
          class="item-img"
          v-if="item.type == 2"
          :src="item.content"
        />
      </div>
    </div>

    <div class="connect-item-remark" v-if="showRemark">
      <span>备注：</span>
      <textarea :value="question.optionRemark" @blur="handleRemarkBlur" :rows="4" placeholder="请输入备注" />
    </div>

    <div class="connect-item-input">
      <div class="input-item">
        <span>提醒次数：</span>
        <input type="number" v-model="remindNum" />
      </div>
      <div class="input-item">
        <span>抬笔提醒次数：</span>
        <input type="number" v-model="penRemindNum" />
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import html2canvas from 'html2canvas'
import {ACCESS_TOKEN} from '@/store/mutation-types'

export default {
  name: 'SttItem',

  props: {
    question: {
      type: Object,
      default: () => {
      }
    },

    isTerminal: {
      type: Boolean,
      default: false
    },

    isControl: {
      type: Boolean,
      default: false
    },

    isSynchronize: {
      type: Boolean,
      default: false
    },

    imgUrl: {
      type: String,
      default: ''
    },

    showRemark: {
      type: Boolean,
      default: false
    },

    questionIndex: {
      type: Number,
      default: 0,
    }
  },

  data() {
    return {
      choose: [],
      path: [],
      imgerver: window._CONFIG['domianURL'],
      errorNum: 0,
      remindNum: 0,
      penRemindNum: 0,
    }
  },

  methods: {
    getUrl(url) {
      if (url.includes('temp')) {
        return this.imgerver + '/' + url
      } else {
        return url
      }
    },
    getArrow( startX, startY, finalX, finalY,theta=40,headlen=10,width=1,color="#333") {
      var idName = document.getElementById("mycanvas");
      var ctx = idName.getContext("2d");

      // 计算各角度和对应坐标
      var angle = Math.atan2(startY - finalY, startX - finalX) * 180 / Math.PI,
          angle1 = (angle + theta) * Math.PI / 180,
          angle2 = (angle - theta) * Math.PI / 180,
          topX = headlen * Math.cos(angle1),
          topY = headlen * Math.sin(angle1),
          botX = headlen * Math.cos(angle2),
          botY = headlen * Math.sin(angle2);
      ctx.save();
      var arrowX ,arrowY ;
      //绘制箭头
      ctx.beginPath();
      arrowX = finalX + topX;
      arrowY = finalY + topY;

      ctx.moveTo(arrowX, arrowY);
      ctx.lineTo(finalX, finalY);
      arrowX = finalX + botX;
      arrowY = finalY + botY;
      ctx.lineTo(arrowX, arrowY);
      ctx.strokeStyle = color;
      ctx.lineWidth = width;

      ctx.stroke();
      ctx.closePath();
      arrowX = startX - topX;
      arrowY = startY - topY;
      //绘制虚线
      ctx.beginPath();

      ctx.setLineDash([10, 5]);//实线 去掉此句

      ctx.moveTo(arrowX, arrowY);
      ctx.moveTo(startX, startY);
      ctx.lineTo(finalX, finalY);
      ctx.stroke();
      ctx.restore();
    },

    clickItem(index) {
      if (!this.choose.length) {
        this.choose.push(index)
        return
      }

      let isError = false
      if (this.questionIndex < 2) {
        if (index != this.choose[this.choose.length - 1] + 1) {
          isError = true
        }
        this.errorNum++
      } else {
        if ((this.choose.length % 2 === 1 && index !== this.choose[this.choose.length - 1] + 1) || (this.choose.length % 2 === 0 && index != this.choose[this.choose.length - 1] + 3)) {
          isError = true
          console.log('错误=======', this.choose.length % 2, index, this.choose[this.choose.length - 1])
        }
        this.errorNum++
      }

      const ref1 = this.$refs[`item${this.choose[this.choose.length - 1]}`][0]
      const x1 = (Number(ref1.offsetLeft) + Number(ref1.offsetHeight) / 2)
      const y1 = (Number(ref1.offsetTop) + Number(ref1.offsetWidth) / 2)
      const ref2 = this.$refs[`item${index}`][0]
      const x2 = (Number(ref2.offsetLeft) + Number(ref2.offsetHeight) / 2)
      const y2 = Number(ref2.offsetTop) + Number(ref2.offsetWidth) / 2
      this.drawLine(x1, y1, x2, y2, isError)

      this.path.push({
        x1: x1,
        y1: y1,
        x2: x2,
        y2: y2
      })

      this.choose.push(index)
    },

    drawLine(x1, y1, x2, y2, isError) {
      const canvas = document.getElementById('mycanvas')
      const ctx = canvas.getContext('2d')
      ctx.save()
      ctx.beginPath() //不写每次都会重绘上次的线
      ctx.lineCap = "round"
      ctx.lineJoin = "round"

      ctx.moveTo(x1, y1)
      ctx.lineTo(x2, y2)
      ctx.closePath()
      ctx.strokeStyle = isError ? "red" : "#333"
      ctx.lineWidth = 2
      ctx.stroke()
      ctx.restore()
      if (isError) {
        setTimeout(() => {
          this.revoke()
        }, 1000)
      }
    },

    revoke() {
      const canvas = document.getElementById('mycanvas')
      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, 1200, 700)
      this.choose.pop()
      if (this.choose.length === 1 && this.choose[0] !== 1) {
        this.choose = []
      }

      this.path.pop()
      this.path.forEach(item => {
        this.drawLine(this.drawLine(item.x1, item.y1, item.x2, item.y2))
      })
    },

    async getImg() {
      let flieParam = new FormData()
      const img = await this.toImg()
      flieParam.append('file', new File([this.convertBase64UrlToBlob(img)], 'connectLine.png'))
      flieParam.append('biz', 'temp/image/connectLine')
      return fetch(window._CONFIG['domianURL'] + '/sys/common/upload', {
        method: 'post',
        body: flieParam,
        headers: {'X-Access-Token': Vue.ls.get(ACCESS_TOKEN)},
      })
        .then((response) => response.text())
        .then((result) => {
          const canvasImgUrl = JSON.parse(result).message
          this.$emit('submitImg', canvasImgUrl)
        })
    },

    chooseItem(item) {
      this.question.answer = item.title
      this.submit(item)
    },

    async submit(item) {
      if (this.isControl && this.isSynchronize) {
        this.$emit('submit', {optionId: item.id || undefined, answer: this.imgUrl, type: '16'})
        this.$emit('setRemark', {questionId: this.question.id, remark: `${this.remindNum};${this.errorNum};${this.penRemindNum}`})
        return
      }
      let flieParam = new FormData()
      const img = await this.toImg()
      flieParam.append('file', new File([this.convertBase64UrlToBlob(img)], 'connectLine.png'))
      flieParam.append('biz', 'temp/image/connectLine')
      return fetch(window._CONFIG['domianURL'] + '/sys/common/upload', {
        method: 'post',
        body: flieParam,
        headers: {'X-Access-Token': Vue.ls.get(ACCESS_TOKEN)},
      })
        .then((response) => response.text())
        .then((result) => {
          const canvasImgUrl = JSON.parse(result).message
          this.$emit('submit', {optionId: item.id || undefined, answer: canvasImgUrl, type: '16'})
          this.$emit('setRemark', {questionId: this.question.id, remark: `${this.remindNum};${this.errorNum};${this.penRemindNum}`})
        })
    },  

    // base64转formData
    convertBase64UrlToBlob(urlData) {
      //去掉url的头，并转换为byte
      var bytes = window.atob(urlData.split(',')[1]);
      //处理异常,将ascii码小于0的转换为大于0
      var ab = new ArrayBuffer(bytes.length);
      var ia = new Uint8Array(ab);
      for (var i = 0; i < bytes.length; i++) {
        ia[i] = bytes.charCodeAt(i);
      }
      return new Blob([ia], {type: 'image/png'});
    },

    // 绘制图片
    toImg() {
      let canvas2 = document.createElement('canvas')
      const ref = this.$refs['connectImg']
      let w = ref.offsetWidth
      let h = ref.offsetHeight
      canvas2.width = w * 2
      canvas2.height = h * 2
      const context = canvas2.getContext('2d')
      context.scale(2, 2)
      let scale = 2
      let opts = {
        scale,
        canvas2: context,
        w,
        h,
        scrollX: 0,
        scrollY: 0,
        // 【重要】开启跨域配置
        useCORS: true,
        allowTaint: true
      }
      return new Promise((resolve, reject) => {
        html2canvas(ref, opts).then(function (canvas) {
          const imgUrl = canvas.toDataURL('image/png')
          resolve(imgUrl)
        })
      })
    },

    handleRemarkBlur(e) {
      this.$set(this.question, 'optionRemark', e.target.value)
      this.$emit('setRemark', {questionId: this.question.id, remark: this.question.optionRemark})
    },
  },

  watch: {
    questionIndex(val) {
      this.errorNum = 0
      this.remindNum = 0
      this.penRemindNum = 0
      this.choose = []
      this.path = []
    }
  }
}
</script>

<style lang="scss" scoped>
.connect-item {
  padding: 0 100px;

  #mycanvas {
    position: absolute;
    top: 0;
    left: 0;
  }

  .connect-title {
    font-size: 28px;
    padding-bottom: 20px;
  }

  .connect-main {
    position: relative;
    width: 1000px;

    .connect-img {
      position: relative;
      width: 1000px;
      height: 460px;
      border: 1px solid #ccc;
      overflow: hidden;
    }

    .connect-img-big {
      height: 1200px;
    }

    .img {
      width: 100%;
      height: 100%;
    }

    .clear-btn {
      position: absolute;
      top: 0;
      left: 0;
      padding: 10px;
      background-color: #448ef7;
      color: #fff;
      cursor: pointer;
      z-index: 99;
    }

    .confirm-btn {
      position: absolute;
      top: 0;
      right: 0;
      padding: 10px;
      background-color: #448ef7;
      color: #fff;
      cursor: pointer;
      z-index: 99;
    }

    .main-item-icon {
      position: absolute;
      width: 50px;
      height: 50px;
    }

    .main-item-start-0 {
      top: 180px;
      left: 200px;
    }

    .main-item-start-1 {
      top: 50px;
      left: 70px;
    }

    .main-item-start-2 {
      top: 300px;
      left: 120px;
    }

    .main-item-start-3 {
      top: 700px;
      left: 50px;
    }

    .main-item-end-0 {
      top: 260px;
      left: 30px;
    }

    .main-item-end-1 {
      top: 160px;
      left: 550px;
    }

    .main-item-end-2 {
      top: 350px;
      left: 810px;
    }

    .main-item-end-3 {
      top: 1020px;
      left: 110px;
    }

    .main-item {
      position: absolute;
      width: 50px;
      height: 50px;
      border: 1px solid #333;
      z-index: 1;

      font-size: 30px;
      font-weight: 500;
      line-height: 50px;
      text-align: center;
      color: #333;
      background: #fff;
      cursor: pointer;
    }

    .main-item-round {
      border-radius: 50%;
    }

    .choose-item {
      box-shadow: 0 1px 0.625rem #777;
    }

    .main-item-0-1 {
      top: 180px;
      left: 250px;
    }

    .main-item-0-2 {
      top: 30px;
      left: 520px;
    }

    .main-item-0-3 {
      top: 150px;
      left: 900px;
    }

    .main-item-0-4 {
      top: 200px;
      left: 620px;
    }

    .main-item-0-5 {
      top: 320px;
      left: 820px;
    }

    .main-item-0-6 {
      top: 360px;
      left: 280px;
    }

    .main-item-0-7 {
      top: 290px;
      left: 570px;
    }

    .main-item-0-8 {
      top: 260px;
      left: 80px;
    }

    .main-item-1-1 {
      top: 50px;
      left: 120px;
    }

    .main-item-1-2 {
      top: 40px;
      left: 360px;
    }

    .main-item-1-3 {
      top: 200px;
      left: 80px;
    }

    .main-item-1-4 {
      top: 500px;
      left: 160px;
    }

    .main-item-1-5 {
      top: 950px;
      left: 80px;
    }

    .main-item-1-6 {
      top: 280px;
      left: 280px;
    }

    .main-item-1-7 {
      top: 600px;
      left: 420px;
    }

    .main-item-1-8 {
      top: 800px;
      left: 200px;
    }

    .main-item-1-9 {
      top: 1100px;
      left: 140px;
    }

    .main-item-1-10 {
      top: 1050px;
      left: 650px;
    }

    .main-item-1-11 {
      top: 900px;
      left: 900px;
    }

    .main-item-1-12 {
      top: 920px;
      left: 380px;
    }

    .main-item-1-13 {
      top: 750px;
      left: 700px;
    }

    .main-item-1-14 {
      top: 540px;
      left: 780px;
    }

    .main-item-1-15 {
      top: 820px;
      left: 850px;
    }

    .main-item-1-16 {
      top: 120px;
      left: 910px;
    }

    .main-item-1-17 {
      top: 300px;
      left: 780px;
    }

    .main-item-1-18 {
      top: 500px;
      left: 720px;
    }

    .main-item-1-19 {
      top: 410px;
      left: 600px;
    }

    .main-item-1-20 {
      top: 650px;
      left: 670px;
    }

    .main-item-1-21 {
      top: 750px;
      left: 450px;
    }

    .main-item-1-22 {
      top: 130px;
      left: 370px;
    }

    .main-item-1-23 {
      top: 60px;
      left: 800px;
    }

    .main-item-1-24 {
      top: 180px;
      left: 830px;
    }

    .main-item-1-25 {
      top: 160px;
      left: 600px;
    }

    .main-item-2-1 {
      top: 300px;
      left: 170px;
    }

    .main-item-2-2 {
      top: 300px;
      left: 520px;
    }

    .main-item-2-3 {
      top: 380px;
      left: 160px;
    }

    .main-item-2-4 {
      top: 370px;
      left: 350px;
    }

    .main-item-2-5 {
      top: 160px;
      left: 300px;
    }

    .main-item-2-6 {
      top: 60px;
      left: 700px;
    }

    .main-item-2-7 {
      top: 70px;
      left: 190px;
    }

    .main-item-2-8 {
      top: 65px;
      left: 400px;
    }

    .main-item-2-9 {
      top: 180px;
      left: 580px;
    }

    .main-item-2-10 {
      top: 170px;
      left: 840px;
    }

    .main-item-2-11 {
      top: 170px;
      left: 440px;
    }

    .main-item-2-12 {
      top: 65px;
      left: 820px;
    }

    .main-item-2-13 {
      top: 380px;
      left: 710px;
    }

    .main-item-2-14 {
      top: 350px;
      left: 860px;
    }

    .main-item-2-15 {
      top: 240px;
      left: 900px;
    }

    .main-item-3-1 {
      top: 700px;
      left: 100px;
    }

    .main-item-3-2 {
      top: 690px;
      left: 400px;
    }

    .main-item-3-3 {
      top: 540px;
      left: 90px;
    }

    .main-item-3-4 {
      top: 380px;
      left: 360px;
    }

    .main-item-3-5 {
      top: 330px;
      left: 300px;
    }

    .main-item-3-6 {
      top: 580px;
      left: 200px;
    }

    .main-item-3-7 {
      top: 200px;
      left: 195px;
    }

    .main-item-3-8 {
      top: 260px;
      left: 140px;
    }

    .main-item-3-9 {
      top: 420px;
      left: 85px;
    }

    .main-item-3-10 {
      top: 100px;
      left: 110px;
    }

    .main-item-3-11 {
      top: 50px;
      left: 50px;
    }

    .main-item-3-12 {
      top: 120px;
      left: 210px;
    }

    .main-item-3-13 {
      top: 50px;
      left: 450px;
    }

    .main-item-3-14 {
      top: 50px;
      left: 850px;
    }

    .main-item-3-15 {
      top: 150px;
      left: 540px;
    }

    .main-item-3-16 {
      top: 120px;
      left: 780px;
    }

    .main-item-3-17 {
      top: 500px;
      left: 880px;
    }

    .main-item-3-18 {
      top: 220px;
      left: 790px;
    }

    .main-item-3-19 {
      top: 120px;
      left: 910px;
    }

    .main-item-3-20 {
      top: 90px;
      left: 620px;
    }

    .main-item-3-21 {
      top: 210px;
      left: 350px;
    }

    .main-item-3-22 {
      top: 280px;
      left: 530px;
    }

    .main-item-3-23 {
      top: 290px;
      left: 710px;
    }

    .main-item-3-24 {
      top: 420px;
      left: 720px;
    }

    .main-item-3-25 {
      top: 330px;
      left: 450px;
    }

    .main-item-3-26 {
      top: 860px;
      left: 710px;
    }

    .main-item-3-27 {
      top: 360px;
      left: 640px;
    }

    .main-item-3-28 {
      top: 730px;
      left: 850px;
    }

    .main-item-3-29 {
      top: 540px;
      left: 690px;
    }

    .main-item-3-30 {
      top: 400px;
      left: 540px;
    }

    .main-item-3-31 {
      top: 620px;
      left: 640px;
    }

    .main-item-3-32 {
      top: 500px;
      left: 580px;
    }

    .main-item-3-33 {
      top: 540px;
      left: 780px;
    }

    .main-item-3-34 {
      top: 820px;
      left: 790px;
    }

    .main-item-3-35 {
      top: 710px;
      left: 740px;
    }

    .main-item-3-36 {
      top: 950px;
      left: 760px;
    }

    .main-item-3-37 {
      top: 830px;
      left: 910px;
    }

    .main-item-3-38 {
      top: 1080px;
      left: 890px;
    }

    .main-item-3-39 {
      top: 1100px;
      left: 720px;
    }

    .main-item-3-40 {
      top: 860px;
      left: 460px;
    }

    .main-item-3-41 {
      top: 880px;
      left: 300px;
    }

    .main-item-3-42 {
      top: 700px;
      left: 500px;
    }

    .main-item-3-43 {
      top: 540px;
      left: 410px;
    }

    .main-item-3-44 {
      top: 750px;
      left: 240px;
    }

    .main-item-3-45 {
      top: 820px;
      left: 120px;
    }

    .main-item-3-46 {
      top: 1060px;
      left: 390px;
    }

    .main-item-3-47 {
      top: 1020px;
      left: 465px;
    }

    .main-item-3-48 {
      top: 1120px;
      left: 190px;
    }

    .main-item-3-49 {
      top: 1020px;
      left: 160px;
    }
  }

  .connect-item-answer {
    width: 1200px;
    padding-top: 30px;
    display: flex;
    flex-wrap: wrap;

    .answer-item {
      display: flex;
      width: 300px;
      padding: 10px;

      .ant-btn {
        height: 40px;
        margin-right: 10px;
      }

      .choose-btn {
        background: green;
      }

      .ant-btn:active {
        background: green;
      }

      .item-text {
        display: inline-block;
        font-size: 24px;
        line-height: 40px;
        cursor: pointer;
      }

      .item-img {
        width: 150px;
        cursor: pointer;
      }
    }
  }

  .connect-item-remark {
    display: flex;
    justify-content: flex-start;
    padding-top: 20px;

    span {
      width: 60px;
    }

    textarea {
      flex: 1;
      border-color: #ccc;
      border-radius: 4px;
    }
  }

  .connect-item-input {
    display: flex;
    align-items: center;

    .input-item {
      padding-right: 120px;
      display: flex;
      align-items: center;

      span {
        width: 120px;
      }

      input {
        border-radius: 4px;
      }
    }
  }
}
</style>
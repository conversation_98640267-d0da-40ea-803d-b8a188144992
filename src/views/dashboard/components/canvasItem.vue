<template>
  <div class="canvas-item">
    <p class="canvas-title" v-html="question.title"></p>
    <p v-if="question.titleExplain && isControl && isSynchronize" class="canvas-title">题目解释：{{ question.titleExplain }}</p>

    <div class="canvas-content">
      <img v-if="question.imgPath" class="content-img" ref="imgRef" :src="getUrl(question.imgPath)" />

      <div class="content-main" ref="contentMain">
        <img class="img" v-if="isControl && isSynchronize && imgUrl" :src="getUrl(imgUrl)" />
        <template v-else>
          <div class="clear-btn" @click="clearCanvas('main-canvas')">清空</div>
          <div class="confirm-btn" v-if="isTerminal && isSynchronize" @click="getImg">确定</div>
          <canvas id="main-canvas" class="main-canvas" width="473px" height="473px"></canvas>
        </template>
      </div>
    </div>

    <template v-if="!(isTerminal && isSynchronize)">
      <div class="connect-item-answer" v-if="question.psOptions.length">
        <div
          class="answer-item"
          v-for="(item, index) in question.psOptions"
          :key="index"
          :id="item.id"
          @click="chooseItem(item)"
        >
          <a-button type="primary" :class="[(question.answer === item.title || question.optionId === item.id) && 'choose-btn']" size="large">{{ item.title }}</a-button>
          <span v-if="item.type == 1" class="item-text">{{ item.content }}</span>
          <img
            class="item-img"
            v-if="item.type == 2"
            :src="item.content"
          />
        </div>
      </div>
      <div class="canvas-footer" v-else>
        <span class="footer-text">输入分值：</span>

        <a-input-number
          v-model="score"
          placeholder="请输入分值"
          :min="0"
          :max="100"
          :precision="0"
        />

        <a-button @click="confirm" type="primary">
          确定
          <a-icon type="arrow-right" />
        </a-button>
      </div>
    </template>
    
    <div class="connect-item-remark" v-if="showRemark">
      <span>备注：</span>
      <textarea :value="question.optionRemark" @blur="handleRemarkBlur" :rows="4" placeholder="请输入备注" />
    </div>
  </div>
</template>

<script>
import { ACCESS_TOKEN } from '@/store/mutation-types'
import Vue from 'vue'

export default {
  name: 'CanvasItem',

  props: {
    question: {
      type: Object,
      default: () => {}
    },

    isTerminal: {
      type: Boolean,
      default: false
    },

    isControl: {
      type: Boolean,
      default: false
    },

    isSynchronize: {
      type: Boolean,
      default: false
    },

    imgUrl: {
      type: String,
      default: ''
    },

    showRemark: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      imgerver: window._CONFIG['domianURL'],
      score: '',
      canvas: null
    }
  },

  methods: {
    getUrl(url) {
      if (url.includes('temp')) {
        return this.imgerver + '/' + url
      } else {
        return url
      }
    },
    // 初始化画布
    initCanvas() {
      const sale = 473 / this.$refs['contentMain'].clientWidth
      //1.获取canvas
      let myCanvas = document.getElementById('main-canvas')
      //获取2d对象
      this.canvas = myCanvas.getContext('2d')
      //控制线条是否画
      let isMouseMove = false
      //线条位置
      let lastX, lastY
      //画线
      let drawLine = (x, y, isT) => {
        if (isT) {
          this.canvas.beginPath()
          this.canvas.lineWidth = 2 //设置线宽状态
          this.canvas.strokeStyle = '#000000' //设置线的颜色状态
          this.canvas.lineCap = 'round'
          this.canvas.lineJoin = 'round'
          this.canvas.moveTo(lastX, lastY)
          this.canvas.lineTo(x, y)
          this.canvas.stroke()
          this.canvas.closePath()
        }
        // 每次移动都要更新坐标位置
        lastX = x
        lastY = y
      }

      let down = (e) => {
        e.preventDefault()
        isMouseMove = true
        lastX = e.offsetX * sale
        lastY = e.offsetY * sale
      }
      let down2 = (e) => {
        e.preventDefault()
        isMouseMove = true
        lastX = (e.touches[0].pageX - this.$refs['contentMain'].offsetLeft) * sale
        lastY = (e.touches[0].pageY - this.$refs['contentMain'].offsetTop) * sale
      }
      let move = (e) => {
        if (isMouseMove) {
          drawLine(
            e.offsetX * sale,
            e.offsetY * sale,
            true
          )
        }
      }
      let move2 = (e) => {
        if (isMouseMove) {
          drawLine(
            (e.touches[0].pageX - this.$refs['contentMain'].offsetLeft) * sale,
            (e.touches[0].pageY - this.$refs['contentMain'].offsetTop) * sale,
            true
          )
        }
      }
      let leave = (e) => {
        isMouseMove = false
      }
      myCanvas.addEventListener('mousedown', down)
      myCanvas.addEventListener('touchstart', down2)
      myCanvas.addEventListener('mousemove', move)
      myCanvas.addEventListener('touchmove', move2)
      myCanvas.addEventListener('mouseup', leave)
      myCanvas.addEventListener('mouseleave', leave)
      myCanvas.addEventListener('touchend', leave)
    },

    // 清空画布
    clearCanvas(id) {
      let myCanvas = document.getElementById(id)
      this.canvas.beginPath()
      this.canvas.clearRect(0, 0, myCanvas.width, myCanvas.height)
      this.canvas.closePath() //可加入，可不加入
    },

    async getImg() {
      let myCanvas = document.getElementById('main-canvas')
      let dataurl = myCanvas.toDataURL('image/png', 1.0)
      let arr = dataurl.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      let bstr = atob(arr[1])
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      let flieParam = new FormData()
      flieParam.append('file', new File([new Blob([u8arr], { type: mime })], 'canvas.png'))
      flieParam.append('biz', 'temp/image/canvas')
      fetch(window._CONFIG['domianURL'] + '/sys/common/upload', {
        method: 'post',
        body: flieParam,
        headers: { 'X-Access-Token': Vue.ls.get(ACCESS_TOKEN) },
      })
        .then((response) => response.text())
        .then((result) => {
          const canvasImgUrl = JSON.parse(result).message
          this.clearCanvas('main-canvas')
          this.$emit('submitImg', canvasImgUrl)
        })
    },

    // 画布转换成图片
    confirm() {
      if (this.isControl && this.isSynchronize) {
        this.$emit('submit', {optionId: id, answer: this.imgUrl, type: '3'})
        return
      }
      if (!this.score && this.score !== 0) {
        this.$message.warning('请填写当前项目的分值！')
        return
      }
      let myCanvas = document.getElementById('main-canvas')
      let dataurl = myCanvas.toDataURL('image/png', 1.0)
      let arr = dataurl.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      let bstr = atob(arr[1])
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      let flieParam = new FormData()
      flieParam.append('file', new File([new Blob([u8arr], { type: mime })], 'canvas.png'))
      flieParam.append('biz', 'temp/image/canvas')
      fetch(window._CONFIG['domianURL'] + '/sys/common/upload', {
        method: 'post',
        body: flieParam,
        headers: { 'X-Access-Token': Vue.ls.get(ACCESS_TOKEN) },
      })
        .then((response) => response.text())
        .then((result) => {
          const canvasImgUrl = JSON.parse(result).message
          this.clearCanvas('main-canvas')
          this.$emit('submit', { score: this.score, answer: canvasImgUrl, type: '3' })
        })
    },

    async chooseItem(item) {
      this.question.answer = item.title
      if (this.isControl && this.isSynchronize) {
        this.$emit('submit', {optionId: item.id, answer: this.imgUrl, type: '3'})
        return
      }
      let myCanvas = document.getElementById('main-canvas')
      let dataurl = myCanvas.toDataURL('image/png', 1.0)
      let arr = dataurl.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      let bstr = atob(arr[1])
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      let flieParam = new FormData()
      flieParam.append('file', new File([new Blob([u8arr], { type: mime })], 'canvas.png'))
      flieParam.append('biz', 'temp/image/canvas')
      fetch(window._CONFIG['domianURL'] + '/sys/common/upload', {
        method: 'post',
        body: flieParam,
        headers: { 'X-Access-Token': Vue.ls.get(ACCESS_TOKEN) },
      })
        .then((response) => response.text())
        .then((result) => {
          const canvasImgUrl = JSON.parse(result).message
          this.clearCanvas('main-canvas')
          this.$emit('submit', {optionId: item.id, answer: canvasImgUrl, type: '3'})
        })
    },

    handleRemarkBlur(e) {
      this.$set(this.question, 'optionRemark', e.target.value)
      this.$emit('setRemark', {questionId: this.question.id, remark: this.question.optionRemark})
    }
  },

  watch: {
    question: {
      handler() {
        this.score = ''
        setTimeout(() => {
          this.initCanvas()
        }, 50)
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style lang="scss">
.canvas-item {
  padding-left: 100px;

  .canvas-title {
    font-size: 28px;
  }

  .canvas-content {
    display: flex;

    .content-img {
      // width: 350px;
      height: 380px;
      margin-right: 150px;
    }

    .content-main {
      position: relative;
      width: 473px;
      height: 473px;
      box-shadow: 0 1px 0.625rem #777;

      .img {
        width: 100%;
        height: 100%;
      }

      .clear-btn {
        position: absolute;
        top: 0;
        left: 0;
        padding: 10px;
        background-color: #448ef7;
        color: #fff;
        cursor: pointer;
      }

      .confirm-btn {
        position: absolute;
        top: 0;
        right: 0;
        padding: 10px;
        background-color: #448ef7;
        color: #fff;
        cursor: pointer;
      }

      .main-canvas {
        max-width: 473px;
        max-height: 473px;
      }
    }
  }

  .canvas-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 30px;

    .footer-text {
      display: inline-block;
      font-size: 24px;
      line-height: 40px;
    }

    .ant-input-number {
      width: 400px;
      height: 40px;

      .ant-input-number-input {
        height: 40px;
      }
    }

    .ant-btn {
      height: 40px;
      margin-left: 10px;
      border-radius: 5px;
    }
  }

  .connect-item-answer {
    width: 1200px;
    padding-top: 30px;
    display: flex;
    flex-wrap: wrap;

    .answer-item {
      display: flex;
      width: 300px;
      padding: 10px;

      .choose-btn {
        background: green;
      }
      .ant-btn:active {
        background: green;
      }

      .ant-btn {
        height: 40px;
        margin-right: 10px;
      }

      .item-text {
        display: inline-block;
        font-size: 24px;
        line-height: 40px;
        cursor: pointer;
      }

      .item-img {
        width: 150px;
        cursor: pointer;
      }
    }
  }

  .connect-item-remark {
    display: flex;
    justify-content: flex-start;
    padding-top: 20px;

    span {
      width: 60px;
    }

    textarea {
      flex: 1;
      border-color: #ccc;
      border-radius: 4px;
    }
  }
}
</style>
<template>
  <div class="choose-item">
    <div class="choose-item-top">
      <div v-if="isPPVT" style="display: flex; align-items: center; justify-content: space-between">
        <div @click="handlePlayAudio()" style="display: flex; align-items: center">
          <p v-if="question.titleType" class="choose-item-title-ppvt" v-html="question.title"></p>
          <img v-if="questionIndex < 123" src="../../../assets/img/audio_play.png" class="ppvt-play-audio" style="width: 70px; height: 70px">
        </div>
        <div v-if="questionIndex == 2">
          <a-button type="primary" size="large" style="margin-right: 40px" @click="handlePPVTBegin()">开始</a-button>
          <a-button type="primary" size="large" @click="handlePPVTEnd()">结束</a-button>
        </div>
      </div>
      <p v-else-if="question.titleType" class="choose-item-title" v-html="question.title"></p>
      <div v-if="isShowInterval" class="time-interval-view">
        <a-button type="primary" @click="startTime()">开始计时</a-button>
        <div class="time-container">{{ time }}</div>
      </div>
      <img
        class="choose-item-img"
        v-if="question.imgPath"
        :src="question.imgPath"
      />
      <p class="choose-item-title" v-if="question.titleExplain && isControl && isSynchronize">题目解释：{{ question.titleExplain }}</p>
    </div>

    <div class="choose-item-answer"  v-if="!(isTerminal && isSynchronize)">
      <div
        class="answer-item"
        v-for="(item, index) in question.psOptions"
        :key="index"
        :id="item.id"
        @click="chooseItem(item)"
      >
        <a-button type="primary" :class="[(question.answer === item.title || question.optionId === item.id) && 'choose-btn']" size="large">{{ item.title }}</a-button>
        <span v-if="item.type == 1" class="item-text">{{ item.content }}</span>
        <img
          class="item-img"
          v-if="item.type == 2"
          :src="item.content"
        />
      </div>
    </div>

    <template v-if="twoRemark">
      <div class="connect-item-remark">
        <span>第一次：</span>
        <textarea :value="remark1" @blur="handleRemarkBlur2" :rows="4" placeholder="请输入备注" />
      </div>
      <div class="connect-item-remark">
        <span>第二次：</span>
        <textarea :value="remark2" @blur="handleRemarkBlur2" :rows="4" placeholder="请输入备注" />
      </div>
    </template>
    <div class="connect-item-remark" v-if="showRemark && !twoRemark">
      <span>备注：</span>
      <textarea :value="question.optionRemark" @blur="handleRemarkBlur" :rows="4" placeholder="请输入备注" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChooseItem',

  props: {
    question: {
      type: Object,
      default: () => {
      }
    },

    isTerminal: {
      type: Boolean,
      default: false
    },

    isControl: {
      type: Boolean,
      default: false
    },

    isSynchronize: {
      type: Boolean,
      default: false
    },

    isPPVT: {
      type: Boolean,
      default: false
    },

    questionIndex: {
      type: Number,
      default: 0
    },

    showRemark: {
      type: Boolean,
      default: false
    },

    twoRemark: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      imgerver: window._CONFIG['domianURL'],
      isShowInterval: false,
      hour: 0,
      minute: 0,
      second: 0,
      time: "00:00:00",
      timeInterval: null,
      remark1: '',
      remark2: '',
    }
  },

  mounted() {
    if (!this.showRemark) this.getText()
    this.checkMeasure()
  },

  watch: {
    questionIndex(index) {
      this.checkMeasure()
    }
  },

  methods: {
    //判断量表
    checkMeasure() {
      if (this.question.measureId == "6d38707527187bc953862b4244c38e16") {
        // 韦氏儿童详细版
        if (this.question.type == 4 || this.question.type == 5 || this.question.type == 6) {
          this.isShowInterval = true
        } else {
          this.isShowInterval = false
        }
      } else if (this.question.measureId == "058a17a6a479e3bebc464448c453fef1") {
        // 韦氏成人详细版
        if (this.question.type == 3 || this.question.type == 9 || this.question.type == 10) {
          this.isShowInterval = true
        } else {
          this.isShowInterval = false
        }
      } else if (this.question.measureId == "f8c58adbd3405416696fb329fb250101") {
        // 韦氏幼儿详细版
        if (this.question.type == 5 || this.question.type == 10 || this.question.type == 11) {
          this.isShowInterval = true
        } else {
          this.isShowInterval = false
        }
      } else {
        this.isShowInterval = false
      }

      if (this.isShowInterval && this.timeInterval) {
        this.initTime();
        clearInterval(this.timeInterval)
        this.timeInterval = null
      }
    },

    chooseItem(item) {
      this.question.answer = item.title
      this.$emit('submit', {optionId: item.id, type: item.type})
    },

    getText() {
      const list = ['1', '2', '3', '4', '5', '6', '7']
      let that = this
      window.onkeypress = function(e) {
        if (that.isStop) return
        const key = e.key.toUpperCase()
        const index = list.indexOf(key)
        if (index !== -1) {
          that.$emit('submit', {optionId: that.question.psOptions[index].id, type: that.question.psOptions[index].type})
        }
      }
    },
    // 重复播放题目
    handlePlayAudio() {
      this.$emit('playAudioAgain')
    },

    handlePPVTBegin() {
      this.$emit('ppvtBegin')
    },
    handlePPVTEnd() {
      let that = this
      this.$info({
        title: '提示',
        content: '该确定结束该测验？',
        okText: '确定',
        keyboard: false,
        onOk() {
          that.$emit('ppvtEnd')
        }
      })
    },

    //计时器计时开始
    startTime() {
      clearInterval(this.timeInterval)
      this.initTime()

      this.timeInterval = setInterval(() => {
        this.second = this.second + 1;
        if (this.second >= 60) {
          this.second = 0;
          this.minute = this.minute + 1;
        }
 
        if (this.minute >= 60) {
          this.minute = 0;
          this.hour = this.hour + 1;
        }
        this.time = this.complZero(this.hour) + ":" + this.complZero(this.minute) + ":" + this.complZero(this.second);
      }, 1000);
    },
    //格式化计时时间00:00:00
    complZero(n) {
      return n < 10 ? "0" + n : "" + n;
    },
    initTime() {
      this.hour = 0
      this.minute = 0
      this.second = 0
      this.time = "00:00:00"
    },

    handleRemarkBlur(e) {
      this.$set(this.question, 'optionRemark', e.target.value)
      this.$emit('setRemark', {questionId: this.question.id, remark: this.question.optionRemark})
    },

    handleRemarkBlur2() {
      this.$set(this.question, 'optionRemark', this.remark1 + this.remark2)
      this.$emit('setRemark', {questionId: this.question.id, remark: this.question.optionRemark})
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-item {
  padding: 0 100px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .choose-item-top {
    width: 100%;
    min-height: 300px;
    text-align: center;

    .choose-item-title {
      width: 100%;
      margin: 0;
      padding: 0 150px 20px 150px;
      font-size: 28px;
      text-align: left;
    }
    .choose-item-title-ppvt {
      margin: 0;
      font-size: 28px;
      text-align: left;
    }

    .choose-item-img {
      width: 600px;
    }

    .time-interval-view {
      padding: 10px 150px 10px 150px;
      display: flex;
      align-items: center;

      .time-container {
        margin-left: 30px;
        font-size: 20px;
        font-weight: 500;
      }
    }
  }

  .choose-item-answer {
    width: 100%;
    padding: 50px 130px 0 130px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .answer-item {
      display: flex;
      padding: 10px 20px 20px 20px;

      .ant-btn {
        height: 40px;
        margin-right: 10px;
      }

      .choose-btn {
        background: green;
      }
      .ant-btn:active {
        background: green;
      }

      .item-text {
        display: inline-block;
        font-size: 24px;
        line-height: 40px;
        cursor: pointer;
      }

      .item-img {
        width: 150px;
        cursor: pointer;
        background: #eee;
        border: 1px solid #eee;
      }
    }
  }

  .connect-item-remark {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    padding-top: 20px;

    span {
      width: 90px;
    }

    textarea {
      flex: 1;
      border-color: #ccc;
      border-radius: 4px;
    }
  }
}
</style>
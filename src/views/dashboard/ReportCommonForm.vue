<template>
  <a-drawer
    :title='title'
    :maskClosable='true'
    :width='drawerWidth'
    placement='right'
    :closable='true'
    destroyOnClose
    @close='handleCancel'
    :visible='visible'>
    <a-spin :spinning='loading'>
      <div v-if='!isEdit'>
        <div class='no-print' style='text-align: right;margin-right: 35px;'>
          <a-popover title='设置' trigger='click' placement='bottom' style='margin-right: 10px;'>
            <template slot='content'>
              <a-list>
                <a-list-item>
                  <a-switch slot='actions' size='small' :defaultChecked="this.userInfo().isShowIntroduction == '1'"
                            @change='onIntroduction' />
                  <a-list-item-meta>
                    <div slot='title'>报告简介</div>
                  </a-list-item-meta>
                </a-list-item>
                <a-list-item>
                  <a-switch slot='actions' size='small' :defaultChecked="this.userInfo().isShowOrgOption == '1'"
                            @change='onOrgOption' />
                  <a-list-item-meta>
                    <div slot='title'>原始数据</div>
                  </a-list-item-meta>
                </a-list-item>
              </a-list>
            </template>
            <a-button ghost type='primary'>
              <a-icon type='setting' />
            </a-button>
          </a-popover>
          <a-button v-print="'#reportCommonContent'" ghost type='primary' style='margin-right: 10px;'>打印</a-button>
          <a-button ghost type='primary' @click='viewEdit' style='margin-right: 10px;'>编辑</a-button>
        </div>
        <div id='reportCommonContent'>
          <p v-html='datas'></p>
        </div>
      </div>
      <div v-if='isEdit'>
        <a-spin :spinning='loading'>
          <a-row>
            <j-editor :j-height='700' v-model='editContent' />
          </a-row>
          <a-row style='margin-top: 15px;'>
            <a-col :span='18'></a-col>
            <a-col :span='6'>
              <a-button ghost type='primary' @click='saveResport' style='margin-right: 10px;'>确定</a-button>
              <a-button ghost type='primary' @click='cancelResport'>取消</a-button>
            </a-col>
          </a-row>
        </a-spin>
      </div>
    </a-spin>
  </a-drawer>
</template>

<script>
import { getAction } from '@/api/manage'
import JEditor from '@/components/jeecg/JEditor'
import { mapGetters } from 'vuex'

export default {
  name: 'ReportCommonForm',
  components: {
    JEditor
  },
  props: {
    type: {
      type: Number,
      default: 1, // 2: 档案详情 1: 其他
    }
  },
  data() {
    return {
      title: '操作',
      loading: false,
      drawerWidth: 700,
      visible: false,
      datas: '',
      isEdit: false,
      editContent: '',
      url: {
        getPrintInfoByResultId: '/diagnosis/dxResult/getPrintInfoByResultId',
        generatePrintHtml: '/diagnosis/dxResult/generatePrintHtml',
        generatePrintHtmlForOnePatient: '/diagnosis/dxResult/generatePrintHtmlForOnePatient'
      }
    }
  },
  methods: {
    ...mapGetters(['userInfo']),
    handleCancel() {
      this.close()
    },
    edit(record) {
      let that = this
      that.resetScreenSize() // 调用此方法,根据屏幕宽度自适应调整抽屉的宽度
      that.visible = true
      that.generatePrintHtml(record.id)
    },
    batchPrint(ids) {
      let that = this
      that.resetScreenSize() // 调用此方法,根据屏幕宽度自适应调整抽屉的宽度
      that.visible = true
      that.generatePrintHtml(ids)
    },
    generatePrintHtml(ids) {
      this.loading = true
      const url = this.type === 1 ? this.url.generatePrintHtml : this.url.generatePrintHtml
      getAction(url, { ids: ids }).then((res) => {
        if (res.success) {
          this.datas = res.result
          this.loading = false
        }
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
      this.isEdit = false
    },
    // 根据屏幕变化,设置抽屉尺寸
    resetScreenSize() {
      let screenWidth = document.body.clientWidth
      if (screenWidth < 500) {
        this.drawerWidth = screenWidth
      } else {
        this.drawerWidth = 900
      }
    },
    viewEdit() {
      let that_ = this
      this.isEdit = true
      this.editContent = this.datas
      this.loading = true
      setTimeout(function() {
        that_.loading = false
      }, 250)
    },
    saveResport() {
      this.isEdit = false
      this.datas = this.editContent
    },
    cancelResport() {
      this.isEdit = false
    },
    onOrgOption(checked) {
      if (checked) {
        document.getElementById('showOrgOptionStr').style.display = 'block'
      } else {
        document.getElementById('showOrgOptionStr').style.display = 'none'
      }
    },
    onIntroduction(checked) {
      if (checked) {
        document.getElementById('briefIntroducteStr').style.display = 'block'
      } else {
        document.getElementById('briefIntroducteStr').style.display = 'none'
      }
    }
  }
}
</script>

<style scoped>

</style>
<template>
  <a-modal
    :title='title'
    :width="device === 'desktop'?1200:920"
    :visible='visible'
    :confirmLoading='confirmLoading'
    @cancel='handleCancel'>
    <template slot='footer'>
      <a-button key='back' @click='handleCancel'>
        关闭
      </a-button>
      <a-button key='submit' type='primary' @click='handleOk'>
        确定
      </a-button>
    </template>
    <a-spin :spinning='confirmLoading'>
      <a-form :form='form'>
        <div>
          <a-row class='form-row' :gutter='16'>
            <a-col :lg='8'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='编号'>
                <a-auto-complete
                  option-label-prop='value'
                  v-model='model.userNumber'
                  @select='onSelect'
                  @change='onChange'
                  @search='handleSearch'
                >
                  <a-input placeholder='请输入编号' v-decorator="['userNumber', validatorRules.userNumber ]"
                           autocomplete='off'>
                    <a-tooltip slot='suffix' title='生成编号【已存在的测试者请输入原编号】'>
                      <a-icon type='redo' style='color: rgba(0,0,0,.45)' @click='generateNumber' />
                    </a-tooltip>
                  </a-input>
                  <template slot='dataSource'>
                    <a-select-option v-for='(item, index) in numDataSource' :key='index' :value='item.value'>
                      <p>{{ item.text }} {{ item.name }}</p>
                    </a-select-option>
                  </template>
                </a-auto-complete>
              </a-form-item>
            </a-col>
            <a-col :lg='8'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='姓名'>
                <a-auto-complete
                  option-label-prop='name'
                  v-model='model.name'
                  @change='onChangeName'
                  @search='handleSearchName'
                >
                  <a-input placeholder='请输入姓名' v-decorator="['name', validatorRules.userNumber ]"
                           autocomplete='off'>
                  </a-input>
                  <template slot='dataSource'>
                    <a-select-option v-for='item in nameList' :key='item.value' :value='item.value'>
                      <p>{{ item.text }} {{ item.name }}</p>
                    </a-select-option>
                  </template>
                </a-auto-complete>
              </a-form-item>
            </a-col>
            <a-col :lg='8'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='性别'>
                <j-dict-select-tag v-decorator="['sex', validatorRules.sex]" :triggerChange='true'
                                   placeholder='请选择性别'
                                   dictCode='sex' />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row class='form-row' :gutter='16'>
            <a-col :lg='8'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='年龄'>
                <a-input-number placeholder='请输入年龄' :min='1' :max='150' :precision='0'
                                v-decorator="['age', validatorRules.age ]" style='width: 100%' />
              </a-form-item>
            </a-col>
            <a-col :lg='8'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='联系电话'>
                <a-input placeholder='请输入联系电话' v-decorator="['telphone', validatorRules.telphone ]"
                         autocomplete='off' />
              </a-form-item>
            </a-col>
            <a-col :lg='8'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='住院号'>
                <a-input placeholder='请输入住院号' v-decorator="['legalCaseNo', validatorRules.legalCaseNo ]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row class='form-row' :gutter='16'>
            <a-col :lg='8'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='测试类型'>
                <j-dict-select-tag v-decorator="['preDiagnosis', validatorRules.preDiagnosis]" :triggerChange='true'
                                   placeholder='请选择测试类型' dictCode='pre_diagnosis' />
              </a-form-item>
            </a-col>
            <a-col :lg='8'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='婚姻状况'>
                <j-dict-select-tag v-decorator="['maritalStatus', validatorRules.maritalStatus]" :triggerChange='true'
                                   placeholder='请选择婚姻状况' dictCode='marital_status' />
              </a-form-item>
            </a-col>
            <a-col :lg='8'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='文化程度'>
                <j-dict-select-tag v-decorator="['cultural', validatorRules.cultural]" :triggerChange='true'
                                   placeholder='请选择文化程度' dictCode='cultural' />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row class='form-row' :gutter='16'>
            <a-col :lg='8'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='职业'>
                <a-input placeholder='请输入职业' v-decorator="['profession', validatorRules.profession ]" />
              </a-form-item>
            </a-col>
            <a-col :lg='8'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='被试来源'>
                <j-dict-select-tag v-decorator="['testerSource', validatorRules.testerSource]" :triggerChange='true'
                                   placeholder='请输入被试来源' dictCode='tester_source' />
              </a-form-item>
            </a-col>
            <a-col :lg='8'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='民族'>
                <j-dict-select-tag v-decorator="['nationality', validatorRules.nationality]" :triggerChange='true'
                                   placeholder='请选择民族' dictCode='nationality' />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter='16'>
            <a-col :lg='8'>
              <!-- <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='科室'>
                <a-input placeholder='请输入科室' v-decorator="['departmentName', validatorRules.departmentName ]" />
              </a-form-item> -->
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='科室'>
                <a-select
                  style='width: 100%'
                  placeholder='请选择科室'
                  show-search
                  mode='SECRET_COMBOBOX_MODE_DO_NOT_USE'
                  v-decorator="['departmentName', validatorRules.departmentName]">
                  <a-select-option v-for='item in departmentNameList' :key='item.id' :value='item.name'>
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :lg='8'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='生日'>
                <a-date-picker placeholder='请选择生日' style='width: 102%' v-decorator="[ 'birthday',{}]" />
              </a-form-item>
            </a-col>
            <a-col :lg='8'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='籍贯'>
                <a-input placeholder='请输入籍贯' v-decorator="['nativePlace', validatorRules.nativePlace ]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter='24' v-has="'user:form:isTerminal'">
            <a-col :lg='12'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='是否推送终端'>
                <a-select style='width:100%' placeholder='是否推送其他终端' v-decorator="[ 'isPushTerminal']"
                          @change='terminalHandleChange'>
                  <a-select-option :value='0'>否</a-select-option>
                  <a-select-option :value='1'>是</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :lg='12'>
              <a-form-item
                v-if='isPushTerminal == 1'
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='选择推送终端'>
                <a-select
                  style='width: 100%'
                  placeholder='请选择推送终端'
                  v-decorator="['selectedTerminal', validatorRules.selectedTerminal]">
                  <a-select-option v-for='user in users' :key='user.id'>
                    {{ user.realname }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import pick from 'lodash.pick'
import ACol from 'ant-design-vue/es/grid/Col'
import moment from 'moment'
import { queryUserByCurrentAccount } from '@/api/api'
import ARow from 'ant-design-vue/es/grid/Row'
import { mixinDevice } from '@/utils/mixin.js'
import { globalShowedAuth } from '@/utils/authFilter'

export default {
  name: 'UserForm',
  components: { ARow, ACol },
  mixins: [mixinDevice],
  data() {
    return {
      title: '操作',
      visible: false,
      model: {
        userNumber: ''
      },
      terminalId: '',
      numDataSource: [],
      userNumber: '',
      userId: '',
      isPushTerminal: 0,
      answerType: 0,
      selectedTerminal: '',
      users: [],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      currSelectedIds: [],
      package_id:'',
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        cultural: { rules: [{ required: false, message: '请输入文化程度!' }] },
        profession: { rules: [{ required: false, message: '请输入职业!' }] },
        userNumber: {
          rules: [{ required: true, message: '请输入编号!' }, {
            max: 25,
            message: '编号大于25位，请重新输入'
          }]
        },
        name: { rules: [{ required: true, message: '请输入姓名!' }] },
        sex: { rules: [{ required: true, message: '请选择性别!' }] },
        age: { rules: [{ required: true, message: '请输入年龄!' }] },
        legalCaseNo: { rules: [{ required: false, message: '请输入住院号！' }] },
        preDiagnosis: { rules: [{ required: false, message: '请选择测试类型!' }] },
        testerSource: { rules: [{ required: false, message: '请输入被试来源!' }] },
        nationality: { rules: [{ required: false, message: '请输入民族!' }] },
        nativePlace: { rules: [{ required: false, message: '请输入籍贯!' }] },
        departmentName: { rules: [{ required: false, message: '请输入科室信息!' }] },
        telphone: { rules: [{ required: false, pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号!' }] },
        maritalStatus: { rules: [{ required: false, message: '请选择婚姻状况!' }] },
        selectedTerminal: { rules: [{ required: true, message: '请选择推送终端!' }] }
      },
      url: {
        addForHome: '/psychology/psUser/addForHome',
        userList: '/psychology/psUser/findUserNumberList',
        queryById: '/psychology/psUser/queryById',
        checkUnfinishedMeasure: '/psychology/psUser/checkUnfinishedMeasure',
        pushTerminalByUsers: '/psychology/psUser/pushTerminalByUsers',
        generNumber: '/psychology/psUser/generNumber',
        findNameList: '/psychology/psUser/findNameList',
        addPsUser: '/psychology/psUser/addPsUser',
        hospitalOfficeList: '/system/sysDepartHospitalOffice/list'
      },
      nameList: [],
      departmentNameList: []
    }
  },
  created() {
  },
  beforeDestroy() {
  },
  methods: {
    moment,
    loadMeasureIds(currSelectedIds,package_id) {
      this.reSetData()
      this.currSelectedIds = currSelectedIds;
      this.package_id = package_id;
      this.visible = true
      this.handleSearchDepartment()
    },
    pushTerminalLoad(currSelectedIds, terminalId) {
      console.log('222')
      this.reSetData()
      this.currSelectedIds = currSelectedIds
      this.terminalId = terminalId
      this.visible = true
      this.handleSearchDepartment()
    },
    reSetData() {
      this.model = {
        userNumber: ''
      }
      this.form = this.$form.createForm(this)
      this.userId = ''
      this.numDataSource = []
      this.nameList = []
    },
    edit(record) {
      const formData = this.form.getFieldsValue(['isPushTerminal', 'selectedTerminal'])
      this.form.resetFields()
      const data = {
        ...formData,
        ...record
      }
      this.model = Object.assign({}, data)
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'cultural', 'profession', 'userNumber', 'legalCaseNo', 'name', 'sex', 'age', 'preDiagnosis',
          'departmentName', 'testerSource', 'nationality', 'nativePlace', 'departmentName', 'telphone', 'maritalStatus', 'isPushTerminal', 'selectedTerminal'))
        //时间格式化
        this.form.setFieldsValue({ birthday: this.model.birthday ? moment(this.model.birthday) : null })
      })
    },
    close() {
      this.selectedTerminal = ''
      this.$emit('close')
      this.reSetData()
      this.visible = false
    },
    handleOk() {
      // 触发表单验证
      this.form.validateFields(async (err, values) => {
        if (!err) {
          let formData = Object.assign(this.model, values)
          //时间格式化
          formData.birthday = formData.birthday ? formData.birthday.format('YYYY-MM-DD') : null
          if (this.isPushTerminal == 0) {
            //暂时废弃提示信息，因为首页已经有继续答题入口
            //直接跳转答题页面
            // formData.measureIds = this.currSelectedIds
            // that.pushAnswerPage(formData)
            this.goAnswerPage(formData)
          } else {
            const data = await this.addPsUser(formData)
            formData.id = data.id
            const params = {
              userIds: formData.selectedTerminal,
              psUserId: formData.id
            }
            this.pushTerminalByUsers(params)
          }
        }
      })
    },
    pushTerminalByUsers(params) {
      getAction(this.url.pushTerminalByUsers, {
        ...params,
        measureIds: this.currSelectedIds.join(','),
        package_id:this.package_id
      }).then((res) => {
        if (res.success) {
          this.$notification['success']({
            message: '添加成功',
            duration: 3,
            description: '已在选择的终端推送答题信息'
          })
          this.reSetData()
          this.confirmLoading = false
          this.close()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    async goAnswerPage(formData) {
      let that = this
      formData.measureIds = this.currSelectedIds;
      formData.package_id = this.package_id;
      let res = await postAction(that.url.checkUnfinishedMeasure, formData)
      if (res.success) {
        that.pushAnswerPage(formData)
      } else {
        if (res.code == 10001) {
          this.$confirm({
            title: '提示',
            content: res.message,
            okText: '新增',
            cancelText: '覆盖',
            onOk() {
              formData.id = null
              that.pushAnswerPage(formData)
            },
            onCancel() {
              that.pushAnswerPage(formData)
            }
          })
        } else {
          that.$message.error(res.message)
          //提示是否做未完成的测量题目
          //TODO 主页有继续答题流程，此处暂时注释
          /*            this.$confirm({
                        title: '提示',
                        content: '有未完成的诊断测试，是否继续答题？',
                        onOk() {
                          that.answerType = 1
                          that.pushAnswerPage(formData)
                        },
                        onCancel() {
                          that.pushAnswerPage(formData)
                        }
                      })*/
        }
      }
      // this.reSetData()
    },
    pushAnswerPage(formData) {
      let that = this
      let httpurl = that.url.addForHome
      formData.answerType = that.answerType
      if (this.terminalId) {
        formData.terminalId = this.terminalId
      }
      postAction(httpurl, formData).then((res) => {
        if (res.success) {
          this.$notification['success']({
            message: '添加成功',
            duration: 3,
            description: '已在选择的终端推送答题信息'
          })
          that.confirmLoading = false
          that.close()
        } else {
          that.$message.error(res.message)
        }
      })
    },
    addPsUser(formData) {
      let that = this
      let httpurl = that.url.addPsUser
      formData.measureIds = this.currSelectedIds
      formData.package_id = this.package_id
      formData.answerType = that.answerType
      if (this.terminalId) {
        formData.terminalId = this.terminalId
      }
      return postAction(httpurl, formData).then((res) => {
        if (res.success) {
          return res.result
        } else {
          that.$message.error(res.message)
          return false
        }
      })
    },
    handleCancel() {
      this.close()
    },
    handleSearch(value) {
      let type = 1
      if (globalShowedAuth('user:search')) {
        type = 2
      }
      getAction(this.url.userList, { userNumber: value, type: type }).then((res) => {
        if (res.success) {
          this.numDataSource = !value ? [] : res.result
        }
      })
    },
    onBlur() {
      if (this.userId) {
        getAction(this.url.queryById, { id: this.userId }).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    terminalHandleChange(value) {
      this.isPushTerminal = value
      if (value == 1) {
        //加载终端列表
        this.loadTerminalList()
      }
    },
    onChange(value) {
      // this.userId = value
      // this.onBlur()
    },
    onSelect(value) {
      this.userId = value
      this.onBlur()
    },
    onChangeName(value) {
      this.userId = value
      this.onBlur()
    },
    onchangeDepartment(value) {

    },
    handleSearchDepartment(value) {
      getAction(this.url.hospitalOfficeList, { pageNo: 1, pageSize: 50 }).then((res) => {
        if (res.success) {
          this.departmentNameList = res.result.records || []
        } else {
          this.$message.error(res.message)
        }
      })
    },
    generateNumber() {
      let that = this
      that.form.resetFields()
      that.userId = ''
      getAction(that.url.generNumber, {}).then((res) => {
        if (res.success) {
          this.model.userNumber = res.result
          this.$nextTick(() => {
            this.form.setFieldsValue(pick(this.model, 'userNumber'))
          })
        } else {
          that.$message.error(res.message)
        }
      })
    },
    loadTerminalList() {
      queryUserByCurrentAccount({}).then((res) => {
        if (res.success) {
          this.users = res.result
        } else {
          // console.log(res.message)
        }
      })
    },
    handleSearchName(value) {
      let type = 1
      if (globalShowedAuth('user:search')) {
        type = 2
      }
      getAction(this.url.findNameList, { name: value, type: type }).then((res) => {
        if (res.success) {
          this.nameList = res.result || []
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>

<style scoped>
/deep/ .ant-model-content {
  position: relative;
  background-color: #fff;
  background-clip: padding-box;
  border: 0;
  border-radius: 2px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  pointer-events: auto;
  max-width: 1000px;
  display: inline-block;
}
</style>
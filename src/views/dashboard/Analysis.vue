<template>
  <div>
    <a-row :gutter="16">
      <a-col :xs="24" :sm="24" :md="24" :lg="9" :xl="6">
        <MeasureList ref="MeasureList" @changeTagShow="changeTagShow"></MeasureList>
      </a-col>
      <a-col :xs="24" :sm="24" :md="24" :lg="15" :xl="18">
        <a-card v-show="tagShow" title="游戏分类" :bordered="false" style="margin-bottom: 16px;">
          <GameMenu></GameMenu>
        </a-card>
        <a-card v-show="!tagShow" title="测试者档案" :bordered="false" style="margin-bottom: 16px;">
          <UserList ref="UserList"></UserList>
        </a-card>
        <a-card v-show="!tagShow" :bordered="false">
          <ResultList ref="ResultList"></ResultList>
        </a-card>
      </a-col>
    </a-row>
    <!-- 患者表单 -->
    <UserForm ref="UserForm"/>
  </div>
</template>

<script>
  import ChartCard from '@/components/chart/ChartCard'
  import ACol from 'ant-design-vue/es/grid/Col'
  import ATooltip from 'ant-design-vue/es/tooltip/Tooltip'
  import MiniArea from '@/components/chart/MiniArea'
  import MiniBar from '@/components/chart/MiniBar'
  import MiniProgress from '@/components/chart/MiniProgress'
  import RankList from '@/components/chart/RankList'
  import Bar from '@/components/chart/Bar'
  import Trend from '@/components/Trend'
  import { getLoginfo } from '@/api/api'
  import UserList from './UserList'
  import ResultList from './ResultList'
  import MeasureList from './MeasureList'
  import GameMenu from './GameMenu.vue'
  import UserForm from '../dashboard/UserForm'
  import { WebsocketMixin } from '@/mixins/WebsocketMixin'

  export default {
    name: 'Analysis',
    mixins: [WebsocketMixin],
    components: {
      ATooltip,
      ACol,
      ChartCard,
      MiniArea,
      MiniBar,
      MiniProgress,
      RankList,
      Bar,
      Trend,
      UserList,
      ResultList,
      MeasureList,
      UserForm,
      GameMenu
    },
    data() {
      return {
        loading: true,
        loginfo: {},
        tagShow: false
      }
    },
    mounted() {
    },
    created() {
      //获取微信传入的code，手动登录
      setTimeout(() => {
        this.loading = !this.loading
      }, 1000)
      this.initLogInfo()
    },
    methods: {
      initLogInfo() {
        getLoginfo(null).then((res) => {
          if (res.success) {
            this.loginfo = res.result
          }
        })
      },
      changeTagShow(show) {
        this.tagShow = show
      }
    }
  }
</script>

<style lang="scss" scoped>
// body {
//   overscroll-behavior-y: contain;
// }
</style>
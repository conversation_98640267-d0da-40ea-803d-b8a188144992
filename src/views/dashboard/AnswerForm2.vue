<template>
  <div class="iframa-page" tyle="user-select:none;">
    <iframe class="iframa" :src="url"></iframe>
  </div>
</template>

<script>
  import { GetRequest } from '@/components/_util/util'
  export default {
    name: 'iframeBox',
    data() {
      return {
        url:"https://www.baidu.com"
      }
    },
    watch: {
      audioChange(val) {
        if (val) {
          this.playAudio()
        } else {
          voiceCancel()
        }
      }
    },
    created() {
      console.log(GetRequest().hash)
      //初始化答题页数据
      this.loadData()
    },
    methods: {}
  }
</script>

<style scoped>
.iframa-page,
.iframa{
  width: 100%;
  height: 100%;
}
</style>
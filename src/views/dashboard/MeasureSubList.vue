<template>
  <div>
    <a-card :bodyStyle="style1">
      <a-alert type="info" :showIcon="true">
        <div slot="message">
          当前选择：
          <a v-if="this.title.length > 0" style="display: flex; flex-direction: column;">
            <span v-for="label,index of this.titles" :key="index">{{ label }}</span>
          </a>
          <a v-if="this.title.length > 0" style="margin-left: 10px" @click="onClearSelected">取消选择</a>
          <a v-if="this.title.length > 0" style="margin-left: 10px" @click="submitSelected">开始答题</a>
        </div>
      </a-alert>
      <a-input-search @search="onSearch" :disabled="hasRoleFZD" style="width:100%;margin-top: 10px;" placeholder="请输入量表名称" />
    </a-card>
    <a-card :bordered="false" style="height:612px;overflow:auto;" :bodyStyle="style2">
      <a-skeleton :loading="loading" style="padding-left:16px;margin-top: 5px;" active>
        <div style="background: #fff;padding-left:16px;height: 100%; margin-top: 5px">
          <!-- 树-->
          <a-col :md="10" :sm="24">
            <template>
              <a-dropdown :trigger="[this.dropTrigger]" @visibleChange="dropStatus">
                   <span style="user-select: none;">
                      <a-tree
                        :checkable="!hasRoleFZD"
                        @check="onCheck"
                        @rightClick="rightHandle"
                        @select="onTreeNodeSelect"
                        :selectedKeys=[]
                        :checkedKeys="checkedKeys"
                        :treeData="measureTree"
                        :checkStrictly="true"
                        :expandedKeys="iExpandedKeys"
                        :autoExpandParent="autoExpandParent"
                        :defaultExpandParent="defaultExpandParent"
                        @expand="onExpand" />
                    </span>
              </a-dropdown>
            </template>
          </a-col>
        </div>
      </a-skeleton>
    </a-card>
    <!-- 表单区域 -->
    <UserForm ref="UserForm" @ok="modalFormOk" />
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { searchMeasureTree } from '@/api/api'
import UserForm from './UserForm'
import Vue from 'vue'
import { USER_ROLE } from '@/store/mutation-types'

export default {
  name: 'MeasureList',
  mixins: [JeecgListMixin],
  components: {
    UserForm
  },
  data() {
    return {
      description: '量表列表页面',
      loading: true,
      currSelected: {},
      style1: {
        'padding-bottom': '10px',
        // 'padding-top': '6px'
      },
      style2: {
        'padding-left': '0px',
        'padding-top': '0px'
      },
      measureTree: [],
      checkIds: [],
      titles: [],
      dropTrigger: '',
      title: '',
      checkedKeys: [],
      rightClickSelectedKey: '',
      iExpandedKeys: [],
      autoExpandParent: false,
      defaultExpandParent: false,
      isSearchState: false,
      url: {
        list: '/psychology/psCategory/queryTreeList'
      },
      hasRoleFZD: Vue.ls.get(USER_ROLE, []).includes('fzddt'),
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  created() {
  },
  methods: {
    loadData() {
      var that = this
      that.measureTree = []
      searchMeasureTree(null).then((res) => {
        if (res.success) {
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            that.measureTree.push(temp)
            that.setThisExpandedKeys(temp)
            that.loading = false
          }
          this.loading = false
        }
      })
    },
    setThisExpandedKeys(node) {
      if (node.children && node.children.length > 0) {
        this.iExpandedKeys.push(node.key)
        for (let a = 0; a < node.children.length; a++) {
          this.setThisExpandedKeys(node.children[a])
        }
      }
    },
    onClearSelected() {
      this.checkedKeys = {}
      this.selectedKeys = []
      this.checkIds = []
      this.title = ''
      this.titles = []
    },
    onSearch(value) {
      let that = this
      if (value) {
        that.isSearchState = true
        searchMeasureTree({ measureName: value }).then((res) => {
          if (res.success) {
            that.measureTree = []
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              that.measureTree.push(temp)
            }
          } else {
            that.$message.warning(res.message)
          }
        })
      } else {
        that.isSearchState = false
        that.loadData()
      }
    },
    // 右键点击下拉框改变事件
    dropStatus(visible) {
      if (visible == false) {
        this.dropTrigger = ''
      }
    },
    //选中事件
    onTreeNodeSelect(selectedKeys, e) {
      //触发复选框的勾选事件
      e.node.$el.childNodes[1].click()
    },
    onCheck(checkedKeys, info) {
      if (!this.isSearchState) {
        this.titles = []
        this.checkIds = []
      }
      this.checkedKeys = checkedKeys.checked

      //搜索量表操作中，并且进行取消选择操作
      if (!info.checked && this.isSearchState) {
        //取差集
        let differenceSet = this.currentCheckIds.filter(item => this.checkedKeys.includes(item) == false);

        let index = this.currentCheckIds.indexOf(differenceSet[0])
        this.titles.splice(index, 1); // 从数组中删除指定下标的对象
        this.checkIds = this.checkedKeys
      } else {
        info.checkedNodes.forEach(item => {
          this.titles.push(item.data.props.title)
          this.checkIds.push(item.key)
        })
      }

      this.titles = Array.from(new Set(this.titles))
      this.checkIds = Array.from(new Set(this.checkIds))

      this.currentCheckIds = Array.from(new Set(this.checkIds))

      this.title = this.titles.join(',')
    },
    // 右键操作方法
    rightHandle(node) {
      this.dropTrigger = 'contextmenu'
      this.rightClickSelectedKey = node.node.eventKey
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.iExpandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    submitSelected() {
      //保存
      this.$refs.UserForm.loadMeasureIds(this.checkIds)
      this.$refs.UserForm.title = '添加测试者信息'
    },
    nodeModalOk() {
    },
    nodeModalClose() {
    }
  }
}
</script>
<style lang="less" scoped>
.ant-card-bordered {
  border: 0px solid #e8e8e8
}
</style>
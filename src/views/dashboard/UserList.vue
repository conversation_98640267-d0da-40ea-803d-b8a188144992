<template>
  <a-skeleton :loading="skeleton" active>
    <a-table
      ref="table"
      size="middle"
      bordered
      rowKey="id"
      :columns="columns"
      :dataSource="data"
      :pagination="ipagination"
      :loading="loading"
      @change="handleTableChange">
        <span slot="action" slot-scope="text, record">
          <a @click="goUserDetail(record.id)">档案详情</a>
          <a-divider v-has="'home:list:cognize:action'" type="vertical"/>
          <a v-has="'home:list:cognize:action'" @click="goCognize(record)">认知训练</a>
        </span>
    </a-table>
    <!--  认知推荐  -->
    <CognizeRecommend ref="CognizeRecommend"></CognizeRecommend>
  </a-skeleton>
</template>

<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import {getAction} from '@/api/manage'
import CognizeRecommend from '@views/dashboard/CognizeRecommend'
import { colAuthFilter, globalShowedAuth } from '@/utils/authFilter'

export default {
  name: 'UserList',
  mixins: [JeecgListMixin],
  components: {
    CognizeRecommend
  },
  data() {
    return {
      description: '测试者信息管理页面',
      data: [],
      loading: true,
      skeleton: true,
      // 患者信息表头
      columns: [
        {
          title: '编号',
          align: 'center',
          dataIndex: 'userNumber'
        },
        {
          title: '姓名',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '联系电话',
          align: 'center',
          dataIndex: 'telphone'
        },
        {
          title: '年龄',
          align: 'center',
          dataIndex: 'age'
        },
        {
          title: '性别',
          align: 'center',
          dataIndex: 'sex_dictText'
        },
        {
          title: '婚否',
          align: 'center',
          dataIndex: 'maritalStatus_dictText'
        },
        {
          title: '文化程度',
          align: 'center',
          dataIndex: 'cultural_dictText'
        },
        {
          title: '职业',
          align: 'center',
          dataIndex: 'profession'
        },
        {
          title: '测试类型',
          align: 'center',
          dataIndex: 'preDiagnosis_dictText'
        },
        {
          title: '被试来源',
          align: 'center',
          dataIndex: 'testerSource_dictText'
        },
        {
          title: '操作',
          dataIndex: 'actionParam',
          align: 'center',
          scopedSlots: {customRender: 'action'}
        }
      ],
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 5,
        showSizeChanger: false,
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        total: 0
      },
      url: {
        userList: '/psychology/psUser/list'
      }
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  created() {
    //初始化患者列表
    this.loadData()
    //权限控制
    this.initColumns()
  },
  methods: {
    loadData(arg) {
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var param = {}
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      param.column = 'createTime'
      param.order = 'desc'
      getAction(this.url.userList, param).then((res) => {
        if (res.success) {
          let result = res.result.records;
          if (globalShowedAuth('resultList:actionParam')) {
            this.data = result
          } else {
            this.data = []
            for (let i = 0; i < result.length; i++) {
              if (result[i].telphone) {
                result[i].telphone = result[i].telphone.replace(/^(.{3})(?:\d+)(.{4})$/, "$1****$2");
              }
              if (result[i].name) {
                result[i].name = this.hideInsurantName(result[i].name);
              }
              this.data.push(result[i])
            }
          }
          this.ipagination.total = res.result.total;
          this.loading = false
          this.skeleton = false
        }
      })
    },
    handleTableChange(pagination, filters, sorter) {
      this.loading = true
      //分页、排序、筛选变化时触发
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      this.loadData()
    },
    goCognize(record) {
      this.$refs.CognizeRecommend.loadUserId(record.id)
      this.$refs.CognizeRecommend.title = '智能推送训练'
    },
    goUserDetail(id) {
      this.$router.push({path: '/psychology/modules/PsUserDetailModal/' + id})
    },
    hideInsurantName (val) {
      if (!val || val === '') return ''
      let name = ''
      if (val.length === 2) {
        name = val.substring(0, 1) + '*' // 截取name 字符串截取第一个字符，
      } else if (val.length === 3) {
        name = val.substring(0, 1) + '*' + val.substring(2, 3) // 截取第一个和第三个字符
      } else if (val.length === 4) {
        name = val.substring(0, 2) + '*' + '*' // 4个字隐藏后面两个
      } else if (val.length > 4) {
        name = val.substring(0, 1) // 5个字只显示第一个字
        for (let i = 0; i < val.length - 1; i++) {
          name = name + '*'
        }
      }
      return name
    },
    initColumns() {
      //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
      this.columns = colAuthFilter(this.columns, 'resultList:')
    },
  }
}
</script>
<style lang="less" scoped>
/** Button按钮间距 */
.ant-btn {
  margin-left: 3px
}

.ant-card-body .table-operator {
  margin-bottom: 18px;
}

.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

.anty-row-operator button {
  margin: 0 5px
}

.ant-btn-danger {
  background-color: #ffffff
}

.ant-modal-cust-warp {
  height: 100%
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden
}
</style>
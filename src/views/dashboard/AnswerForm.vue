<template>
  <div style="user-select: none">
    <a-spin size="large" :spinning="spinning">
      <a-row :style="{ background: '#4495D1', height: device === 'desktop' ? '100px' : '50px' }">
        <a-col :xs="5" :sm="7" :md="8" :lg="5" :xl="5"></a-col>
        <a-col
          class="mearsure-title"
          :style="{ margin: device === 'desktop' ? '70px auto 0' : '15px auto 0' }"
          :xs="15"
          :sm="10"
          :md="14"
          :lg="14"
          :xl="15"
        >
          {{ measure.name }}
        </a-col>
        <a-col :xs="5" :sm="10" :md="1" :lg="5" :xl="4"></a-col>
      </a-row>
      <a-row>
        <!-- 量表信息确认开始 -->
        <div v-show="!isShowQuestion">
          <a-row style="margin-top: 10px">
            <a-col :xs="14" :sm="14" :md="14" :lg="14" :xl="14"></a-col>
            <a-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
              <div class="timer">
                <a-switch default-checked v-model="audioChange" @change="handleAudioChange" />
                <span style="margin-left: 5px">语音读题</span>
              </div>
              <div class="timer">
                <a-icon type="clock-circle" style="padding-right: 10px" theme="twoTone" />
                <span>{{ timer }}</span>
              </div>
              <div class="timer">
                <a-icon type="ordered-list" style="padding-right: 10px" />
                <span>{{ '--/' + quesionList.length }}</span>
              </div>
            </a-col>
            <a-col :xs="2" :sm="2" :md="2" :lg="2" :xl="2"></a-col>
          </a-row>
          <a-row v-if="subsectionTitle && device === 'desktop'" style="margin-top: 20px">
            <a-col :span="6"></a-col>
            <a-col :span="3">
              <h3 style="font-weight: bold">{{ subsectionTitle }}</h3>
            </a-col>
            <a-col :span="5"></a-col>
          </a-row>
          <a-row>
            <a-col :xs="5" :sm="5" :md="2" :lg="2" :xl="5"></a-col>
            <a-col :xs="14" :sm="14" :md="20" :lg="20" :xl="14">
              <p v-html="measure.description" class="description"></p>
            </a-col>
            <a-col :xs="5" :sm="5" :md="2" :lg="2" :xl="5"></a-col>
          </a-row>
          <a-row style="text-align: center; margin-top: 50px">
            <a-col :span="5"></a-col>
            <a-col :span="14">
              <a-button type="primary" size="large" @click="quesionStart">开始</a-button>
            </a-col>
            <a-col :span="14"></a-col>
            <a-col :span="5"></a-col>
          </a-row>
        </div>
        <!-- 量表信息确认结束 -->
        <!-- 答题开始 -->
        <div v-show="isShowQuestion">
          <a-row style="margin-top: 10px">
            <a-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6"></a-col>
            <a-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
              <a-alert
                v-if="notAnswerArr.length > 0"
                message="有未答完的题目，请点击左侧滑动条中未完成的题目信息进行补充答题。"
                type="error"
              />
            </a-col>
            <a-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
              <div class="timer">
                <a-switch default-checked v-model="audioChange" @change="handleAudioChange" />
                <span style="margin-left: 5px">语音读题</span>
              </div>
              <div class="timer">
                <a-icon type="clock-circle" style="padding-right: 10px" theme="twoTone" />
                <span>{{ timer }}</span>
              </div>
              <div class="timer">
                <a-icon type="ordered-list" style="padding-right: 10px" />
                <span>{{ quesionNum + '/' + quesionList.length }}</span>
              </div>
            </a-col>
            <a-col :xs="2" :sm="2" :md="2" :lg="2" :xl="2"></a-col>
          </a-row>
          <a-row v-if="subsectionTitle" style="margin-top: 10px">
            <a-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6"></a-col>
            <a-col :xs="3" :sm="3" :md="13" :lg="13" :xl="3">
              <h3 style="font-weight: bold">{{ subsectionTitle }}</h3>
            </a-col>
            <a-col :xs="5" :sm="5" :md="5" :lg="5" :xl="5"></a-col>
          </a-row>
          <a-row v-if="subsectionSubTitle" style="margin-top: 10px; margin-left: 30px">
            <a-col :xs="6" :sm="6" :md="6" :lg="6" :xl="6"></a-col>
            <a-col :xs="13" :sm="13" :md="13" :lg="13" :xl="13">
              <h3 style="font-weight: bold">{{ subsectionSubTitle }}</h3>
            </a-col>
            <a-col :xs="5" :sm="5" :md="5" :lg="5" :xl="5"></a-col>
          </a-row>
          <a-row>
            <a-col
              :xs="5"
              :sm="5"
              :md="5"
              :lg="5"
              :xl="5"
              :style="{ position: quesionList && quesionList[quesionIndex] && ['7', '8'].indexOf(quesionList[quesionIndex].titleType) != -1 ? 'absolute' : '' }"
            >
              <div
                :style="
                  device === 'desktop'
                    ? 'display: inline-block;height: 450px;marginLeft: 70px'
                    : 'display: inline-block;height: 200px;marginLeft: 70px'
                "
              >
                <a-slider
                  :marks="marks"
                  vertical
                  :max="quesionList.length"
                  :value="quesionNum"
                  @change="onChangeSlider"
                />
              </div>
            </a-col>
            <a-col v-if="quesionList && quesionList[quesionIndex] && ['7', '8'].indexOf(quesionList[quesionIndex].titleType) != -1 " :xs="24" :sm="24" :md="24" :lg="24" :xl="24" style="text-align: left">
              <p
                v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].title"
                v-html="quesionList[quesionIndex].title"
                :class="device === 'desktop' ? 'description' : 'descriptionMobile'"
              ></p>
              <img
                class="descriptionImg"
                v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].imgPath && quesionList[quesionIndex].titleType != '7' && quesionList[quesionIndex].titleType != '8'"
                alt="example"
                style="width: 40%; height: 30%"
                :src="url.imgerver + '/' + quesionList[quesionIndex].imgPath"
              />
            </a-col>
            <a-col v-else :xs="14" :sm="14" :md="14" :lg="14" :xl="14" style="text-align: left">
              <p
                v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].title"
                v-html="quesionList[quesionIndex].title"
                :class="device === 'desktop' ? 'description' : 'descriptionMobile'"
              ></p>
              <img
                class="descriptionImg"
                v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].imgPath && quesionList[quesionIndex].titleType != '7' && quesionList[quesionIndex].titleType != '8'"
                alt="example"
                style="width: 40%; height: 30%"
                :src="quesionList[quesionIndex].imgPath"
              />
            </a-col>
            <a-col :xs="5" :sm="5" :md="5" :lg="5" :xl="5"></a-col>
          </a-row>
          <div class="footer">
            <div>
              <a-row style="display: flex; justify-content: center">
                <!-- title -->
                <a-col
                  v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '1' || quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '2'"
                  style="
                    max-width: 100%;
                    text-align: center;
                    user-select: none;
                    display: flex;
                    justify-content: space-between;
                    flex-wrap: wrap;
                  "
                >
                  <template v-for="(item, index) in quesionList[quesionIndex].psOptions">
                    <div
                      :key="index"
                      :id="item.id"
                      class="optionDiv"
                      @click="choose($event)"
                      :style="`display: flex; { 'item.type == 1' ? --textLength:${item.content.length + 4}em;:  }`"
                    >
                      <a-button type="primary" size="large" style="margin-right: 10px">{{ item.title }}</a-button>
                      <span v-if="item.type == 1">{{ item.content }}</span>
                      <img
                        v-if="item.type == 2"
                        alt="example"
                        style="width: 60%; height: 60%"
                        :src="item.content"
                      />
                    </div>
                  </template>
                </a-col>
                <!--自输入-分值-->
                <a-col :span="3" v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '3'">
                  <span class="description">分值：</span>
                </a-col>
                <!--自输入-内容-->
                <a-col :span="5" v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '4'">
                  <span class="description2">输入内容：</span>
                </a-col>
                <!--自输入-时间-->
                <a-col :span="5" v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '5'">
                  <span class="description2">输入时间：</span>
                </a-col>
                <!--自输入-数值-->
                <a-col :span="5" v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '6'">
                  <span class="description2">请输入：</span>
                </a-col>

                <!--自输入-分值-->
                <a-col :span="7" v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '3'" style="margin-top: 6px">
                  <div class="optionDiv" v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '3'">
                    <a-input-number
                      v-model="handImportValue"
                      placeholder="请输入分值"
                      :min="0"
                      :max="100"
                      :precision="0"
                      style="width: 100%"
                      @change="handImportChange"
                    />
                  </div>
                </a-col>
                <a-col :span="7" v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '3'" style="margin-top: 6px">
                  <!-- 手动输入分值确定-->
                  <a-button @click="choose($event)" type="primary" style="margin-right: 5px; border-radius: 4px">
                    确定
                    <a-icon type="arrow-right" />
                  </a-button>
                </a-col>
                <!--自输入-内容-->
                <a-col :span="12" v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '4'" style="margin-top: 6px">
                  <div class="optionDiv" v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '4'">
                    <a-textarea
                      v-model="handImportValue"
                      placeholder="请输入内容"
                      :min="0"
                      :max="100"
                      :precision="0"
                      style="width: 100%"
                    />
                  </div>
                </a-col>
                <a-col :span="7" v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '4'" style="margin-top: 6px">
                  <a-button @click="choose($event)" type="primary" style="margin-right: 5px; border-radius: 4px">
                    确定
                    <a-icon type="arrow-right" />
                  </a-button>
                </a-col>
                <!--自输入-时间-->
                <a-col :span="7" v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '5'" style="margin-top: 6px">
                  <div v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '5'">
                    <a-time-picker v-model="handImportValue" format="HH:mm" />
                  </div>
                </a-col>
                <a-col :span="7" v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '5'" style="margin-top: 6px">
                  <!-- 手动输入时间确定-->
                  <a-button @click="choose($event)" type="primary" style="margin-right: 5px; border-radius: 4px">
                    确定
                    <a-icon type="arrow-right" />
                  </a-button>
                </a-col>
                <!--自输入-数值-->
                <a-col :span="12" v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '6'" style="margin-top: 6px">
                  <div class="optionDiv">
                    <a-input-number
                      v-model="handImportValue"
                      placeholder="请输入数值"
                      :min="0"
                      :max="100"
                      :precision="0"
                      style="width: 100%"
                      @change="handImportChange"
                    />
                  </div>
                </a-col>
                <a-col :span="7" v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '6'" style="margin-top: 6px">
                  <!-- 手动输入分值确定-->
                  <a-button @click="choose($event)" type="primary" style="margin-right: 5px; border-radius: 4px">
                    确定
                    <a-icon type="arrow-right" />
                  </a-button>
                </a-col>
              </a-row>
            </div>
            <!-- 画钟  -->
            <div v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '7'" class="interact-canvas">
              <div class="interact-canvas-box" v-if="showInteractCanvas">
                <img
                  class="interact-canvas-image"
                  :src="url.imgerver + '/' + quesionList[quesionIndex].imgPath"
                  alt=""
                />
                <div class="interact-canvas-content">
                  <div class="interact-canvas-clear-btn" @click="clearCanvas('interact_type_canvas')">清空</div>
                  <canvas id="interact_type_canvas" class="interact_type_canvas" width="480px" height="480px"></canvas>
                </div>
                <div class="values">
                  <div class="label">分值：</div>
                  <a-input-number
                    v-model="handImportValue"
                    placeholder="请输入分值"
                    :min="0"
                    :max="100"
                    :precision="0"
                    style="width: 200px"
                    @change="handImportChange"
                  />
                  <!-- <div class="interact-answer-action" @click="choose($event)">确定</div> -->
                  <a-button @click="uploadChoose($event)" type="primary" style="margin-left: 5px; border-radius: 4px">
                    确定
                    <a-icon type="arrow-right" />
                  </a-button>
                </div>
              </div>
            </div>
            <!--    数字广度        -->
            <div class="interact-number-box" v-if="quesionList && quesionList[quesionIndex] && quesionList[quesionIndex].titleType == '8'">
              <audio ref="music" muted controls="controls" style="display:none">
                <source :src="quesionList[quesionIndex].imgPath" type="audio/mpeg" />
              </audio>
              <div class="interact-buttons">
                <div
                  class="interact-button"
                  v-for="item of 9"
                  :key="item"
                  @click="
                    () => {
                      numberAnswer.push(item)
                    }
                  "
                >
                  {{ item }}
                </div>
              </div>
              <div class="interact-answer">
                <div class="interact-answer-label">您的答案是：</div>
                <div class="interact-answer-input" v-for="(item, index) of numberAnswer" :key="index">
                  {{ item }}
                </div>
                <a-button
                  @click="
                    () => {
                      numberAnswer.splice(numberAnswer.length - 1, 1)
                    }
                  "
                  style="margin-left: 5px; border-radius: 4px"
                >
                  回删
                </a-button>
                <a-button @click="choose($event)" type="primary" style="margin-left: 5px; border-radius: 4px">
                  确定
                  <a-icon type="arrow-right" />
                </a-button>
              </div>
            </div>
            <a-row style="text-align: right; margin-top: 20px; display: flex; justify-content: flex-end">
              <a-col>
                <a-button-group size="large">
                  <a-button @click="previousPage" type="primary" style="margin-right: 5px; border-radius: 4px">
                    <a-icon type="left" />
                    上一题
                  </a-button>
                  <a-button @click="nextPage" type="primary" style="border-radius: 4px">
                    下一题
                    <a-icon type="right" />
                  </a-button>
                </a-button-group>
              </a-col>
            </a-row>
          </div>
        </div>
        <!-- 答题结束 -->
      </a-row>
    </a-spin>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import ACol from 'ant-design-vue/es/grid/Col'
import ARow from 'ant-design-vue/es/grid/Row'
import { mixinDevice } from '@/utils/mixin.js'
import { voiceCancel, voicePrompt } from '@/utils/util'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import Vue from 'vue'

export default {
  name: 'AnswerForm',
  components: { ARow, ACol },
  mixins: [mixinDevice],
  data() {
    return {
      title: '操作',
      timer: '00:00:00',
      time: 0,
      audioChange: false,
      totalTime: 0,
      timerHour: 0,
      timerMinute: 0,
      timerSecond: 0,
      spinning: true,
      completeCount: 0,
      quesionNum: 1,
      userId: '',
      type: '',
      subsectionTitle: '',
      subsectionSubTitle: '',
      subsectionContent: '',
      measure: {},
      //未答完题目标准指针
      notAnswerArr: [],
      isShowQuestion: false,
      interactCanvasValue: '',
      showInteractCanvas: false,
      quesionList: [],
      quesionIndex: 0,
      numberAnswer: [],
      canvasImgUrl: '',
      dxResult: {},
      marks: {},
      /*当前题目手动输入值*/
      handImportValue: '',
      url: {
        loadAnswerPageData: '/diagnosis/dxAnswer/loadAnswerPageData',
        addOption: '/diagnosis/dxResult/addOption',
        getNotAnswer: '/diagnosis/dxAnswer/getNotAnswer',
        imgerver: window._CONFIG['domianURL'],
      },
    }
  },
  watch: {
    audioChange(val) {
      if (val) {
        this.playAudio()
      } else {
        voiceCancel()
      }
    },
    quesionIndex(val) {
      if (this.quesionList && this.quesionList[val] && this.quesionList[val].titleType == '7') {
        this.showInteractCanvas = true
      } else {
        this.showInteractCanvas = false
      }
    },
    showInteractCanvas(val) {
      if (val) {
        this.initCanvas()
      }
    },
  },
  created() {
    this.interact_type_canvas = null
    //初始化答题页数据
    this.loadData()
  },
  methods: {
    uploadChoose() {
      let myCanvas = document.getElementById('interact_type_canvas')
      let dataurl = myCanvas.toDataURL('image/png', 1.0)
      var arr = dataurl.split(',')
      var mime = arr[0].match(/:(.*?);/)[1]
      var bstr = atob(arr[1])
      var n = bstr.length
      var u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      // window._CONFIG['domianURL']+"/sys/common/upload"
      // const token = Vue.ls.get(ACCESS_TOKEN);
      // this.headers = {"X-Access-Token":token}
      let flieParam = new FormData()
      flieParam.append('file', new File([new Blob([u8arr], { type: mime })], 'huazhong.png'))
      flieParam.append('biz', 'temp/image/huazhong')
      fetch(window._CONFIG['domianURL'] + '/sys/common/upload', {
        method: 'post',
        body: flieParam,
        headers: { 'X-Access-Token': Vue.ls.get(ACCESS_TOKEN) },
      })
        .then((response) => response.text())
        .then((result) => {
          this.canvasImgUrl = JSON.parse(result).message
          this.choose()
        })
      return
    },
    getDom(id) {
      return new Promise((resolve, reject) => {
        let fun = () => {
          let myCanvas = document.getElementById(id)
          if (myCanvas) {
            resolve()
          } else {
            setTimeout(() => {
              fun()
            }, 500)
          }
        }
        fun()
      })
    },
    initCanvas() {
      this.getDom('interact_type_canvas').then(() => {
        //1.获取canvas
        let myCanvas = document.getElementById('interact_type_canvas')
        //获取2d对象
        this.interact_type_canvas = myCanvas.getContext('2d')
        //控制线条是否画
        let isMouseMove = false
        //线条位置
        let lastX, lastY
        //画线
        let drawLine = (x, y, isT) => {
          if (isT) {
            this.interact_type_canvas.beginPath()
            this.interact_type_canvas.lineWidth = 2 //设置线宽状态
            this.interact_type_canvas.strokeStyle = '#000000' //设置线的颜色状态
            this.interact_type_canvas.lineCap = 'round'
            this.interact_type_canvas.lineJoin = 'round'
            this.interact_type_canvas.moveTo(lastX, lastY)
            this.interact_type_canvas.lineTo(x, y)
            this.interact_type_canvas.stroke()
            this.interact_type_canvas.closePath()
          }
          // 每次移动都要更新坐标位置
          lastX = x
          lastY = y
        }

        let down = (e) => {
          isMouseMove = true
          let myCanvasBox = document.getElementsByClassName('interact-canvas-content')[0]
          let myCanvasBox2 = document.getElementsByClassName('interact-canvas')[0]
          drawLine(
            e.pageX - myCanvasBox.offsetLeft - myCanvasBox2.offsetLeft,
            e.pageY - myCanvasBox.offsetTop - myCanvasBox2.offsetTop - 100,
            false
          )
        }
        let move = (e) => {
          if (isMouseMove) {
            let myCanvasBox = document.getElementsByClassName('interact-canvas-content')[0]
            let myCanvasBox2 = document.getElementsByClassName('interact-canvas')[0]
            //offsetLeft
            drawLine(
              e.pageX - myCanvasBox.offsetLeft - myCanvasBox2.offsetLeft,
              e.pageY - myCanvasBox.offsetTop - myCanvasBox2.offsetTop - 100,
              true
            )
          }
        }
        let up = (e) => {
          isMouseMove = false
        }
        let leave = (e) => {
          isMouseMove = false
        }
        myCanvas.addEventListener('mousedown', down)
        myCanvas.addEventListener('mousemove', move)
        myCanvas.addEventListener('mouseup', up)
        myCanvas.addEventListener('mouseleave', leave)
        // function saveImgInfo() {

        // }
        // imgInfo.addEventListener('click', saveImgInfo)
      })
    },
    clearCanvas(id) {
      let myCanvas = document.getElementById(id)
      this.interact_type_canvas.beginPath()
      this.interact_type_canvas.clearRect(0, 0, myCanvas.width, myCanvas.height)
      this.interact_type_canvas.closePath() //可加入，可不加入
    },
    playAudio() {
      voiceCancel()
      if (this.audioChange) {
        //题目信息
        if (!this.isShowQuestion) {
          voicePrompt(
            '接下来会朗读测试前的描述，请注意倾听后根据要求答题：' +
              this.measure.description +
              '描述朗读完毕，请点击开始按钮开始答题。',
            4
          )
        } else {
          let test = ''
          if (this.quesionList[this.quesionIndex].titleType == '1' && this.quesionList[this.quesionIndex].title) {
            test = test + '，' + this.quesionList[this.quesionIndex].title
          }
          this.quesionList[this.quesionIndex].psOptions.forEach(function (e) {
            test = test + '，' + e.title + e.content
          })
          test = test + '，选项朗读完毕，请选择适合您的选项。'
          voicePrompt(test, 3)
        }
      }
    },
    handleAudioChange(fixed) {
      this.audioChange = fixed
    },
    /**
     * 加载量表信息数据
     */
    loadData() {
      const that = this
      that.spinning = true
      that.type = that.$route.params.type
      let params = {}
      if (that.type == '1' || that.type == '0') {
        that.userId = that.$route.params.userId
        params = { userId: that.$route.params.userId, type: that.$route.params.type }
      } else if (that.type == '2') {
        params = { resultId: that.$route.params.resultId, type: that.$route.params.type }
      }
      let httpurl = that.url.loadAnswerPageData
      getAction(httpurl, params).then((res) => {
        if (res.success) {
          if (res.result) {
            that.spinning = false
            that.isShowQuestion = false
            that.notAnswerArr = []
            if (res.result.measure) that.measure = res.result.measure
            if (res.result.quesionList) {
              that.quesionList = res.result.quesionList
              if (that.quesionList && that.quesionList[that.quesionIndex] && that.quesionList[that.quesionIndex].titleType == '7') {
                that.showInteractCanvas = true;
              }else {
                that.showInteractCanvas = false;
              }
            }
            if (res.result.quesionIndex != null) {
              that.quesionIndex = res.result.quesionIndex
              that.completeCount = res.result.quesionIndex
              that.quesionNum = res.result.quesionIndex + 1
              that.subsectionContent = res.result.quesionList[res.result.quesionIndex].subsectionContent
              that.subsectionTitle = res.result.quesionList[res.result.quesionIndex].subsectionTitle
              that.subsectionSubTitle = res.result.quesionList[res.result.quesionIndex].subsectionSubTitle
            }
            if (res.result.dxResult) that.dxResult = res.result.dxResult
          } else {
            that.$router.push({
              name: 'dashboard',
            })
            this.$notification['success']({
              message: '测试完成',
              description: that.measure.name + '量表测试已完成',
            })
          }
        } else {
          that.$message.warning('数据异常，请返回首页后重新开始答题。（我们保存了答题进度，点击继续答题开始续答）', 5)
          setTimeout(function () {
            that.$router.push({
              name: 'dashboard',
            })
          }, 2500)
        }
      })
    },
    /**
     * 答题开始
     */
    quesionStart() {
      this.isShowQuestion = true
      let date = new Date()
      this.time = date.getTime()
      this.totalTime = date.getTime()
      setInterval(() => {
        this.startTimer()
      }, 10)
      this.playAudio()
      if (this.quesionList && this.quesionList[this.quesionIndex] && this.quesionList[this.quesionIndex].titleType == '8') {
        setTimeout(() => {
          this.$refs.music.load()
          this.$refs.music.play()
        }, 2000);
      }
    },
    startTimer() {
      let that = this
      let date = new Date()
      let intervals = date.getTime() - that.time
      var b = (intervals % 60000) / 1000
      var c = (intervals % 3600000) / 60000
      var d = intervals / 3600000
      that.timerSecond = b < 10 ? '0' + Math.floor(b) : Math.floor(b)
      that.timerMinute = c < 10 ? '0' + Math.floor(c) : Math.floor(c)
      that.timerHour = d < 10 ? '0' + Math.floor(d) : Math.floor(d)
      that.timer = that.timerHour + ':' + that.timerMinute + ':' + that.timerSecond
    },
    /**
     * 上一页
     * */
    previousPage() {
      if (this.quesionIndex >= 1) {
        this.quesionIndex--
        if (this.quesionList[this.quesionIndex].subsectionContent) {
          this.isShowQuestion = false
        }
        this.subsectionContent = this.quesionList[this.quesionIndex].subsectionContent
        this.subsectionTitle = this.quesionList[this.quesionIndex].subsectionTitle
        this.subsectionSubTitle = this.quesionList[this.quesionIndex].subsectionSubTitle
      }
      this.quesionNum--
      this.playAudio()
      if (that.quesionList[that.quesionIndex].titleType == '8') {
        setTimeout(() => {
          this.$refs.music.load()
          this.$refs.music.play()
        }, 2000);
      }
    },
    /**
     * 下一页
     */
    nextPage() {
      if (this.interact_type == 2) {
        if (!this.interactCanvasValue) {
          return false
        }
      }
      if (this.quesionIndex + 1 < this.quesionList.length) {
        this.quesionIndex++
        if (this.quesionList[this.quesionIndex].subsectionContent) {
          this.isShowQuestion = false
        }
        this.subsectionContent = this.quesionList[this.quesionIndex].subsectionContent
        this.subsectionTitle = this.quesionList[this.quesionIndex].subsectionTitle
        this.subsectionSubTitle = this.quesionList[this.quesionIndex].subsectionSubTitle
      }
      this.quesionNum++
      this.playAudio()
      this.refreshNotAnswer(this.quesionNum)
      if (this.quesionList && this.quesionList[this.quesionIndex] && this.quesionList[this.quesionIndex].titleType == '8') {
        setTimeout(() => {
          this.$refs.music.load()
          this.$refs.music.play()
        }, 2000);
      }
    },
    /**
     * 选择选项
     * @param e
     */
    choose(e) {
      const that = this
      let httpurl = that.url.addOption
      let optionInfo = {}
      let date = new Date()
      optionInfo.userId = that.userId
      optionInfo.measureId = that.measure.id
      if (e) {
        optionInfo.optionId = e.currentTarget.id
      }
      optionInfo.resultId = that.dxResult.id
      optionInfo.questionId = that.quesionList[that.quesionIndex].id
      optionInfo.time = date - that.totalTime
      optionInfo.totalTime = date - that.time
      that.totalTime = date
      /*自输入-分值*/
      if (that.quesionList[that.quesionIndex].titleType == '3') {
        if (!that.handImportValue && that.handImportValue != 0) {
          this.$message.warning('请填写当前项目的分值！')
          return
        }
        optionInfo.type = '3'
        optionInfo.score = that.handImportValue
        //清除数据
        that.handImportValue = null
      }
      /*自输入-画钟*/
      if (that.quesionList[that.quesionIndex].titleType == '7') {
        if (!that.handImportValue && that.handImportValue != 0) {
          this.$message.warning('请填写当前项目的分值！')
          return
        }
        optionInfo.type = '3'
        optionInfo.score = that.handImportValue
        optionInfo.answer = that.canvasImgUrl
        //清除数据
        that.handImportValue = null
      }
      /*自输入-数字*/
      if (that.quesionList[that.quesionIndex].titleType == '8') {
        let score = 0
        if (that.numberAnswer.join('-') == that.quesionList[that.quesionIndex].answer) {
          score = 1
        }
        optionInfo.type = '3'
        optionInfo.score = score
        //清除数据
        that.handImportValue = null
        that.numberAnswer = []
      }
      /*自输入-数值*/
      if (that.quesionList[that.quesionIndex].titleType == '6') {
        if (!that.handImportValue && that.handImportValue != 0) {
          this.$message.warning('请填写当前项目的数值！')
          return
        }
        optionInfo.type = '6'
        optionInfo.score = 0
        optionInfo.answer = that.handImportValue
        //清除数据
        that.handImportValue = null
      }
      /*自输入-内容*/
      if (that.quesionList[that.quesionIndex].titleType == '4') {
        if (!that.handImportValue && that.handImportValue.length == 0) {
          this.$message.warning('请填写当前项目需要回复的内容！')
          return
        }
        optionInfo.type = '4'
        optionInfo.score = 0
        optionInfo.answer = that.handImportValue
        //清除数据
        that.handImportValue = null
      }
      /*自输入-时间*/
      if (that.quesionList[that.quesionIndex].titleType == '5') {
        if (!that.handImportValue && that.handImportValue.length == 0) {
          this.$message.warning('请填写当前项目需要回复的内容！')
          return
        }
        optionInfo.type = '5'
        optionInfo.score = 0
        optionInfo.answer = that.handImportValue ? that.handImportValue.format('HH:mm') : null
        //清除数据
        that.handImportValue = null
      }
      postAction(httpurl, optionInfo)
        .then((res) => {
          if (res.success) {
            if (res.code == 200) {
              that.completeCount++
            }
            //成功后去掉未答题列表中的对应数据
            this.notAnswerArr.splice(this.notAnswerArr.indexOf(that.quesionNum), 1)
            if (res.code === 10001) {
              that.loadData()
            } else {
              if (that.quesionNum == that.quesionList.length) {
                this.$message.warning('还有题目未答完')
                return;
              } else {
                that.quesionNum++
              }
            }
            this.refreshNotAnswer(this.quesionNum)
          } else {
            that.$message.warning(res.message)
          }
        })
        .catch((err) => {
          if (that.notAnswerArr.indexOf(that.quesionNum) === -1) {
            that.notAnswerArr.push(that.quesionNum)
          }
          that.quesionNum++
          that.completeCount++
          that.refreshNotAnswerArr()
        })
      //下一题
      if (that.quesionIndex + 1 < that.quesionList.length) {
        that.quesionIndex++
        if (that.quesionList[that.quesionIndex].subsectionContent) {
          that.isShowQuestion = false
        }
        that.subsectionContent = that.quesionList[that.quesionIndex].subsectionContent
        that.subsectionTitle = that.quesionList[that.quesionIndex].subsectionTitle
        that.subsectionSubTitle = that.quesionList[that.quesionIndex].subsectionSubTitle
        //如果是手动输入清除输入框中的值
        if (that.quesionList && that.quesionList[that.quesionIndex] && that.quesionList[that.quesionIndex].titleType == '3') {
          that.handImportValue = ''
        }
        this.playAudio()
        if (that.quesionList && that.quesionList[that.quesionIndex] && that.quesionList[that.quesionIndex].titleType == '8') {
          setTimeout(() => {
            this.$refs.music.load()
            this.$refs.music.play()
          }, 2000);
        }
      }
    },
    handleCancel() {
      this.close()
      voiceCancel()
    },
    onChangeSlider(value) {
      this.quesionNum = value
      this.quesionIndex = value - 1
    },
    /*手动输入值变化回调*/
    handImportChange(value) {
      this.handImportValue = value
    },
    refreshNotAnswer(val) {
      let that = this
      getAction(that.url.getNotAnswer, {
        measureId: that.measure.id,
        resultId: that.dxResult.id,
        questionNum: val,
      }).then((res) => {
        if (res.success && res.result) {
          that.notAnswerArr = that.unique(that.notAnswerArr, res.result.questionArr)
          that.refreshNotAnswerArr()
        }
      })
    },
    refreshNotAnswerArr() {
      let that = this
      let marks = {}
      for (let key in that.notAnswerArr) {
        marks[that.notAnswerArr[key]] = {
          style: {
            color: '#f50',
          },
          label: '第' + that.notAnswerArr[key] + '题未答完',
        }
      }
      that.marks = marks
    },
    unique(arr, arr2) {
      for (var i = 0; i < arr2.length; i++) {
        if (arr.indexOf(arr2[i]) === -1) {
          arr.push(arr2[i])
        }
      }
      return arr
    },
  },
  destroyed() {
    clearInterval(this.timer)
    voiceCancel()
  },
  showImg(url) {
    return this.url.imgerver + '/' + url
  },
}
</script>

<style lang="scss" scoped>
@import './AnswerForm.scss';
</style>
<template>
  <div>
    <a-skeleton :loading="skeleton" active>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="data"
        :pagination="ipagination"
        :loading="loading"
        tableLayout="auto"
        @change="handleTableChange">
        <span v-if="isShowAuth('resultList:actionParam')" slot="action" slot-scope="text, record">
              <a v-if="record.status == '2' && isAssessType(record)" @click="handleExportDoc(record)">报告下载</a>
              <a v-if="record.status != '2' && isAssessType(record)" @click="continueQuestions(record)">继续答题</a>
              <a-divider type="vertical" v-if="isAssessType(record)" />
              <a @click="handlePrintForm(record)">内容预览</a>
        </span>
        <span v-else slot="action" slot-scope="text, record">
              <a v-if="record.status != '2'" @click="continueQuestions(record)">继续答题</a>
              <a v-if="record.status == '2'">----</a>
        </span>
      </a-table>
    </a-skeleton>
    <!-- 打印表单页 -->
    <report-common-form ref="ReportForm"></report-common-form>
    <!-- 眼动筛查报告 -->
    <dx-em-report-model ref="emReportFrom"></dx-em-report-model>
  </div>
</template>

<script>

import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { downFile, getAction } from '@/api/manage'
import ReportCommonForm from './ReportCommonForm'
import DxEmReportModel from '@views/diagnosis/modules/DxEmReportModel'
import { colAuthFilter, globalShowedAuth } from '@/utils/authFilter'

export default {
  name: 'DxResultList',
  components: { ReportCommonForm, DxEmReportModel },
  mixins: [JeecgListMixin],
  data() {
    return {
      description: '诊断结果管理页面',
      data: [],
      anwerStatusDictOptions: [],
      loading: true,
      skeleton: true,
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 3,
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showSizeChanger: false,
        total: 0
      },
      // 表头
      columns: [
        {
          title: '编号',
          align: 'center',
          dataIndex: 'userNo'
        },
        {
          title: '主试',
          align: 'center',
          dataIndex: 'userName'
        },
        {
          title: '操作人',
          align: 'center',
          dataIndex: 'doctorName'
        },
        {
          title: '量表',
          align: 'center',
          dataIndex: 'measureName'
        },
        {
          title: '总粗分',
          align: 'center',
          dataIndex: 'totalPoints'
        },
        {
          title: '测评用时',
          align: 'center',
          dataIndex: 'timeStr'
        },
        {
          title: '答题状态',
          align: 'center',
          dataIndex: 'status_dictText'
        },
        {
          title: '答题时间',
          align: 'center',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 请求参数
      url: {
        list: '/diagnosis/dxResult/list',
        exportDocUrl: 'diagnosis/dxResult/exportSingleDoc',
        goContinueQuestionPage: '/psychology/psUser/goContinueQuestionPage'
      },
      isorter: {
        column: 'updateTime',
        order: 'desc'
      }
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  created() {
    //初始化结果列表
    this.loadData()
    //权限控制
    this.initColumns()
  },
  methods: {
    loadData(arg) {
      console.log('1111')
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams()
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          let result = res.result.records;
          this.data = []
          if (globalShowedAuth('resultList:actionParam')) {
            this.data = result
          } else {
            for (let i = 0; i < result.length; i++) {
              if (result[i].userName) {
                result[i].userName = this.hideInsurantName(result[i].userName);
              }
              this.data.push(result[i])
            }
          }
          this.ipagination.total = res.result.total
          this.loading = false
          this.skeleton = false
        }
      })
    },
    continueQuestions(record) {
      let that = this
      let httpurl = that.url.goContinueQuestionPage
      let formData = {}
      formData.resultId = record.id
      getAction(httpurl, formData).then((res) => {
        if (res.success) {
          this.$notification['success']({
            message: '添加成功',
            duration: 3,
            description: '已在选择的终端推送答题信息'
          })
        } else {
          that.$message.warning(res.message)
        }
      }).finally(() => {
        that.confirmLoading = false
      })
    },
    handleExportDoc(record) {
      if (record.status != '2') {
        this.$message.error('未答完题目不能查看报告！')
        return
      }
      this.downLoading = true
      var fileName = record.userName + '_' + record.measureName + '.pdf'
      let param = { ...this.queryParam }
      delete param.createTimeRange // 时间参数不传递后台
      param.ids = record.id
      downFile(this.url.exportDocUrl, param).then((data) => {
        if (!data) {
          this.$message.warning('文件下载失败')
          this.downLoading = false
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), fileName)
          this.downLoading = false
        } else {
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(url) //释放掉blob对象
          this.downLoading = false
        }
      })
    },
    handlePrintForm(record) {
      if (record.status != '2') {
        this.$message.error('未答完题目不能预览！')
        return
      }
      // 眼动筛查报告
      if (record.assessType === 'em') {
        this.$refs.emReportFrom.edit(record)
        this.$refs.emReportFrom.title = '内容预览'
      } else {
        this.$refs.ReportForm.edit(record)
        this.$refs.ReportForm.title = '内容预览'
      }
    },
    handleTableChange(pagination, filters, sorter) {
      this.loading = true
      //分页、排序、筛选变化时触发
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      this.loadData()
    },
    initColumns() {
      //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
      this.columns = colAuthFilter(this.columns, 'resultList:')
    },
    isShowAuth(code) {
      return globalShowedAuth(code)
    },
    hideInsurantName (val) {
      if (!val || val === '') return ''
      let name = ''
      if (val.length === 2) {
        name = val.substring(0, 1) + '*' // 截取name 字符串截取第一个字符，
      } else if (val.length === 3) {
        name = val.substring(0, 1) + '*' + val.substring(2, 3) // 截取第一个和第三个字符
      } else if (val.length === 4) {
        name = val.substring(0, 2) + '*' + '*' // 4个字隐藏后面两个
      } else if (val.length > 4) {
        name = val.substring(0, 1) // 5个字只显示第一个字
        for (let i = 0; i < val.length - 1; i++) {
          name = name + '*'
        }
      }
      return name
    },
    isAssessType(record) {
      if (!record.assessType || record.assessType === 'scale') {
        return true
      }
      return false
    }
  }
}
</script>
<style lang="less" scoped>
/** Button按钮间距 */
.ant-btn {
  margin-left: 3px
}

.ant-card-body .table-operator {
  margin-bottom: 18px;
}

.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

.anty-row-operator button {
  margin: 0 5px
}

.ant-btn-danger {
  background-color: #ffffff
}

.ant-modal-cust-warp {
  height: 100%
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden
}
</style>
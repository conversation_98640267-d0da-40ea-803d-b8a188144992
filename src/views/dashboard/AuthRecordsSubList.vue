<template>
  <div>
    <a-card :bordered="false" style="height:612px;overflow:auto;" :bodyStyle="style">
      <a-skeleton :loading="loading" style="padding-left:16px;margin-top: 5px;" active>
        <div style="background: #fff;padding-left:16px;height: 100%; margin-top: 5px">
          <!-- 树-->
          <template>
            <a-list item-layout="horizontal" style="width:100%;" :data-source="authRecordsList">
              <a-list-item slot="renderItem" class="listContent" slot-scope="item">
                <a-row>
                  <a-col :span="18">
                    <a-list-item-meta>
                      <a slot="title">套餐名称:{{ item.name }}</a>
                      <span>{{ item.measureNames }}</span>
                    </a-list-item-meta>
                  </a-col>
                  <a-col :span="6">
                    <a-button key="submit" type="primary" @click="submitSelected(item)">
                      开始答题
                    </a-button>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="24">
                    <div v-for="(text, index) in item.measureNames.split(',')" :key="index">
                      {{ text }}
                      <a-button v-if="text.length > 0" type="link" style="color: red; padding: 0 5px;" @click="deleteMeasure(item, index)">x</a-button>
                    </div>
                  </a-col>
                </a-row>
              </a-list-item>
            </a-list>
          </template>
        </div>
      </a-skeleton>
    </a-card>
    <!-- 表单区域 -->
    <UserForm ref="UserForm" @ok="modalFormOk"/>
  </div>
</template>

<script>
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import {searchAuthRecord} from '@/api/api'
import UserForm from './UserForm'
import {getAction} from '@/api/manage'

export default {
  name: 'MeasureList',
  mixins: [JeecgListMixin],
  components: {
    UserForm
  },
  data() {
    return {
      description: '量表列表页面',
      loading: true,
      style: {
        'padding-left': '0px',
        'padding-top': '0px'
      },
      authRecordsList: [],
      checkIds: [],
      url: {
        addForHis: '/psychology/psUser/addForHis'
      }
    }
  },
  computed: {},
  created() {
  },
  methods: {
    loadData() {
      var that = this
      that.authRecordsList = []
      searchAuthRecord({'isTop': 1}).then((res) => {
        if (res.success) {
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            that.authRecordsList.push(temp)
            that.loading = false
          }
          this.loading = false
        }
      })
    },
    onClearSelected() {
      this.selectedKeys = []
      this.checkIds = []
    },
    deleteMeasure(item, index) {
      let measureNames = item.measureNames.split(',')
      let measureIds = item.measureIds.split(',')
      measureNames.splice(index, 1)
      measureIds.splice(index, 1)
      
      item.measureNames = measureNames.join(',')
      item.measureIds = measureIds.join(',')
      console.log(item)
    },
    submitSelected(item) {
      if (item.measureIds) {
        //保存
        let measureIds = item.measureIds
        this.$refs.UserForm.loadMeasureIds(measureIds.split(","),item.id)
        this.$refs.UserForm.title = '添加测试者信息'
      } else {
        this.$message.warning("量表套餐中未包含任何量表!")
      }
    },
    // // his系统对接
    // submitSelected(item) {
    //   if (item.measureIds) {
    //     let that = this
    //     let httpurl = that.url.addForHis
    //     getAction(httpurl, {"id": item.id}).then((res) => {
    //       if (res.success) {
    //         this.$notification['success']({
    //           message: '添加成功',
    //           duration: 3,
    //           description: '已在选择的终端推送答题信息'
    //         })
    //         that.confirmLoading = false
    //         that.close()
    //       } else {
    //         that.$message.error(res.message)
    //       }
    //     })
    //   } else {
    //     this.$message.warning("量表套餐中未包含任何量表!")
    //   }
    // },
  }
}
</script>
<style lang="less" scoped>
.ant-card-bordered {
  border: 0px solid #e8e8e8
}

.listContent {
  border: 1px solid #e8e8e8 !important;
  margin-bottom: 5px;
  border-radius: 10px;
  padding-left: 5px;
  padding-right: 5px;
}
.ant-list-item {
  align-items: stretch;
  flex-direction: column;
}
</style>
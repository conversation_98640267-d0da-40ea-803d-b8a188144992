<template>
  <div>
    <a-card :bodyStyle="style1">
      <a-alert type="info" :showIcon="true">
        <div slot="message">
          当前选择：
          <a v-if="this.title.length > 0">{{ this.title }}</a>
          <a v-if="this.title.length > 0" style="margin-left: 10px" @click="onClearSelected">取消选择</a>
          <a v-if="this.title.length > 0" style="margin-left: 10px" @click="submitSelected">开始训练</a>
        </div>
      </a-alert>
      <a-input-search @search="onSearch" :disabled="hasRoleFZD" style="width:100%;margin-top: 10px;" placeholder="请输入量表名称" />
    </a-card>
    <a-card :bordered="false" style="height:612px;overflow:auto;" :bodyStyle="style2">
      <div class="tab-text" @click="changeTagStatus">{{ tagShow ? '< 收起视图' : '展开视图 >' }}</div>
      <a-skeleton :loading="loading" style="padding-left:16px;margin-top: 5px;" active>
        <div style="background: #fff;padding-left:16px;height: 100%; margin-top: 5px">
          <!-- 树-->
          <a-col :md="10" :sm="24">
            <template>
              <a-dropdown :trigger="[this.dropTrigger]" @visibleChange="dropStatus">
                   <span style="user-select: none;">
                      <a-tree
                        :checkable="!hasRoleFZD"
                        @check="onCheck"
                        @rightClick="rightHandle"
                        :selectedKeys=[]
                        :checkedKeys="checkedKeys"
                        :treeData="cognizeTree"
                        :checkStrictly="true"
                        :expandedKeys="iExpandedKeys"
                        :autoExpandParent="autoExpandParent"
                        :defaultExpandParent="defaultExpandParent"
                        @expand="onExpand" />
                    </span>
              </a-dropdown>
            </template>
          </a-col>
        </div>
      </a-skeleton>
    </a-card>
    <!-- 表单区域 -->
    <UserForm ref="UserForm" @ok="modalFormOk" />
  </div>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { searchCognizeTree } from '@/api/api'
import UserForm from './UserForm2'
import Vue from 'vue'
import { USER_ROLE } from '@/store/mutation-types'

export default {
  name: 'MeasureList',
  mixins: [JeecgListMixin],
  components: {
    UserForm
  },
  props: {
    tagShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      description: '量表列表页面',
      loading: true,
      currSelected: {},
      style1: {
        'padding-bottom': '10px',
        'padding-top': '6px'
      },
      style2: {
        'padding-left': '0px',
        'padding-top': '0px'
      },
      cognizeTree: [],
      checkIds: [],
      titles: [],
      dropTrigger: '',
      title: '',
      checkedKeys: [],
      rightClickSelectedKey: '',
      iExpandedKeys: [],
      autoExpandParent: false,
      defaultExpandParent: false,
      isSearchState: false,
      url: {},
      hasRoleFZD: Vue.ls.get(USER_ROLE, []).includes('fzddt'),
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  mounted() {
    this.$bus.$on('setNodes', (keys, titles) => {
      this.checkedKeys = keys
      this.titles = Array.from(new Set(titles))
      this.checkIds = Array.from(new Set(keys))
      this.title = this.titles.join(',')
      this.$bus.$emit('setKeys', this.checkedKeys)
    })
  },
  methods: {
    changeTagStatus() {
      this.$emit('changeTagStatus')
    },

    loadData() {
      var that = this
      that.cognizeTree = []
      searchCognizeTree(null).then((res) => {
        if (res.success) {
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            that.cognizeTree.push(temp)
            that.setThisExpandedKeys(temp)
            that.loading = false
          }
          this.$bus.$emit('setMenu', that.cognizeTree)
          this.loading = false
        }
      })
    },
    setThisExpandedKeys(node) {
      if (node.children && node.children.length > 0) {
        this.iExpandedKeys.push(node.key)
        for (let a = 0; a < node.children.length; a++) {
          this.setThisExpandedKeys(node.children[a])
        }
      }
    },
    onClearSelected() {
      this.checkedKeys = {}
      this.selectedKeys = []
      this.checkIds = []
      this.title = ''
      this.titles = []
    },
    onSearch(value) {
      let that = this
      if (value) {
        that.isSearchState = true
        searchCognizeTree({ cognizeName: value }).then((res) => {
          if (res.success) {
            that.cognizeTree = []
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i]
              that.cognizeTree.push(temp)
            }
            that.$bus.$emit('setMenu', that.cognizeTree)
          } else {
            that.$message.warning(res.message)
          }
        })
      } else {
        that.isSearchState = false
        that.loadData()
      }
    },
    // 右键点击下拉框改变事件
    dropStatus(visible) {
      if (visible == false) {
        this.dropTrigger = ''
      }
    },
    onCheck(checkedKeys, info) {
      if (!this.isSearchState) {
        this.titles = []
        this.checkIds = []
      }
      this.checkedKeys = checkedKeys.checked
      info.checkedNodes.forEach(item => {
        this.titles.push(item.data.props.title)
        this.checkIds.push(item.key)
      })
      this.titles = Array.from(new Set(this.titles))
      this.checkIds = Array.from(new Set(this.checkIds))
      this.title = this.titles.join(',')
      this.$bus.$emit('setKeys', this.checkedKeys)
    },
    // 右键操作方法
    rightHandle(node) {
      this.dropTrigger = 'contextmenu'
      this.rightClickSelectedKey = node.node.eventKey
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.iExpandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    submitSelected() {
      //保存
      this.$refs.UserForm.loadtrainIds(this.checkIds)
      this.$refs.UserForm.title = '添加测试者信息'
    },
    nodeModalOk() {
    },
    nodeModalClose() {
    }
  }
}
</script>
<style lang="less" scoped>
.ant-card-bordered {
  border: 0px solid #e8e8e8
}

.tab-text {
  text-align: right;
  cursor: pointer;
}
</style>
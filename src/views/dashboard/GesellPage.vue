<template>
  <div class="gesell">
    <div class="gesell-button">
      <div :class="['button-item', chooseBtn === item.type && 'choose-button' ]" v-for="item in questionData" :key="item.type" @click="setChoose(item.type)">{{item.name}}</div>
    </div>

    <div class="gesell-content">
      <div class="content-item" v-for="(item, index) in questions" :key="index + 'item'">
        <p class="item-name">{{item.name}}</p>

        <div class="item-question" v-for="(ques, index) in item.questionList" :key="ques.id">
          <p class="question-name">{{ index + 1 }}. {{ ques.title }}</p>

          <div class="question-answer">
            <div
              class="answer-item"
              v-for="(answer, index) in ques.psOptions"
              :key="index"
              :id="answer.id"
              @click="chooseItem(ques, answer)"
            >
              <a-button :class="[(ques.optionTitle || ques.answer) === answer.title && 'choose-btn']" type="primary" size="large">{{ answer.title }}</a-button>
              <span class="item-text">{{ answer.content }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <a-button class="confirm-btn" type="primary" size="small" @click="sumbit">提交</a-button>
  </div>
</template>

<script>
export default {
  name: 'Gesell',

  props: {
    questionList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      chooseBtn: 1,
      questions: [],
      questionData: []
    }
  },

  mounted() {
    this.initQuestion()
  },

  methods: {
    initQuestion() {
      this.questionList.forEach(item => {
        let ageList = this.questionData.filter(it => it.type === item.type) // tab 阶段数据
        if (ageList.length) {
          let typeList = ageList[0].typeList && ageList[0].typeList.filter(it => it.name === item.subsectionSubTitle) || [] // 大类数据 例如大运动
          if (typeList.length) {
            typeList[0].questionList.push(item)
          } else {
            ageList[0].typeList.push({
              name: item.subsectionSubTitle,
              questionList: [item]
            })
          }
        } else {
          this.questionData.push({
            name: item.subsectionTitle,
            type: item.type,
            typeList: [{
              name: item.subsectionSubTitle,
              questionList: [item]
            }]
          })
        }
      })
      this.questions = this.questionData.filter(item => item.type === this.chooseBtn)[0].typeList
    },

    setChoose(index) {
      this.chooseBtn = index
      this.questions = this.questionData.filter(item => item.type === this.chooseBtn)[0].typeList
    },

    chooseItem(ques, item) {
      if ((ques.optionTitle || ques.answer) === item.title) {
        ques.optionTitle = null
        ques.answer = null
        return
      }

      if (ques.optionTitle) {
        ques.optionTitle = null
      }
      ques.answer = item.title
      this.$emit('submit', {optionId: item.id, type: ques.type, questionId: ques.id}, true)
    },

    sumbit() {
      this.$emit('commit')
    }
  }
}
</script>

<style lang="scss" scoped>
.gesell {
  text-align: center;
  .gesell-button {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    background: #F4F7FF;
    border-radius: 12px;
    overflow: hidden;

    .button-item {
      padding: 5px 12px;
      // margin: 6px;
      font-size: 14px;
      color: #333;
      // background: #F5F5F5;
      // border: 1px solid #999;
      // border-radius: 12px;
      cursor: pointer;
    }

    .choose-button {
      color: #2878FF;
      // background: #2878FF;
      // border: 1px solid #2878FF;
    }
  }

  .gesell-content {
    display: flex;
    padding-top: 20px;

    .content-item {
      flex: 1;
      padding: 0 8px;
      border-right: 1px dashed #eee;

      .item-name {
        font-size: 14px;
        text-align: center;
      }

      .item-question {
        padding: 10px 0;

        .question-name {
          font-size: 12px;
          text-align: left;
        }

        .question-answer {
          display: flex;
          flex-wrap: wrap;

          .answer-item {
            display: flex;
            padding: 10px;

            .ant-btn {
              height: 28px;
              line-height: 28px;
              margin-right: 10px;
              font-size: 12px;
              color: #1890ff;
              background: #fff;
              border: 1px solid #1890ff;
            }

            .choose-btn {
              color: #fff;
              background: #1890ff;
            }

            .item-text {
              display: inline-block;
              font-size: 12px;
              line-height: 28px;
              cursor: pointer;
            }
          }
        }
      }
    }

    .content-item:last-child {
      border: none;
    }
  }

  .confirm-btn {
    height: 32px;
    margin-top: 20px;
    line-height: 32px;
  }
}
</style>

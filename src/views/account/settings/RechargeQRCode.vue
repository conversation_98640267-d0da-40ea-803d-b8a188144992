<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :footer="null"
    @cancel="handleCancel"
  >
    <a-row :gutter="16">
      <a-col :span="4"></a-col>
      <a-col :span="16" style="text-align: center">
        请使用 微信 扫一扫
      </a-col>
      <a-col :span="4"></a-col>
    </a-row>
    <a-row :gutter="16" style="margin-bottom: 20px">
      <a-col :span="4"></a-col>
      <a-col :span="16" style="text-align: center">
        扫描二维码支付
      </a-col>
      <a-col :span="4"></a-col>
    </a-row>
    <a-row :gutter="16">
      <a-col :span="4"></a-col>
      <a-col :span="16">
        <a-spin :spinning="confirmLoading">
          <div id="qrcode" ref="qrCodeDiv"></div>
        </a-spin>
      </a-col>
      <a-col :span="4"></a-col>
    </a-row>
    <a-row :gutter="16" style="margin-top: 20px;">
      <a-col :span="4"></a-col>
      <a-col :span="16" style="text-align: center">
        二维码有效时长为2小时, 请尽快支付
      </a-col>
      <a-col :span="4"></a-col>
    </a-row>
  </a-modal>
</template>

<script>
  import QRCode from 'qrcodejs2'
  import { httpAction } from '@/api/manage'
  import ACol from 'ant-design-vue/es/grid/Col'

  export default {
    name: 'RechargeQRCode',
    components: { ACol },
    data() {
      return {
        productId: '',
        title: '扫描二维码',
        modalWidth: 460,
        visible: false,
        confirmLoading: false,
        url: {
          unifiedOrder: '/wx/pay/unifiedOrder'
        }
      }
    },
    methods: {
      show(productId) {
        let that = this
        that.confirmLoading = true
        if (!productId) {
          that.$message.warning('生成二维码失败!')
          return
        } else {
          that.productId = productId
          that.visible = true
          httpAction(that.url.unifiedOrder, { 'productId': productId }, 'post').then((res) => {
            if (res.success) {
              let qrcode = new QRCode(that.$refs.qrCodeDiv, {
                width: 280,
                height: 280, // 高度
                text: res.result.codeURL, // 二维码内容
                // render: 'canvas' // 设置渲染方式（有两种方式 table和canvas，默认是canvas）
                // background: '#f0f'
                // foreground: '#ff0'
              })
              that.confirmLoading = false
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
          })
        }
      },
      handleCancel() {
        this.close()
      },
      close() {
        this.$emit('close')
        this.visible = false
        this.$refs.qrCodeDiv.innerHTML = ''
      },
    }
  }
</script>

<style scoped>

</style>
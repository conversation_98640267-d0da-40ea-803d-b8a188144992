<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">

          <a-col :md="6" :sm="8">
            <a-form-item label="交易流水号">
              <a-input placeholder="请输入交易流水号" v-model="queryParam.serialNumber"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="测试者名称">
              <j-input placeholder="请输入测试者名称" v-model="queryParam.userName"></j-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :md="6" :sm="8">
              <a-form-item label="门诊号">
                <a-input placeholder="请输入门诊号" v-model="queryParam.userNumber"></a-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <!--    <div class="table-operator">
          <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
          <a-button type="primary" icon="download" @click="handleExportXls('交易订单明细')">导出</a-button>
          <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl"
                    @change="handleImportExcel">
            <a-button type="primary" icon="import">导入</a-button>
          </a-upload>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="batchDel">
                <a-icon type="delete"/>
                删除
              </a-menu-item>
            </a-menu>
            <a-button style="margin-left: 8px"> 批量操作
              <a-icon type="down"/>
            </a-button>
          </a-dropdown>
        </div>-->

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">
      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <dxTransactionBill-modal ref="modalForm" @ok="modalFormOk"></dxTransactionBill-modal>
  </a-card>
</template>

<script>
  import DxTransactionBillModal from './modules/DxTransactionBillModal'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import JInput from '@/components/jeecg/JInput'

  export default {
    name: 'DxTransactionBillList',
    mixins: [JeecgListMixin],
    components: {
      DxTransactionBillModal,
      JInput
    },
    data() {
      return {
        description: '交易订单明细管理页面',
        // 表头
        columns: [
          {
            title: '交易流水号',
            align: 'center',
            dataIndex: 'serialNumber'
          },
          {
            title: '门诊号',
            align: 'center',
            dataIndex: 'userNumber'
          },
          {
            title: '患者名称',
            align: 'center',
            dataIndex: 'userName'
          },
          {
            title: '主试',
            align: 'center',
            dataIndex: 'adminRealname'
          },
          {
            title: '终端类型',
            align: 'center',
            dataIndex: 'terminalType_dictText'
          },
          {
            title: '交易类型',
            align: 'center',
            dataIndex: 'transactionType_dictText'
          },
          {
            title: '总金额',
            align: 'center',
            dataIndex: 'totalPrice'
          },
          {
            title: '状态',
            align: 'center',
            dataIndex: 'status_dictText'
          },
          {
            title: '交易时间',
            align: 'center',
            dataIndex: 'tradingTime'
          }
        ],
        url: {
          list: '/bill/dxTransactionBill/list',
          delete: '/bill/dxTransactionBill/delete',
          deleteBatch: '/bill/dxTransactionBill/deleteBatch',
          exportXlsUrl: 'bill/dxTransactionBill/exportXls',
          importExcelUrl: 'bill/dxTransactionBill/importExcel'
        }
      }
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {}
  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>
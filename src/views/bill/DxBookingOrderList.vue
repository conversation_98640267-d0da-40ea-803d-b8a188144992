<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">

          <a-col :md="6" :sm="8">
            <a-form-item label="订单号">
              <a-input placeholder="请输入订单号" v-model="queryParam.sn"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="测试者">
              <j-input placeholder="请输入测试者姓名" v-model="queryParam.userName"></j-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="心理咨询师">
              <j-input placeholder="请输入心理咨询师姓名" v-model="queryParam.psychiatristName"></j-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <!--    <div class="table-operator">
          <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
          <a-button type="primary" icon="download" @click="handleExportXls('心理咨询预约订单表')">导出</a-button>
          <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl"
                    @change="handleImportExcel">
            <a-button type="primary" icon="import">导入</a-button>
          </a-upload>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="batchDel">
                <a-icon type="delete"/>
                删除
              </a-menu-item>
            </a-menu>
            <a-button style="margin-left: 8px"> 批量操作
              <a-icon type="down"/>
            </a-button>
          </a-dropdown>
        </div>-->

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
        selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <span slot="action" slot-scope="text, record">
<!--          <a @click="handleEdit(record)">编辑</a>-->
          <a @click="handleConfirm(record)">确认订单</a>

          <a-divider type="vertical"/>
          <!--          <a-dropdown>
                      <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
                      <a-menu slot="overlay">
                        <a-menu-item>
                          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                            <a>删除</a>
                          </a-popconfirm>
                        </a-menu-item>
                      </a-menu>
                    </a-dropdown>-->
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <dxBookingOrder-modal ref="modalForm" @ok="modalFormOk"></dxBookingOrder-modal>
  </a-card>
</template>

<script>
  import DxBookingOrderModal from './modules/DxBookingOrderModal'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import { getAction } from '@/api/manage'
  import JInput from '@/components/jeecg/JInput'

  export default {
    name: 'DxBookingOrderList',
    mixins: [JeecgListMixin],
    components: {
      DxBookingOrderModal,
      JInput
    },
    data() {
      return {
        description: '心理咨询预约订单表管理页面',
        // 表头
        columns: [
          {
            title: '订单号',
            align: 'center',
            dataIndex: 'sn'
          },
          {
            title: '心理咨询师',
            align: 'center',
            dataIndex: 'psychiatristName'
          },
          {
            title: '测试者姓名',
            align: 'center',
            dataIndex: 'userName'
          },
          {
            title: '测试者年龄',
            align: 'center',
            dataIndex: 'userAge'
          },
          {
            title: '测试者性别',
            align: 'center',
            dataIndex: 'userSex_dictText'
          },
          {
            title: '问题类型',
            align: 'center',
            dataIndex: 'problemType'
          },
          {
            title: '咨询方式',
            align: 'center',
            dataIndex: 'consultWay'
          },
          {
            title: '费用',
            align: 'center',
            dataIndex: 'totalPrice'
          },
          {
            title: '下单手机',
            align: 'center',
            dataIndex: 'phone'
          },
          {
            title: '订单状态',
            align: 'center',
            dataIndex: 'status_dictText'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: '/diagnosis/dxBookingOrder/list',
          delete: '/diagnosis/dxBookingOrder/delete',
          deleteBatch: '/diagnosis/dxBookingOrder/deleteBatch',
          exportXlsUrl: 'diagnosis/dxBookingOrder/exportXls',
          importExcelUrl: 'diagnosis/dxBookingOrder/importExcel',
          updateStatus: '/diagnosis/dxBookingOrder/updateStatus'
        }
      }
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {
      handleConfirm(record) {
        getAction(this.url.updateStatus, { id: record.id, status: 2 }).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.loadData()
          } else {
            this.$message.warning(res.message)
          }
        })
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>
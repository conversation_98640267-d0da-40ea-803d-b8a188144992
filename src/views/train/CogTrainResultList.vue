<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="测试者名称">
              <a-input placeholder="请输入测试者名称" v-model="queryParam.userName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="训练名称">
              <a-input placeholder="请输入训练名称" v-model="queryParam.trainName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="训练分类">
              <a-select v-model="queryParam.trainType" placeholder="请选择训练分类">
                <a-select-option
                  value="plane">
                  平面游戏
                </a-select-option>
                <a-select-option
                  value="vr">
                  VR游戏
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button type="primary" icon="download" @click="handleExportXls('训练结果')">导出</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete" />
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a
        style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <span slot="action" slot-scope="text, record">
          <a v-if="record.status == '0'" @click="continueTrain(record)">继续训练</a>
          <a v-if="record.status == '1'" @click="handlePrintForm(record)">报告预览</a>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <cogTrainResult-modal ref="modalForm" @ok="modalFormOk"></cogTrainResult-modal>
    <!--  报告显示  -->
    <train-report-common-view ref="TrainReportCommonView"></train-report-common-view>
  </a-card>
</template>

<script>
import CogTrainResultModal from './modules/CogTrainResultModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@api/manage'
import TrainReportCommonView from '@views/train/modules/TrainReportCommonView'

export default {
  name: 'CogTrainResultList',
  mixins: [JeecgListMixin],
  components: {
    CogTrainResultModal,
    TrainReportCommonView
  },
  data() {
    return {
      description: '训练结果管理页面',
      // 表头
      columns: [
        {
          title: '测试者名称',
          align: 'center',
          dataIndex: 'userName'
        },
        {
          title: '训练分类',
          align: 'center',
          dataIndex: 'trainType',
          customRender: function(text) {
            return text === 'vr' ? 'VR游戏' : '平面游戏'
          }
        },
        {
          title: '训练名称',
          align: 'center',
          dataIndex: 'trainName'
        },
        {
          title: '等级',
          align: 'center',
          dataIndex: 'grade'
        },
        {
          title: '训练状态',
          align: 'center',
          dataIndex: 'status_dictText'
        },
        {
          title: '测试时间',
          align: 'center',
          dataIndex: 'time'
        },
        {
          title: '总分',
          align: 'center',
          dataIndex: 'totalPoints'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/train/cogTrainResult/list',
        delete: '/train/cogTrainResult/delete',
        deleteBatch: '/train/cogTrainResult/deleteBatch',
        exportXlsUrl: 'train/cogTrainResult/exportXls',
        importExcelUrl: 'train/cogTrainResult/importExcel',
        continueTrain: '/psychology/psUser/continueTrain'
      }
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    continueTrain(record){
      let that = this
      let httpurl = that.url.continueTrain
      let formData = {}
      formData.id = record.id
      formData.trainId = record.trainId;
      getAction(httpurl, formData).then((res) => {
        if (res.success) {
          this.$notification['success']({
            message: '添加成功',
            duration: 3,
            description: '已在选择的终端推送答题信息'
          })
        } else {
          that.$message.warning(res.message)
        }
      }).finally(() => {
      })
    },
    handlePrintForm(record) {
      if (record.status != '1') {
        this.$message.error('未完成训练不能预览！')
        return
      }
      this.$refs.TrainReportCommonView.edit(record)
      this.$refs.TrainReportCommonView.title = '内容预览'
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
<template>
  <a-modal
    :title="title"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel">
    <template #footer>
      <a-button key="back" @click="handleCancel">关闭</a-button>
      <a-button key="submit" type="primary" @click="handleOk()">复制</a-button>
    </template>
    <a-card :bordered="false">
      <!-- 查询区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline" :form="form">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="测试者编号" required>
                <a-input placeholder="请输入测试者编号" v-decorator="['userNo', validatorRules.userNo ]"></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="选择量表" required>
                <a-select placeholder="请选择量表" v-decorator="['measureId', validatorRules.measureId ]">
                  <a-select-option v-for="item, index of measureList" :value="item.value" :key="index">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                <a-button type="primary" @click="exportExcelFactor" icon="search" style="margin-left: 8px">导出</a-button>
                <a-button type="primary" @click="exportExcelFactorAll" icon="search" style="margin-left: 8px">全部导出</a-button>
                <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="user-number-content">
        <span v-for="item, index of userNums" :key="index" style="margin-right: 10px;">{{ item }}</span>
      </div>
    </a-card>
  </a-modal>
</template>

<script>
import { filterObj } from '@/utils/util'
import { getAction, downFile } from '@/api/manage'

export default {
  name: 'DxCompletionUserModel',
  data() {
    return {
      title: '未做测评用户编号',
      visible: false,
      queryParam: {},
      confirmLoading: false,
      userNums: [],
      measureList: [],
      userNumsStr: '',
      form: this.$form.createForm(this),
      validatorRules: {
        userNo: { rules: [{ required: true, message: '请输入编号!' }] },
        measureId: { rules: [{ required: true, message: '请选择量表!' }] },
      },
      url: {
        completionUser: '/diagnosis/dxResult/queryCompletionUser',
        exportCompletionUser: '/diagnosis/dxResult/exportCompletionUser',
        exportCompletionUserAll: '/diagnosis/dxResult/exportCompletionUserAll',
      }
    }
  },
  created() {
  },
  methods: {
    add(record) {
      this.visible = true
    },
    completion(record) {
      if (record) {
        this.measureList = []
        this.searchReset()
        let measureIds = record.measureIds.split(',')
        let measureNames = record.measureNames.split(',')
        measureIds.forEach((item, index) => {
          this.measureList.push({
            value: item,
            name: measureNames[index]
          })
        });
      }
      this.visible = true
    },
    searchQuery() {
      this.loadCompletionUser();
    },
    loadCompletionUser() {
      this.form.validateFields(async (err, values) => {
        if (!err) {
          let queryParam = Object.assign(this.queryParam, values)
          var params = filterObj(queryParam);//查询条件
          delete params.createTimeRange; // 时间参数不传递后台
          this.confirmLoading = true;
          getAction(this.url.completionUser, params).then((res) => {
            if (res.success) {
              this.userNums = res.result.incompletePsUserIds
            }
            if(res.code===510){
              this.$message.warning(res.message)
            }
            this.confirmLoading = false;
          })
        }
      })
    },
    exportQuery() {
      this.form.validateFields(async (err, values) => {
        if (!err) {
          let queryParam = Object.assign(this.queryParam, values)
          var params = filterObj(queryParam);//查询条件
          delete params.createTimeRange; // 时间参数不传递后台
          let url = `${window._CONFIG['domianURL']}/${this.url.exportCompletionUser}?userNo=` + params.userNo + '&measureId=' + params.measureId
          window.location.href = url
        }
      })
    },

    exportExcelFactor() {
      this.form.validateFields(async (err, values) => {
        if (!err) {
          let queryParam = Object.assign(this.queryParam, values)
          var params = filterObj(queryParam);//查询条件
          delete params.createTimeRange; // 时间参数不传递后台
          this.$message.success("正在导出，请稍后")
          downFile(this.url.exportCompletionUser, params, 'get').then((data)=>{
            if (!data) {
              this.$message.warning("文件下载失败")
              return
            }
            if (typeof window.navigator.msSaveBlob !== 'undefined') {
              window.navigator.msSaveBlob(new Blob([data]), '未答题用户'+'.xls')
            }else{
              let url = window.URL.createObjectURL(new Blob([data]))
              let link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              link.setAttribute('download', '未答题用户'+'.xls')
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link); //下载完成移除元素
              window.URL.revokeObjectURL(url); //释放掉blob对象
            }
          }).finally(() => {
            this.factorCanClick = true
          })
        }
      })
    },

    exportExcelFactorAll() {
      this.form.validateFields(async (err, values) => {
        if (!err) {
          let queryParam = Object.assign(this.queryParam, values)
          var params = filterObj(queryParam);//查询条件
          params.userNos = "YXXX,GXQXX,WCXX"
          delete params.createTimeRange; // 时间参数不传递后台
          this.$message.success("正在导出，请稍后")
          downFile(this.url.exportCompletionUserAll, params, 'get').then((data)=>{
            if (!data) {
              this.$message.warning("文件下载失败")
              return
            }
            if (typeof window.navigator.msSaveBlob !== 'undefined') {
              window.navigator.msSaveBlob(new Blob([data]), '未答题用户'+'.xls')
            }else{
              let url = window.URL.createObjectURL(new Blob([data]))
              let link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              link.setAttribute('download', '未答题用户'+'.xls')
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link); //下载完成移除元素
              window.URL.revokeObjectURL(url); //释放掉blob对象
            }
          }).finally(() => {
            this.factorCanClick = true
          })
        }
      })
    },
    searchReset() {
      this.queryParam = {}
      this.form.resetFields()
      this.userNums = []
    },
    close() {
      sessionStorage.removeItem('groupInfo')
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
    async handleOk() {
      try {
        let str = this.userNums.join("\n");
        await navigator.clipboard.writeText(str);
      } catch (err) {
        console.error('Failed to copy: ', err);
      }
    }
  }
}
</script>

<style lang="less" scoped>
  .user-number-content {
    height: 500px;
    overflow-y: scroll;
  }
</style>
<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="设置类型">
          <j-dict-select-tag v-decorator="['type', {}]" :triggerChange="true" placeholder="请选择类型" dictCode="cog_push_type" />
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="推送分数范围">
          <a-input-number v-decorator="[ 'pushRangeBefor', {}]" />
          -
          <a-input-number v-decorator="[ 'pushRangeAfter', {}]" />
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="训练分类">
          <a-select
            placeholder="请选训练评分类"
            v-decorator="['trainType']"
            @change="handleChange">
            <a-select-option value="plane">
              平面游戏
            </a-select-option>
            <a-select-option value="vr">
              VR游戏
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="认知训练">
          <a-tree-select
            showSearch
            multiple
            style="width:100%"
            :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
            :treeData="trainTree"
            v-model="selectedCognize"
            treeNodeFilterProp="title"
            placeholder="请选择认知训练">
          </a-tree-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'
import pick from 'lodash.pick'

export default {
  name: 'CogTrainPushSetModal',
  data() {
    return {
      title: '操作',
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      selectedCognize: [],
      trainTree: [],
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {},
      url: {
        add: '/train/cogTrainPushSet/add',
        edit: '/train/cogTrainPushSet/edit',
        treeList: '/train/cogTrainPackage/queryTreeSelectList'
      }
    }
  },
  created() {
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'delFlag', 'pushRangeAfter', 'pushRangeBefor', 'sysOrgCode', 'trainIds', 'trainNames', 'type'))
        //时间格式化
      })
      this.loadTreeData()
    },
    close() {
      this.$emit('close')
      this.visible = false
      this.selectedCognize = []
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          let selectedCognize = this.selectedCognize.map(select => select.split(',')[0])
          formData.trainIds = selectedCognize.length > 0 ? selectedCognize.join(',') : ''
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
            that.close()
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    handleChange(value) {
      this.loadTreeData(value)
    },
    loadTreeData(type = 'plane') {
      this.trainTree = []
      getAction(this.url.treeList, {trainType: type}).then((res) => {
        if (res.success) {
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            this.trainTree.push(temp)
          }
          this.loading = false
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>

</style>
<template>
  <a-modal
    :title='title'
    :width='800'
    :visible='visible'
    :confirmLoading='confirmLoading'
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <a-form :form='form'>
        <a-form-item
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
          v-has="'authList:actionParam:qrcode'"
          label='医生'>
          <a-input placeholder='请输入医生姓名' disabled :defaultValue='nickname()' v-model='model.doctorName' />
        </a-form-item>
        <a-form-item
          :labelCol='labelCol'
          :wrapperCol='wrapperCol'
          label='名称'>
          <a-input placeholder='请输入训练套餐名称'
                   v-decorator="['name',{rules: [{ required: true, message: '请输入套餐名称' }]}]" />
        </a-form-item>
        <a-form-item v-show='!isAdd' label='训练' :labelCol='labelCol' :wrapperCol='wrapperCol' prop='trainIds'>
          <a-tree-select
            showSearch
            multiple
            style='width:100%'
            :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
            :treeData='trainTree'
            v-model='selectedTrain'
            treeNodeFilterProp='title'
            placeholder='请选择训练'>
          </a-tree-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'
import { mapGetters } from 'vuex'
import pick from 'lodash.pick'

export default {
  name: 'TrainPackageRecordsModal',
  data() {
    return {
      title: '操作',
      visible: false,
      selectedTrain: [],
      trainTree: [],
      model: {
        status: '0',
        noGenerRule: '0'
      },
      isAdd: true,
      doctorId: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      resultStatus: false,
      repetitionStatus: false,
      isTop: false,
      signUserStatus: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {},
      url: {
        add: '/train/cogTrainPackageTemplate/add',
        edit: '/train/cogTrainPackageTemplate/edit',
        treeList: '/psychology/psCategory/queryCognizeTreeList'
      }
    }
  },
  created() {
  },
  methods: {
    ...mapGetters(['nickname', 'userInfo']),
    add() {
      this.isAdd = false
      this.doctorId = this.userInfo().id
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.resultStatus = this.model.resultStatus == 1
      this.repetitionStatus = this.model.repetitionStatus == 1
      this.isTop = this.model.isTop == 1
      this.signUserStatus = this.model.signUserStatus == 1
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'name'))
      })
      this.visible = true
      this.loadTreeData()
    },
    close() {
      this.$emit('close')
      this.isAdd = true
      this.model = {}
      this.selectedTrain = []
      this.trainTree = []
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          let selectedTrain = this.selectedTrain.map(select => select.split(',')[0])
          if (selectedTrain.length == 0 && !this.isAdd) {
            this.$message.error('请选择套餐训练')
            that.confirmLoading = false
            return
          }
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            formData.doctorId = this.doctorId
            formData.trainIds = selectedTrain.length > 0 ? selectedTrain.join(',') : ''
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          formData.isTop = 1
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
            that.close()
          })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    loadTreeData() {
      getAction(this.url.treeList, {}).then((res) => {
        if (res.success) {
          for (let i = 0; i < res.result.length; i++) {
            let temp = res.result[i]
            temp.value = temp.key
            for(let j = 0; j < temp.children.length; j++) {
              let train = temp.children[j]
              train.value = train.key + ',' + temp.key
            }
            this.trainTree.push(temp)
          }
          this.loading = false
        }
      })
    }
  }
}
</script>

<style lang='less' scoped>

</style>
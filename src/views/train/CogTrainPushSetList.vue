<template>
  <a-card :bordered="false">
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete" />
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a
        style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">
        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->
    <!-- 表单区域 -->
    <cogTrainPushSet-modal ref="modalForm" @ok="modalFormOk"></cogTrainPushSet-modal>
  </a-card>
</template>

<script>
import CogTrainPushSetModal from './modules/CogTrainPushSetModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'CogTrainPushSetList',
  mixins: [JeecgListMixin],
  components: {
    CogTrainPushSetModal
  },
  data() {
    return {
      description: '训练推送设置管理页面',
      // 表头
      columns: [
        {
          title: '设置类型',
          align: 'center',
          dataIndex: 'type_dictText'
        },
        {
          title: '推送分数范围',
          align: 'center',
          dataIndex: 'pushRangeAfter',
          customRender: (value, row) => {
            if (row.pushRangeBefor == null || row.pushRangeAfter == null) {
              return '无'
            } else {
              const ageRange = row.pushRangeBefor + '-' + row.pushRangeAfter
              return ageRange
            }
          }
        },
        {
          title: '训练分类',
          align: 'center',
          dataIndex: 'trainType',
          customRender: function(text) {
            return text == 'vr' ? 'VR游戏' : '平面游戏'
          }
        },
        {
          title: '认知训练',
          align: 'center',
          dataIndex: 'trainNames'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/train/cogTrainPushSet/list',
        delete: '/train/cogTrainPushSet/delete',
        deleteBatch: '/train/cogTrainPushSet/deleteBatch',
        exportXlsUrl: 'train/cogTrainPushSet/exportXls',
        importExcelUrl: 'train/cogTrainPushSet/importExcel'
      }
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {}
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
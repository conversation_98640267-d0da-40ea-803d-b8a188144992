<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :span="6">
            <a-form-item label="患者名称">
              <a-input placeholder="请输入患者名称" v-model="queryParam.patientName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="作业名称">
              <a-input placeholder="请输入作业名称" v-model="queryParam.fileName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete"/>
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down"/>
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i>
        <span>已选择</span>
        <a style="font-weight: 600">
          {{ selectedRowKeys.length }}
        </a>
        <span>项</span>
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">详情</a>
          <a-divider type="vertical"/>
          <a @click="handleExport(record)">下载</a>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <work-treat-modal ref="modalForm" @ok="modalFormOk"/>

  </a-card>
</template>

<script>

  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import WorkTreatModal from './modules/WorkTreatModal'

  export default {
    name: 'PsUserMeasurePriceList',
    mixins: [JeecgListMixin],
    components: {
      WorkTreatModal,
    },
    data() {
      return {
        description: '量表单价设置主表管理页面',
        // 表头
        columns: [
          {
            title: '患者名称',
            align: "center",
            dataIndex: 'patientName',
          },
          {
            title: '作业名称',
            align: "center",
            dataIndex: 'fileName',
          },
          {
            title: '完成时间',
            align: "center",
            dataIndex: 'completeTime',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: { customRender: 'action' }
          }
        ],
        // 请求参数
        url: {
          list: '/ps/homeworkFile/list',
          delete: '/ps/homeworkFile/delete',
          deleteBatch: '/ps/homeworkFile/deleteBatch',
        },
        downLoading: false
      }
    },
    methods: {
      initDictConfig() {
      },

      handleExport(record) {
        const link = document.createElement('a');
        link.href = record.fileUrl;
        link.download = record.fileUrl.split('/').pop(); // 获取文件名
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        this.$message.success('下载成功');
      },
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>
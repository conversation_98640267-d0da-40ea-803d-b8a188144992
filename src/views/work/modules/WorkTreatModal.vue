<template>
  <a-modal
    :title="title"
    :width="1000"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    :okButtonProps="{ props: {disabled: disableSubmit} }"
    @ok="handleOk"
    @cancel="handleCancel">
    <!-- 主表单区域 -->
    <a-form :form="form">
      <a-row>
        <a-col :span="24" :gutter="8">
          <a-form-item label="患者名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <j-select-patient v-decorator="['patientName', {rules: [{required: true, message: '请选择患者', }]}]" @back="backPatientInfo" :backInfo="true" :isRadio="true" :disabled="disableSubmit"></j-select-patient>
          </a-form-item>
          <a-form-item
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            label="作业治疗">
            <a-select v-decorator="['homeworkId', {rules: [{required: true, message: '请选择作业治疗'}]}]" placeholder="请选择作业治疗" :disabled="disableSubmit">
              <a-select-option v-for="(item, index) in workList" :key="item.id" :value="item.id"> {{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            label="完成时间">
            <a-date-picker showTime format='YYYY-MM-DD HH:mm:ss' v-decorator="[ 'completeTime', {rules: [{required: true, message: '请选择完成时间'}]}]" style="width: 100%" :disabled="disableSubmit" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>
<script>

  import pick from 'lodash.pick'
  import { getAction, httpAction } from '@/api/manage'
  import JSelectPatient from '@/components/jeecgbiz/JSelectPatient'
  import { formatDate } from '@/utils/util'

  export default {
    name: 'VideoTemplateModal',

    components: {
      JSelectPatient
    },

    data() {
      return {
        title: '量表单价设置',
        visible: false,
        validatorRules: {},
        confirmLoading: false,
        disableSubmit: false,
        workList: [],
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        model: {
        },
        form: this.$form.createForm(this),
        disabledEdit: true,
        url: {
          add: '/ps/homeworkFile/add',
          findSelectList: '/ps/homeworkFile/findSelectList',
        },
        workList: [],
      }
    },
    created() {
      this.getWorkList()
    },
    methods: {
      add() {
        this.disabledEdit = false
        this.edit({})
      },
      getWorkList() {
        getAction(this.url.findSelectList, null).then((res) => {
          if (res.success) {
            this.workList = res.result
          }
        })
      },
      edit(record) {
        this.form.resetFields()
        this.visible = true
        this.model = Object.assign({}, record)
        this.model.homeworkId = record.fileName
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'patientName', 'homeworkId', 'completeTime'))
        })
     },
      close() {
        this.$emit('close')
        this.visible = false
        this.form.resetFields()
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let formData = Object.assign(this.model, values)
            formData.completeTime = formatDate(formData.completeTime, 'yyyy-MM-dd hh:mm:ss')
            httpAction(this.url.add, formData, 'post').then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      },

      backPatientInfo(info) {
        this.$nextTick(() => {
          this.model.patientId = info[0].value
          this.form.setFieldsValue({ patientName: info[0].text })
        })
      },
    },
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
</style>
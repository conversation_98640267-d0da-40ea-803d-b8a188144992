<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="团体报告名称">
              <a-input placeholder="请输入团体报告名称" v-model="queryParam.name"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="sync">生成团体报告</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete" />
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a
        style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">
        <template slot="ageRangeBefor" slot-scope="text, record">
          {{ record.ageRangeBefor }} - {{ record.ageRangeAfter }}
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="handlePrintForm(record)">团体报告预览</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->
    <!-- 表单区域 -->
    <dxGroupReportRecord-modal ref="modalForm" @ok="modalFormOk"></dxGroupReportRecord-modal>
    <!-- 打印表单页 -->
    <group-report-form ref="GroupReportForm"></group-report-form>
  </a-card>
</template>

<script>
import DxGroupReportRecordModal from './modules/DxGroupReportRecordModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import GroupReportForm from './modules/GroupReportForm'

export default {
  name: 'DxGroupReportRecordList',
  mixins: [JeecgListMixin],
  components: {
    DxGroupReportRecordModal,
    GroupReportForm
  },
  data() {
    return {
      description: '团体报告记录管理页面',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '团体报告名称',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '编码前缀',
          align: 'center',
          dataIndex: 'prefixEncode'
        },
        {
          title: '测试者编号',
          align: 'center',
          dataIndex: 'userNumber'
        },
        {
          title: '开始时间',
          align: 'center',
          dataIndex: 'startTime'
        },
        {
          title: '结束时间',
          align: 'center',
          dataIndex: 'endTime'
        },
        {
          title: '年龄范围',
          align: 'center',
          dataIndex: 'ageRangeBefor',
          scopedSlots: { customRender: 'ageRangeBefor' }
        },
        {
          title: '性别',
          align: 'center',
          dataIndex: 'sex'
        },
        {
          title: '部门名称',
          align: 'center',
          dataIndex: 'departmentName'
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/group/dxGroupReportRecord/list',
        delete: '/group/dxGroupReportRecord/delete',
        deleteBatch: '/group/dxGroupReportRecord/deleteBatch',
        exportXlsUrl: 'group/dxGroupReportRecord/exportXls',
        importExcelUrl: 'group/dxGroupReportRecord/importExcel'
      }
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    handlePrintForm(record) {
      this.$refs.GroupReportForm.edit(record)
      this.$refs.GroupReportForm.title = '内容预览'
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
<template>
  <div>
    <a-form style="margin: 40px auto 0;">
      <result title="操作成功" :is-success="true" description="已生成团体报告，请返回列表查看">
        <div class="information">
          <a-row>
            <a-col :sm="7" :xs="24"></a-col>
            <a-col :sm="8" :xs="24">团体报告名称：</a-col>
            <a-col :sm="9" :xs="24">{{this.model.name}}</a-col>
          </a-row>
        </div>
        <div slot="action">
          <a-button type="primary" @click="toOrderList">查看报告</a-button>
        </div>
      </result>
    </a-form>
  </div>
</template>

<script>
import Result from '../../result/Result'

export default {
  name: 'Step3',
  components: {
    Result
  },
  data() {
    return {
      loading: false,
      model: {}
    }
  },
  created() {
    this.model = JSON.parse(sessionStorage.getItem('groupInfo'))
  },
  methods: {
    finish() {
      this.$emit('finish')
    },
    toOrderList() {
      this.$emit('finish')
    }
  }
}
</script>
<style lang="scss" scoped>
.information {
  line-height: 22px;

  .ant-row:not(:last-child) {
    margin-bottom: 24px;
  }
}

.money {
  font-family: "Helvetica Neue", sans-serif;
  font-weight: 500;
  font-size: 20px;
  line-height: 14px;
}
</style>
<template>
  <div>
    <a-alert
      :closable="true"
      message="确认数据后，将直接生成团体报告，无法修改。"
      style="max-width: 500px; margin: 40px auto 0;margin-bottom: 24px;"
    />
    <a-card :bordered="false">
      <!-- table区域-begin -->
      <div>
        <a-table
          ref="table"
          size="middle"
          bordered
          rowKey="id"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="ipagination"
          :loading="loading"
          @change="handleTableChange">

          <span slot="degree" slot-scope="text">
            <a-badge :color="text | statusTypeFilter" :text="text | statusFilter" />
          </span>
        </a-table>
      </div>
      <!-- table区域-end -->
      <a-row>
        <a-col :span="10"></a-col>
        <a-col :span="14">
          <a-button :loading="loading" type="primary" @click="nextStep">确认并提交</a-button>
          <a-button style="margin-left: 8px" @click="prevStep">上一步</a-button>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>
<script>

import { getAction, httpAction } from '@api/manage'
import { filterObj } from '@/utils/util'

const degreeMap = {
  0: {
    color: '#D9D9D9',
    text: '未答完'
  },
  1: {
    color: 'green',
    text: '正常'
  },
  2: {
    color: 'yellow',
    text: '轻度'
  },
  3: {
    color: 'red',
    text: '中度'
  },
  4: {
    color: 'purple',
    text: '重度'
  },
  5: {
    color: '#D9D9D9',
    text: '未知'
  }
}

export default {
  name: 'Step2',
  data() {
    return {
      downLoading: false,
      loading: false,
      /* 数据源 */
      dataSource: [],
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      // 表头
      columns: [
        {
          title: '编号',
          align: 'center',
          dataIndex: 'userNo',
          sorter: true
        },
        {
          title: '主试',
          align: 'center',
          dataIndex: 'userName'
        },
        {
          title: '操作人',
          align: 'center',
          dataIndex: 'doctorName'
        },
        {
          title: '量表',
          align: 'center',
          dataIndex: 'measureName'
        },
        {
          title: '答题状态',
          align: 'center',
          dataIndex: 'status_dictText'
        },
        {
          title: '预警结果',
          align: 'center',
          dataIndex: 'degree',
          scopedSlots: { customRender: 'degree' }
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime'
        }
      ],
      /* 排序参数 */
      isorter: {
        column: 'createTime',
        order: 'desc'
      },
      // 请求参数
      url: {
        list: '/diagnosis/dxResult/list',
        addGroup:'/group/dxGroupReportRecord/add'
      }
    }
  },
  filters: {
    statusFilter(type) {
      return degreeMap[type].text
    },
    statusTypeFilter(type) {
      return degreeMap[type].color
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    nextStep() {
      if (this.dataSource.length == 0) {
        this.$message.error('无结果数据!')
        return
      }
      let that = this
      that.loading = true
      let groupInfo = JSON.parse(sessionStorage.getItem('groupInfo'));
      httpAction(this.url.addGroup, groupInfo, 'post').then((res) => {
        if (res.success) {
          that.$emit('nextStep')
        } else {
          that.$message.warning(res.message)
        }
      }).finally(() => {
        that.loading = false
      })
    },
    prevStep() {
      this.$emit('prevStep')
    },
    getQueryParams() {
      var param = Object.assign({}, JSON.parse(sessionStorage.getItem('groupInfo')), this.isorter)
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      return filterObj(param)
    },
    loadData() {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      var params = this.getQueryParams()
      //查找已完成的数据
      params.status = '2'
      params.userNoLeft = params.userNumber;
      delete params.userNumber;
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records
          this.ipagination.total = res.result.total
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      this.loadData()
    }
  }
}
</script>

<style lang="scss" scoped>
.stepFormText {
  margin-bottom: 24px;

  .ant-form-item-label,
  .ant-form-item-control {
    line-height: 22px;
  }
}

</style>
<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="训练名称">
          <a-input placeholder="请输入训练名称" v-decorator="['name', {rules: [{ required: true, message: '请输入训练名称!' }]}]" />
        </a-form-item>
        <a-form-item label="训练类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select
            mode="multiple"
            style="width: 100%"
            placeholder="请选择训练类别"
            v-model="selectedCategory">
            <a-select-option v-for="category in psCategorys" :key="category.id">
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="训练分类" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select
            style="width: 100%"
            placeholder="请选择训练分类"
            v-decorator="['trainType', {rules: [{ required: true, message: '请选择训练分类!' }]}]" @change="handleChange">
            <a-select-option value="plane">
              平面游戏
            </a-select-option>
            <a-select-option value="vr">
              VR游戏
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="训练编码" v-if="trainType == 'vr'">
          <a-input placeholder="请输入训练编码" v-decorator="['code', {rules: [{ required: true, message: '请输入训练编码!' }]}]" />
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="跳转地址">
          <a-input placeholder="请输入跳转地址" v-decorator="['htmlPath', {}]" />
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="排序">
          <a-input placeholder="请输入排序" v-decorator="['sort', {}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { httpAction } from '@/api/manage'
import pick from 'lodash.pick'
import { findPsCategoryAll } from '@/api/api'
import { mode } from 'crypto-js';

export default {
  name: 'PsCognizeModal',
  data() {
    return {
      title: '操作',
      visible: false,
      model: {},
      psCategorys: [],
      selectedCategory: [],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {},
      url: {
        add: '/psychology/psCognize/add',
        edit: '/psychology/psCognize/edit'
      },
      trainType: "",
    }
  },
  created() {
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.selectedCategory = []
      this.model = Object.assign({}, record)
      this.loadPsTemplate()
      this.visible = true
      if (this.model.categoryId) {
        this.selectedCategory = this.model.categoryId.split(',')
      }
      if (this.model.trainType) {
        this.trainType = this.model.trainType
      }
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'name', 'categoryId', 'htmlPath', 'sort', 'code', 'trainType'))
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          formData.categoryId = this.selectedCategory.length > 0 ? this.selectedCategory.join(',') : ''
          formData.type = 2
          //时间格式化
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
            that.close()
          })
        }
      })
    },
    handleChange(e) {
      this.trainType = e
    },
    /**
     * 初始化训练类别下拉选
     */
    loadPsTemplate() {
      findPsCategoryAll({'type':'1'}).then((res) => {
        if (res.success) {
          this.psCategorys = res.result
        }
      })
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>
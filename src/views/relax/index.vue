<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="训练人">
              <a-input placeholder="请输入训练人" v-model="queryParam.patientName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="训练名称">
              <a-input placeholder="请输入训练名称" v-model="queryParam.scene"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        @change="handleTableChange">
      </a-table>
    </div>
    <!-- table区域-end -->
  </a-card>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@api/manage'

export default {
  name: 'CogTrainResultList',
  mixins: [JeecgListMixin],
  data() {
    return {
      description: '放松训练',
      // 表头
      columns: [
        {
          title: '训练人',
          align: 'center',
          dataIndex: 'patientName'
        },
        {
          title: '训练时间',
          align: 'center',
          dataIndex: 'trainTime'
        },
        {
          title: '训练名称',
          align: 'center',
          dataIndex: 'scene'
        },
        {
          title: '训练时长',
          align: 'center',
          dataIndex: 'duration',
          customRender: function(text) {
            return text + '秒'
          }
        }
      ],
      url: {
        list: '/vr/relaxation/list',
      }
    }
  },
  methods: {
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
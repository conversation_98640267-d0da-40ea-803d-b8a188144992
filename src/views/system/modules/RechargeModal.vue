<template>
  <a-modal
    title="充值"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    cancelText="关闭"
    style="top:20px;"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">

        <a-form-item label="充值金额" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input prefix="￥" suffix="RMB" placeholder="请输入充值金额" v-decorator="[ 'rechargeAmount', {}]"/>
        </a-form-item>

      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>

  import { httpAction } from '@/api/manage'

  export default {
    name: 'RechargeModal',
    data() {
      return {
        visible: false,
        confirmLoading: false,
        confirmDirty: false,
        userId: '',
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        form: this.$form.createForm(this),
        url: {
          rechargeAmount:"/sys/user/rechargeAmount",
        },
      }
    },
    created() {
    },
    methods: {
      show(id) {
        this.form.resetFields()
        this.visible = true
        this.userId = id
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'rechargeAmount'))
        });
      },
      close() {
        this.$emit('close')
        this.visible = false
        this.disableSubmit = false
      },
      handleSubmit() {
        // 触发表单验证
        this.form.validateFields((err, values) => {
          let that = this;
          if (!err) {
            this.confirmLoading = true
            let formData = Object.assign(this.model, values)
            formData.id = that.userId;
            httpAction(that.url.rechargeAmount, formData, 'post').then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
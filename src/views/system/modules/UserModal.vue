<template>
  <a-drawer
    :title="title"
    :maskClosable="true"
    :width="drawerWidth"
    placement="right"
    :closable="true"
    @close="handleCancel"
    :visible="visible">

    <template slot="title">
      <div style="width: 100%;">
        <span>{{ title }}</span>
        <span style="display:inline-block;width:calc(100% - 51px);padding-right:10px;text-align: right">
          <a-button @click="toggleScreen" icon="appstore" style="height:20px;width:20px;border:0px"></a-button>
        </span>
      </div>

    </template>

    <a-spin :spinning="confirmLoading">
      <a-form-model ref="ruleForm" :model="formModel" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">

        <a-form-model-item label="用户账号" prop="username">
          <a-input placeholder="请输入用户账号" v-model="formModel.username" :readOnly="!!model.id"/>
        </a-form-model-item>

        <template v-if="!model.id">
          <a-form-model-item label="登陆密码" prop="password">
            <a-input type="password" placeholder="请输入登陆密码" v-model="formModel.password" />
          </a-form-model-item>

          <a-form-model-item label="确认密码" prop="confirmpassword">
            <a-input type="password" @blur="handleConfirmBlur" placeholder="请重新输入登陆密码"
                      v-model="formModel.confirmpassword"/>
          </a-form-model-item>
        </template>

        <a-form-model-item label="用户名字" prop="realname">
          <a-input placeholder="请输入用户名称" v-model="formModel.realname" />
        </a-form-model-item>

        <a-form-model-item label="角色分配" v-show="!roleDisabled" prop="selectedRole">
          <a-select
            mode="multiple"
            style="width: 100%"
            placeholder="请选择用户角色"
            v-model="formModel.selectedRole">
            <a-select-option v-for="(role,roleindex) in roleList" :key="roleindex.toString()" :value="role.id">
              {{ role.roleName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>

        <!--部门分配-->
        <a-form-model-item class="dept-item" label="部门分配" v-show="!departDisabled" prop="checkedDepartNameString">
          <a-input-search
            placeholder="点击右侧按钮选择部门"
            v-model="formModel.checkedDepartNameString"
            readOnly
            @search="onSearch">
            <a-button slot="enterButton" icon="search">选择</a-button>
          </a-input-search>
        </a-form-model-item>
        <a-form-model-item label="身份" prop="identity">
          <a-radio-group
            v-model="formModel.identity"
            @change="identityChange">
            <a-radio value="1">普通用户</a-radio>
            <a-radio value="2">上级</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="负责部门" v-if="departIdShow==true" prop="departIds">
          <a-select
            mode="multiple"
            style="width: 100%"
            placeholder="请选择负责部门"
            v-model="formModel.departIds"
            optionFilterProp="children"
            :getPopupContainer="(target) => target.parentNode"
            :dropdownStyle="{maxHeight:'200px',overflow:'auto'}"
          >
            <a-select-option v-for="item in resultDepartOptions" :key="item.key" :value="item.key"
            >{{item.title}}
            </a-select-option
            >
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="头像" prop="fileList">
          <j-image-upload class="avatar-uploader" bizPath="temp/image/userAvatar" text="上传" v-model="formModel.fileList" ></j-image-upload>
        </a-form-model-item>
        <a-form-model-item label="报告头部" prop="contentHead">
          <j-editor :j-height="360" v-model="model.contentHead"/>
        </a-form-model-item>
        <a-form-model-item label="报告尾部" prop="contentTail">
          <j-editor :j-height="200" v-model="model.contentTail"/>
        </a-form-model-item>
        <a-form-model-item label="生日" prop="birthday">
          <a-date-picker
            style="width: 100%"
            placeholder="请选择生日"
            
            v-model="formModel.birthday"/>
        </a-form-model-item>

        <a-form-model-item label="性别" prop="sex">
          <a-select v-model="formModel.sex" placeholder="请选择性别">
            <a-select-option :value="1">男</a-select-option>
            <a-select-option :value="2">女</a-select-option>
          </a-select>
        </a-form-model-item>

        <a-form-model-item label="邮箱" prop="email">
          <a-input placeholder="请输入邮箱" v-model="formModel.email" />
        </a-form-model-item>

        <a-form-model-item label="抬头" prop="orgName">
          <a-input placeholder="请输入抬头" v-model="formModel.orgName" />
        </a-form-model-item>

        <a-form-model-item label="手机号码" prop="phone">
          <a-input placeholder="请输入手机号码" :disabled="isDisabledAuth('user:form:phone')"
                   v-model="formModel.phone"/>
        </a-form-model-item>

        <a-form-model-item label="工作流引擎" prop="activitiSync">
          <j-dict-select-tag v-model="formModel.activitiSync" placeholder="请选择是否同步工作流引擎" :type="'radio'"
                             :triggerChange="true" dictCode="activiti_sync"/>
        </a-form-model-item>

      </a-form-model>
    </a-spin>
    <depart-window ref="departWindow" @ok="modalFormOk"></depart-window>

    <div class="drawer-bootom-button" v-show="!disableSubmit">
      <a-popconfirm title="确定放弃编辑？" @confirm="handleCancel" okText="确定" cancelText="取消">
        <a-button style="margin-right: .8rem">取消</a-button>
      </a-popconfirm>
      <a-button @click="handleSubmit" type="primary" :loading="confirmLoading">提交</a-button>
    </div>
  </a-drawer>
</template>

<script>
  import pick from 'lodash.pick'
  import moment from 'moment'
  import Vue from 'vue'
  // 引入搜索部门弹出框的组件
  import departWindow from './DepartWindow'
  import { ACCESS_TOKEN } from '@/store/mutation-types'
  import { getAction } from '@/api/manage'
  import { addUser, duplicateCheck, editUser, queryall, queryUserRole } from '@/api/api'
  import { disabledAuthFilter } from '@/utils/authFilter'
  import JImageUpload from '../../../components/jeecg/JImageUpload'
  import JEditor from '@/components/jeecg/JEditor'

  export default {
    name: 'RoleModal',
    components: {
      JImageUpload,
      departWindow,
      JEditor
    },
    data() {
      return {
        formModel: {
          username: '',
          password: '',
          confirmpassword: '',
          realname: '',
          selectedRole: [],
          checkedDepartNameString: '',
          identity: '1',
          departIds: [],
          fileList:[],
          birthday: null,
          sex: null,
          email: '',
          orgName: '',
          phone: '',
          activitiSync: ''
        },
        rules: {
          username: [
            {
              required: true, message: '请输入用户账号!'
            }, {
              validator: this.validateUsername
            }
          ],
          password: [
            {
              required: true,
              pattern: /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/,
              message: '密码由8位数字、大小写字母和特殊符号组成!',
              trigger: 'blur'
            }, {
              validator: this.validateToNextPassword,
              trigger: 'blur'
            }
          ],
          confirmpassword: [
            {
              required: true, message: '请重新输入登陆密码!', trigger: 'blur'
            }, {
              validator: this.compareToFirstPassword, trigger: 'blur'
            }
          ],
          realname: [
            { required: true, message: '请输入用户名称!', trigger: 'blur' }
          ],
          selectedRole: [
            { required: true, message: '请选择用户角色!', trigger: 'change' }
          ],
          checkedDepartNameString: [
            { required: true, message: '请点击右侧按钮选择部门!', trigger: 'change' }
          ],
          orgName: [
            { required: true, message: '请输入用户抬头!', trigger: 'blur' }
          ],
          phone: [
            { validator: this.validatePhone, trigger: 'blur' }
          ],
          email: [
            {
              validator: this.validateEmail,
              trigger: 'blur'
            }
          ],
        },
        departDisabled: false, //是否是我的部门调用该页面
        roleDisabled: false, //是否是角色维护调用该页面
        modalWidth: 800,
        drawerWidth: 700,
        modaltoggleFlag: true,
        confirmDirty: false,
        selectedDepartKeys: [], //保存用户选择部门id
        checkedDepartKeys: [],
        checkedDepartNames: [], // 保存部门的名称 =>title
        userId: '', //保存用户id
        disableSubmit: false,
        userDepartModel: { userId: '', departIdList: [] }, // 保存SysUserDepart的用户部门中间表数据需要的对象
        dateFormat: 'YYYY-MM-DD',
        title: '操作',
        visible: false,
        model: {},
        roleList: [],
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        uploadLoading: false,
        confirmLoading: false,
        headers: {},
        form: this.$form.createForm(this),
        picUrl: '',
        url: {
          fileUpload: window._CONFIG['domianURL'] + '/sys/common/upload',
          imgerver: window._CONFIG['staticDomainURL'],
          userWithDepart: '/sys/user/userDepartList', // 引入为指定用户查看部门信息需要的url
          userId: '/sys/user/generateUserId', // 引入生成添加用户情况下的url
          syncUserByUserName: '/process/extActProcess/doSyncUserByUserName'//同步用户到工作流
        },
        departIdShow: false,
        resultDepartOptions: []
      }
    },
    created() {
      const token = Vue.ls.get(ACCESS_TOKEN)
      this.headers = { 'X-Access-Token': token }

    },
    computed: {
      uploadAction: function() {
        return this.url.fileUpload
      }
    },
    methods: {
      isDisabledAuth(code) {
        return disabledAuthFilter(code)
      },
      //窗口最大化切换
      toggleScreen() {
        if (this.modaltoggleFlag) {
          this.modalWidth = window.innerWidth
        } else {
          this.modalWidth = 800
        }
        this.modaltoggleFlag = !this.modaltoggleFlag
      },
      initialRoleList() {
        queryall().then((res) => {
          if (res.success) {
            this.roleList = res.result
          } else {
            // console.log(res.message)
          }
        })
      },
      loadUserRoles(userid) {
        queryUserRole({ userid: userid }).then((res) => {
          if (res.success) {
            this.formModel.selectedRole = res.result
          } else {
            // console.log(res.message)
          }
        })
      },
      refresh() {
        this.selectedDepartKeys = []
        this.checkedDepartKeys = []
        this.checkedDepartNames = []
        this.userId = ''
        this.resultDepartOptions = []
        this.departId = []
        this.departIdShow = false
        this.$refs.ruleForm.resetFields()
      },
      add() {
        this.picUrl = ''
        this.edit({ activitiSync: '1' })
      },
      edit(record) {
        this.resetScreenSize() // 调用此方法,根据屏幕宽度自适应调整抽屉的宽度
        let that = this
        that.initialRoleList()
        that.formModel.checkedDepartNameString = ''
        if(record.hasOwnProperty("id")){
          that.loadUserRoles(record.id);
          setTimeout(() => {
            that.formModel.fileList = record.avatar;
          }, 5)
        }
        that.userId = record.id
        that.visible = true
        that.model = Object.assign({}, record)
        that.formModel.username = that.model.username
        that.formModel.sex = that.model.sex ? that.model.sex : null
        that.formModel.realname = that.model.realname
        that.formModel.email = that.model.email
        that.formModel.phone = that.model.phone
        that.formModel.activitiSync = that.model.activitiSync
        that.formModel.orgName = that.model.orgName
        //身份为上级显示负责部门，否则不显示
        if(that.model.userIdentity=="2"){
          that.formModel.identity="2";
          that.departIdShow=true;
        }else{
          that.formModel.identity="1";
          that.departIdShow=false;
        }
        // 调用查询用户对应的部门信息的方法
        that.checkedDepartKeys = []
        that.loadCheckedDeparts()
      },
      //
      loadCheckedDeparts() {
        let that = this
        if (!that.userId) {
          return
        }
        getAction(that.url.userWithDepart, { userId: that.userId }).then((res) => {
          that.checkedDepartNames = []
          if (res.success) {
            for (let i = 0; i < res.result.length; i++) {
              that.checkedDepartNames.push(res.result[i].title)
              that.formModel.checkedDepartNameString = this.checkedDepartNames.join(',')
              that.checkedDepartKeys.push(res.result[i].key)
            }
            that.$refs.ruleForm.validateField('checkedDepartNameString')
            that.userDepartModel.departIdList = that.checkedDepartKeys
          } else {
            // console.log(res.message)
          }
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
        this.disableSubmit = false
        this.userDepartModel = { userId: '', departIdList: [] }
        this.checkedDepartNames = []
        this.checkedDepartKeys = []
        this.selectedDepartKeys = []
        this.departIdShow=false;
        this.$refs.ruleForm.resetFields()
      },
      moment,
      handleSubmit () {
        const that = this;
        // 触发表单验证
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            if (that.confirmLoading) return
            that.confirmLoading = true;
            if(!that.formModel.birthday){
              that.formModel.birthday = '';
            }else{
              that.formModel.birthday = that.formModel.birthday.format(this.dateFormat);
            }
            let formData = Object.assign(this.model, that.formModel);
            if(that.formModel.fileList != ''){
              formData.avatar = that.formModel.fileList;
            }else{
              formData.avatar = null;
            }
            formData.selectedroles = this.formModel.selectedRole.length>0?this.formModel.selectedRole.join(","):'';
            formData.selecteddeparts = this.userDepartModel.departIdList.length>0?this.userDepartModel.departIdList.join(","):'';
            formData.userIdentity=this.formModel.identity;
            //如果是上级择传入departIds,否则为空
            if(this.formModel.identity==="2"){
              formData.departIds=this.formModel.departIds.join(",");
            }else{
              formData.departIds="";
            }
            let obj;
            if(!this.model.id){
              formData.id = this.userId;
              obj=addUser
            }else{
              obj=editUser
            }
            obj(formData).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
              that.checkedDepartNames = [];
              that.userDepartModel.departIdList = {userId:'',departIdList:[]};
              that.close();
            })
          }
        })
      },
      handleCancel() {
        this.close()
      },
      validateToNextPassword(rule, value, callback) {
        const form = this.form
        const confirmpassword = this.formModel.confirmpassword

        if (value && confirmpassword && value !== confirmpassword) {
          callback(new Error('两次输入的密码不一样！'))
        }
        if (!value) {
          callback(new Error('请确认密码'))
        }
        if (value && this.confirmDirty) {
          this.$refs.ruleForm.validate(['confirm'], { force: true })
        }
        callback()
      },
      compareToFirstPassword(rule, value, callback) {
        if (value && value !== this.formModel.password) {
          callback(new Error('两次输入的密码不一样！'))
        } else {
          callback()
        }
      },
      validatePhone(rule, value, callback) {
        if (!value) {
          callback()
        } else {
          if (new RegExp(/^1[3|4|5|7|8|9][0-9]\d{8}$/).test(value)) {
            var params = {
              tableName: 'sys_user',
              fieldName: 'phone',
              fieldVal: value,
              dataId: this.userId
            }
            duplicateCheck(params).then((res) => {
              if (res.success) {
                callback()
              } else {
                callback(new Error('手机号已存在!'))
              }
            })
          } else {
            callback(new Error('请输入正确格式的手机号码!'))
          }
        }
      },
      validateEmail(rule, value, callback) {
        if (!value) {
          callback()
        } else {
          if (new RegExp(/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/).test(value)) {
            var params = {
              tableName: 'sys_user',
              fieldName: 'email',
              fieldVal: value,
              dataId: this.userId
            }
            duplicateCheck(params).then((res) => {
              // console.log(res)
              if (res.success) {
                callback()
              } else {
                callback(new Error('邮箱已存在!'))
              }
            })
          } else {
            callback(new Error('请输入正确格式的邮箱!'))
          }
        }
      },
      validateUsername(rule, value, callback) {
        var params = {
          tableName: 'sys_user',
          fieldName: 'username',
          fieldVal: value,
          dataId: this.userId
        }
        duplicateCheck(params).then((res) => {
          if (res.success) {
            callback()
          } else {
            callback(new Error('用户名已存在!'))
          }
        })
      },
      handleConfirmBlur(e) {
        const value = e.target.value
        this.confirmDirty = this.confirmDirty || !!value
      },
      normFile(e) {
        if (Array.isArray(e)) {
          return e
        }
        return e && e.fileList
      },
      beforeUpload: function(file) {
        var fileType = file.type
        if (fileType.indexOf('image') < 0) {
          this.$message.warning('请上传图片')
          return false
        }
        //TODO 验证文件大小
      },
      handleChange(info) {
        this.picUrl = ''
        if (info.file.status === 'uploading') {
          this.uploadLoading = true
          return
        }
        if (info.file.status === 'done') {
          var response = info.file.response
          this.uploadLoading = false
          // console.log(response)
          if (response.success) {
            this.model.avatar = response.message
            this.picUrl = 'Has no pic url yet'
          } else {
            this.$message.warning(response.message)
          }
        }
      },
      getAvatarView() {
        return this.url.imgerver + '/' + this.model.avatar
      },
      // 搜索用户对应的部门API
      onSearch() {
        this.$refs.departWindow.add(this.checkedDepartKeys, this.userId)
      },
      // 获取用户对应部门弹出框提交给返回的数据
      modalFormOk (formData) {
        this.checkedDepartNames = [];
        this.selectedDepartKeys = [];
        this.formModel.checkedDepartNameString = '';
        this.userId = formData.userId;
        this.userDepartModel.userId = formData.userId;
        this.formModel.departIds=[];
        this.resultDepartOptions=[];
        var depart=[];
        for (let i = 0; i < formData.departIdList.length; i++) {
          this.selectedDepartKeys.push(formData.departIdList[i].key);
          this.checkedDepartNames.push(formData.departIdList[i].title);
          this.formModel.checkedDepartNameString = this.checkedDepartNames.join(",");
          //新增部门选择，如果上面部门选择后不为空直接付给负责部门
          depart.push({
            key:formData.departIdList[i].key,
            title:formData.departIdList[i].title
          })
          this.formModel.departIds.push(formData.departIdList[i].key)
        }
        this.$refs.ruleForm.validateField('checkedDepartNameString')
        this.resultDepartOptions=depart;
        this.userDepartModel.departIdList = this.selectedDepartKeys;
        this.checkedDepartKeys = this.selectedDepartKeys  //更新当前的选择keys
      },
      // 根据屏幕变化,设置抽屉尺寸
      resetScreenSize() {
        let screenWidth = document.body.clientWidth
        if (screenWidth < 500) {
          this.drawerWidth = screenWidth
        } else {
          this.drawerWidth = 700
        }
      },
      identityChange(e) {
        if (e.target.value === '1') {
          this.departIdShow = false
        } else {
          this.departIdShow = true
        }
      },
      loadCheckedDeparts() {
        let that = this
        if (!that.userId) {
          return
        }
        getAction(that.url.userWithDepart, { userId: that.userId }).then((res) => {
          that.checkedDepartNames = []
          if (res.success) {
            var depart = []
            var departId = []
            for (let i = 0; i < res.result.length; i++) {
              that.checkedDepartNames.push(res.result[i].title)
              that.formModel.checkedDepartNameString = this.checkedDepartNames.join(',')
              that.checkedDepartKeys.push(res.result[i].key)
              //新增负责部门选择下拉框
              depart.push({
                key: res.result[i].key,
                title: res.result[i].title
              })
              departId.push(res.result[i].key)
            }
            that.$refs.ruleForm.validateField('checkedDepartNameString')
            that.resultDepartOptions = depart
            //判断部门id是否存在，不存在择直接默认当前所在部门
            if (this.model.departIds) {
              this.formModel.departIds = this.model.departIds.split(',')
            } else {
              this.formModel.departIds = departId
            }
            that.userDepartModel.departIdList = that.checkedDepartKeys
          } else {
            // console.log(res.message)
          }
        })
      }
    },
    watch: {
      visible(newValue) {
        if (newValue) {
          this.$nextTick(() => {
            this.formModel.birthday = !this.model.birthday ? null : moment(this.model.birthday, this.dateFormat)
          })
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .avatar-uploader > .ant-upload {
    width: 104px;
    height: 104px;
  }

  .ant-upload-select-picture-card i {
    font-size: 49px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .drawer-bootom-button {
    position: absolute;
    bottom: -8px;
    width: 100%;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    left: 0;
    background: #fff;
    border-radius: 0 0 2px 2px;
  }

  .dept-item {
    label {
      &::after {
        display: inline-block;
        margin-right: 4px;
        color: #f5222d;
        font-size: 1rem;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: '*';
      }
    }
  }
</style>
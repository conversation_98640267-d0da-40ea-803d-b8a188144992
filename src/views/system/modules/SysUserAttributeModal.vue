<template>
  <a-modal
    :title="title"
    :width="1000"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row class="form-row" :gutter="16">
          <a-col :lg="5">
            <a-form-item
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              label="婚否状态">
              <a-switch checkedChildren="显示" unCheckedChildren="隐藏" v-model="maritalStatus"/>
            </a-form-item>
          </a-col>
          <a-col :lg="7">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="婚否文案">
              <a-input placeholder="请输入婚否文案" v-decorator="['maritalText', {}]"/>
            </a-form-item>
          </a-col>
          <a-col :lg="5">
            <a-form-item
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              label="文化程度状态">
              <a-switch checkedChildren="显示" unCheckedChildren="隐藏" v-model="culturalStatus"/>
            </a-form-item>
          </a-col>
          <a-col :lg="7">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="文化程度文案">
              <a-input placeholder="请输入文化程度文案" v-decorator="['culturalText', {}]"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row class="form-row" :gutter="16">
          <a-col :lg="5">
            <a-form-item
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              label="职业状态">
              <a-switch checkedChildren="显示" unCheckedChildren="隐藏" v-model="professionStatus"/>
            </a-form-item>
          </a-col>
          <a-col :lg="7">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="职业文案">
              <a-input placeholder="请输入职业文案" v-decorator="['professionText', {}]"/>
            </a-form-item>
          </a-col>
          <a-col :lg="5">
            <a-form-item
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              label="编号状态">
              <a-switch checkedChildren="显示" unCheckedChildren="隐藏" v-model="userNumberStatus"/>
            </a-form-item>
          </a-col>
          <a-col :lg="7">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="编号文案">
              <a-input placeholder="请输入门诊号文案" v-decorator="['userNumberText', {}]"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row class="form-row" :gutter="16">
          <a-col :lg="5">
            <a-form-item
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              label="姓名状态">
              <a-switch checkedChildren="显示" unCheckedChildren="隐藏" v-model="nameStatus"/>
            </a-form-item>
          </a-col>
          <a-col :lg="7">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="姓名文案">
              <a-input placeholder="请输入姓名文案" v-decorator="['nameText', {}]"/>
            </a-form-item>
          </a-col>
          <a-col :lg="5">
            <a-form-item
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              label="性别状态">
              <a-switch checkedChildren="显示" unCheckedChildren="隐藏" v-model="sexStatus"/>
            </a-form-item>
          </a-col>
          <a-col :lg="7">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="性别文案">
              <a-input placeholder="请输入性别文案" v-decorator="['sexText', {}]"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row class="form-row" :gutter="16">
          <a-col :lg="5">
            <a-form-item
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              label="年龄状态">
              <a-switch checkedChildren="显示" unCheckedChildren="隐藏" v-model="ageStatus"/>
            </a-form-item>
          </a-col>
          <a-col :lg="7">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="年龄文案">
              <a-input placeholder="请输入年龄文案" v-decorator="['ageText', {}]"/>
            </a-form-item>
          </a-col>
          <a-col :lg="5">
            <a-form-item
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              label="生日状态">
              <a-switch checkedChildren="显示" unCheckedChildren="隐藏" v-model="birthdayStatus"/>
            </a-form-item>
          </a-col>
          <a-col :lg="7">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="生日文案">
              <a-input placeholder="请输入生日文案" v-decorator="['birthdayText', {}]"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row class="form-row" :gutter="16">
          <a-col :lg="5">
            <a-form-item
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              label="民族状态">
              <a-switch checkedChildren="显示" unCheckedChildren="隐藏" v-model="nationalityStatus"/>
            </a-form-item>
          </a-col>
          <a-col :lg="7">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="民族文案">
              <a-input placeholder="请输入民族文案" v-decorator="['nationalityText', {}]"/>
            </a-form-item>
          </a-col>
          <a-col :lg="5">
            <a-form-item
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              label="籍贯状态">
              <a-switch checkedChildren="显示" unCheckedChildren="隐藏" v-model="nativePlaceStatus"/>
            </a-form-item>
          </a-col>
          <a-col :lg="7">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="籍贯文案">
              <a-input placeholder="请输入籍贯文案" v-decorator="['nativePlaceText', {}]"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row class="form-row" :gutter="16">
          <a-col :lg="5">
            <a-form-item
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              label="联系电话状态">
              <a-switch checkedChildren="显示" unCheckedChildren="隐藏" v-model="telphoneStatus"/>
            </a-form-item>
          </a-col>
          <a-col :lg="7">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="联系电话文案">
              <a-input placeholder="请输入联系电话文案" v-decorator="['telphoneText', {}]"/>
            </a-form-item>
          </a-col>
          <a-col :lg="5">
            <a-form-item
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              label="案号状态">
              <a-switch checkedChildren="显示" unCheckedChildren="隐藏" v-model="legalCaseNoStatus"/>
            </a-form-item>
          </a-col>
          <a-col :lg="7">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="案号文案">
              <a-input placeholder="请输入案号文案" v-decorator="['legalCaseNoText', {}]"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row class="form-row" :gutter="16">
          <a-col :lg="5">
            <a-form-item
              :labelCol="labelCol2"
              :wrapperCol="wrapperCol2"
              label="单位状态">
              <a-switch checkedChildren="显示" unCheckedChildren="隐藏" v-model="departmentNameStatus"/>
            </a-form-item>
          </a-col>
          <a-col :lg="7">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="单位文案">
              <a-input placeholder="请输入联系电话文案" v-decorator="['departmentNameText', {}]"/>
            </a-form-item>
          </a-col>
          <a-col :lg="5">
          </a-col>
          <a-col :lg="7">
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { getAction, httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import ACol from 'ant-design-vue/es/grid/Col'

  export default {
    name: 'SysUserAttributeModal',
    components: { ACol },
    data() {
      return {
        title: '操作',
        visible: false,
        userId: '',
        model: {},
        maritalStatus: false,
        culturalStatus: false,
        professionStatus: false,
        userNumberStatus: false,
        nameStatus: false,
        sexStatus: false,
        ageStatus: false,
        birthdayStatus: false,
        nationalityStatus: false,
        nativePlaceStatus: false,
        telphoneStatus: false,
        legalCaseNoStatus: false,
        departmentNameStatus: false,
        labelCol: {
          xs: { span: 24 },
          sm: { span: 9 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        labelCol2: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        wrapperCol2: {
          xs: { span: 24 },
          sm: { span: 9 }
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {},
        url: {
          add: '/system/sysUserAttribute/add',
          edit: '/system/sysUserAttribute/edit',
          queryById: '/system/sysUserAttribute/queryById'
        }
      }
    },
    created() {
    },
    methods: {
      show(id) {
        this.form.resetFields()
        this.visible = true
        this.userId = id
        this.laodData(id)
      },
      close() {
        this.$emit('close')
        this.visible = false
        this.disableSubmit = false
      },
      laodData(userId) {
        let that = this
        getAction(this.url.queryById, { userId: userId }).then((res) => {
          if (res.success) {
            that.model = Object.assign({}, res.result)
            that.maritalStatus = res.result.maritalStatus == 1
            that.culturalStatus = res.result.culturalStatus == 1
            that.professionStatus = res.result.professionStatus == 1
            that.userNumberStatus = res.result.userNumberStatus == 1
            that.nameStatus = res.result.nameStatus == 1
            that.sexStatus = res.result.sexStatus == 1
            that.ageStatus = res.result.ageStatus == 1
            that.birthdayStatus = res.result.birthdayStatus == 1
            that.nationalityStatus = res.result.nationalityStatus == 1
            that.nativePlaceStatus = res.result.nativePlaceStatus == 1
            that.telphoneStatus = res.result.telphoneStatus == 1
            that.legalCaseNoStatus = res.result.legalCaseNoStatus == 1
            that.departmentNameStatus = res.result.departmentNameStatus == 1
            that.$nextTick(() => {
              this.form.setFieldsValue(pick(this.model, 'maritalText', 'culturalText', 'professionText', 'userNumberText', 'nameText', 'sexText',
                'ageText', 'birthdayText', 'nationalityText', 'nativePlaceText', 'telphoneText', 'legalCaseNoText','departmentNameText'))
            })
          }
        })
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            formData.maritalStatus = that.maritalStatus ? 1 : 0
            formData.culturalStatus = that.culturalStatus ? 1 : 0
            formData.professionStatus = that.professionStatus ? 1 : 0
            formData.userNumberStatus = that.userNumberStatus ? 1 : 0
            formData.nameStatus = that.nameStatus ? 1 : 0
            formData.sexStatus = that.sexStatus ? 1 : 0
            formData.ageStatus = that.ageStatus ? 1 : 0
            formData.birthdayStatus = that.birthdayStatus ? 1 : 0
            formData.nationalityStatus = that.nationalityStatus ? 1 : 0
            formData.nativePlaceStatus = that.nativePlaceStatus ? 1 : 0
            formData.telphoneStatus = that.telphoneStatus ? 1 : 0
            formData.legalCaseNoStatus = that.legalCaseNoStatus ? 1 : 0
            formData.departmentNameStatus = that.departmentNameStatus ? 1 : 0
            formData.userId = that.userId
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>

<style lang="less" scoped>

</style>
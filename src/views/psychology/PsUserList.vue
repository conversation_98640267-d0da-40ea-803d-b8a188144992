<template>
  <a-card :bordered='false'>

    <!-- 查询区域 -->
    <div class='table-page-search-wrapper'>
      <a-form layout='inline'>
        <a-row :gutter='24'>
          <a-col :md='6' :sm='8'>
            <a-form-item label='姓名'>
              <j-input placeholder='请输入姓名' v-model='queryParam.name'></j-input>
            </a-form-item>
          </a-col>
          <a-col :md='6' :sm='8'>
            <a-form-item label='编号'>
              <j-input placeholder='请输入编号' v-model='queryParam.userNumber'></j-input>
            </a-form-item>
          </a-col>
          <template v-if='toggleSearchStatus'>
            <a-col :md='6' :sm='8'>
              <a-form-item label='编码前缀'>
                <j-input placeholder='请输入编码前缀' v-model='queryParam.prefixEncode'></j-input>
              </a-form-item>
            </a-col>
            <a-col :md='6' :sm='8'>
              <a-form-item label='联系电话'>
                <j-input placeholder='请输入联系电话' v-model='queryParam.telphone'></j-input>
              </a-form-item>
            </a-col>
            <a-col :md='6' :sm='8'>
              <a-form-item label='科室'>
                <j-input placeholder='请输入科室' v-model='queryParam.departmentName'></j-input>
              </a-form-item>
            </a-col>
            <a-col :xl='6' :lg='7' :md='8' :sm='24'>
              <a-form-item label='创建时间'>
                <a-range-picker
                  v-model='searchTime'
                  date-format='YYYY-MM-DD'
                  valueFormat='YYYY-MM-DD'
                  @change='onStartTimeChangeAddTime'
                />
              </a-form-item>
            </a-col>
            <a-col :xl='6' :lg='7' :md='8' :sm='24'>
              <a-form-item label='年龄区间'>
                <a-input type='number' placeholder='' v-model='queryParam.beginAge' style='width: 45%;'></a-input>
                ~
                <a-input type='number' placeholder='' v-model='queryParam.endAge' style='width: 45%;'></a-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :md='6' :sm='8'>
            <span style='float: left;overflow: hidden;' class='table-page-search-submitButtons'>
              <a-button type='primary' @click='searchQuery' icon='search'>查询</a-button>
              <a-button type='primary' @click='searchReset' icon='reload' style='margin-left: 8px'>重置</a-button>
              <a @click='handleToggleSearch' style='margin-left: 8px'>
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class='table-operator'>
      <a-button @click='handleAdd' type='primary' icon='plus'>新增</a-button>
      <a-button type='primary' icon='download' @click="handleExportThemplate('测试者信息')">导出模板</a-button>
      <a-upload name='file' :showUploadList='false' :multiple='false' :headers='tokenHeader' :action='importExcelUrl'
                @change='handleImportExcel'>
        <a-button type='primary' icon='import'>导入</a-button>
      </a-upload>
       <a-button type="primary" icon="arrow-up" @click="handleExportXls('患者信息')">导出</a-button>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref='table'
        size='middle'
        bordered
        rowKey='id'
        :columns='columns'
        :dataSource='dataSource'
        :pagination='ipagination'
        :loading='loading'
        :rowSelection='{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}'
        @change='handleTableChange'>

        <span slot='userNumber' slot-scope='text, record'>{{ record.prefixEncode }}{{ record.userNumber }}</span>

        <span slot='action' slot-scope='text, record'>
          <a @click='goUserDetail(record.id)'>档案详情</a>
          <a-divider v-if="isShowAuth('home:list:cognize:action')" type='vertical' />
          <a v-if="isShowAuth('home:list:cognize:action')" @click='goCognize(record)'>认知训练</a>
          <a-divider v-if="isShowAuth('home:list:cognize:action')" type='vertical' />
          <a v-if="isShowAuth('home:list:cognize:action')" @click='setTrain(record)'>分配方案</a>
          <a-divider type='vertical' />
          <a-dropdown>
            <a class='ant-dropdown-link'>更多 <a-icon type='down' /></a>
            <a-menu slot='overlay'>
              <a-menu-item>
                <a @click='handleAnswer(record)'>答题</a>
              </a-menu-item>
              <a-menu-item>
                <a @click='handleEdit(record)'>编辑</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)'>
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <psUser-modal ref='modalForm' @ok='modalFormOk'></psUser-modal>
    <PaAnswerModal ref='answerModal' :chooseData='chooseData'></PaAnswerModal>
    <!--  认知推荐  -->
    <CognizeRecommend ref='CognizeRecommend'></CognizeRecommend>
    <PsTrainModal ref='trainModal'></PsTrainModal>
  </a-card>
</template>

<script>
import PsUserModal from './modules/PsUserModal'
import PaAnswerModal from './modules/PaAnswerModal.vue'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JInput from '@/components/jeecg/JInput'
import CognizeRecommend from '@views/dashboard/CognizeRecommend'
import { downFile } from '@/api/manage'
import PsTrainModal from './modules/PsTrainModal.vue'
import { globalShowedAuth } from '@/utils/authFilter'

export default {
  name: 'PsUserList',
  mixins: [JeecgListMixin],
  components: {
    JInput,
    PsUserModal,
    PaAnswerModal,
    CognizeRecommend,
    PsTrainModal
  },
  data() {
    return {
      description: '测试者信息管理页面',
      // 表头
      columns: [
        {
          title: '编号',
          align: 'center',
          dataIndex: 'userNumber',
          scopedSlots: { customRender: 'userNumber' }
        },
        {
          title: '姓名',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '联系电话',
          align: 'center',
          dataIndex: 'telphone'
        },
        {
          title: '性别',
          align: 'center',
          dataIndex: 'sex_dictText'
        },
        {
          title: '年龄',
          align: 'center',
          dataIndex: 'age'
        },
        {
          title: '婚否',
          align: 'center',
          dataIndex: 'maritalStatus_dictText'
        },
        {
          title: '文化程度',
          align: 'center',
          dataIndex: 'cultural_dictText'
        },
        {
          title: '职业',
          align: 'center',
          dataIndex: 'profession'
        },
        {
          title: '被试来源',
          align: 'center',
          dataIndex: 'testerSource_dictText'
        },
        {
          title: '民族',
          align: 'center',
          dataIndex: 'nationality_dictText'
        },
        {
          title: '生日',
          align: 'center',
          dataIndex: 'birthday'
        },
        {
          title: '科室',
          align: 'center',
          dataIndex: 'departmentName'
        },
        {
          title: '籍贯',
          align: 'center',
          dataIndex: 'nativePlace'
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'center',
          sorter: true
        },
        // {
        //   title: '修改时间',
        //   dataIndex: 'updateTime',
        //   align:"center",
        //   sorter: true
        // },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/psychology/psUser/list',
        delete: '/psychology/psUser/delete',
        deleteBatch: '/psychology/psUser/deleteBatch',
        exportXlsUrl: 'psychology/psUser/exportXls',
        importExcelUrl: 'psychology/psUser/importExcel'
      },
      chooseData: {}
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  created() {
  },
  methods: {
    isShowAuth(code) {
      return globalShowedAuth(code)
    },
    goCognize(record) {
      this.$refs.CognizeRecommend.loadUserId(record.id)
      this.$refs.CognizeRecommend.title = '智能推送训练'
    },

    setTrain(record) {
      this.$refs.trainModal.add(record.id)
      this.$refs.trainModal.title = '分配训练方案'
    },
    goUserDetail(id) {
      this.$router.push({ path: '/psychology/modules/PsUserDetailModal/' + id })
    },

    handleAnswer(record) {
      this.chooseData = record
      this.$refs.answerModal.visible = true
      this.$refs.answerModal.loadMaterialData()
    },
    handleExportXls(fileName, method = 'get') {
      if (!fileName || typeof fileName != 'string') {
        fileName = '导出文件'
      }
      let param = this.getQueryParams()//查询条件
      delete param.createTimeRange // 时间参数不传递后台
      delete param.pageNo
      delete param.pageSize
      if (param.measureId) {
        param.measureId = param.measureId.split(',')[0]
      }
      downFile(this.url.exportXlsUrl, param, method).then((data) => {
        if (!data) {
          this.$message.warning('文件下载失败')
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), fileName + '.xls')
        } else {
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName + '.xls')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(url) //释放掉blob对象
        }
      })
    },
    handleExportThemplate() {
      window.location.href = 'https://img.zhisongkeji.com/template/%E5%BF%83%E7%90%86CT%E6%82%A3%E8%80%85%E6%A1%A3%E6%A1%88%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'
    }
  }
}
</script>
<style lang='less' scoped>
/** Button按钮间距 */
.ant-btn {
  margin-left: 3px
}

.ant-card-body .table-operator {
  margin-bottom: 18px;
}

.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

.anty-row-operator button {
  margin: 0 5px
}

.ant-btn-danger {
  background-color: #ffffff
}

.ant-modal-cust-warp {
  height: 100%
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden
}
</style>
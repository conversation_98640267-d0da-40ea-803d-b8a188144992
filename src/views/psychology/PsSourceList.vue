<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="标题">
              <a-input placeholder="请输入标题" v-model="queryParam.title"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :md="6" :sm="8">
              <a-form-item label="文章副标题">
                <a-input placeholder="请输入文章副标题" v-model="queryParam.subTitle"></a-input>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="8">
              <a-form-item label="描述、摘要(文章底部）">
                <a-input placeholder="请输入描述、摘要(文章底部）" v-model="queryParam.description"></a-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">
        <template slot="avatarslot" slot-scope="text, record">
          <div class="anty-img-wrap">
            <a-avatar shape="square" :src="getAvatarView(record.titleImg)" icon="user"/>
          </div>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleOpen(record)">查看</a>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <psArticle-modal ref="modalForm" @ok="modalFormOk"></psArticle-modal>
    <ps-article-info ref="modalInfo"></ps-article-info>
  </a-card>
</template>

<script>
  import PsArticleModal from './modules/PsArticleModal1'
  import PsArticleInfo from './modules/PsArticleInfo'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import { getAction } from '@/api/manage'

  export default {
    name: 'PsSourceList',
    mixins: [JeecgListMixin],
    components: {
      PsArticleModal,
      PsArticleInfo
    },
    data() {
      return {
        description: '科普文章表查看页面',
        // 表头
        columns: [
          {
            title: '标题',
            align: 'center',
            dataIndex: 'title'
          },
          {
            title: '标题图（缩略图）',
            align: 'center',
            dataIndex: 'titleImg',
            scopedSlots: { customRender: 'avatarslot' }
          },
          {
            title: '作者',
            align: 'center',
            dataIndex: 'author'
          },
          {
            title: '文章类型',
            align: 'center',
            dataIndex: 'type_dictText'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          imgerver: window._CONFIG['staticDomainURL'],
          list: '/psychology/psArticle/list',
          delete: '/psychology/psArticle/delete',
          deleteBatch: '/psychology/psArticle/deleteBatch',
          exportXlsUrl: 'psychology/psArticle/exportXls',
          importExcelUrl: 'psychology/psArticle/importExcel',
          hitOrOutShelf: 'psychology/psArticle/hitOrOutShelf'
        }
      }
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {
      getAvatarView: function(avatar) {
        return this.url.imgerver + '/' + avatar
      },
      /*上架*/
      hitShelf(record) {
        let that = this
        let httpurl = that.url.hitOrOutShelf
        let formData = {}
        formData.id = record.id
        formData.status = 1
        getAction(httpurl, formData).then((res) => {
          if (res.success) {
            this.loadData();
            that.$message.success('上架成功')
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      },
      /*下架*/
      outShelf(record) {
        let that = this
        let httpurl = that.url.hitOrOutShelf
        let formData = {}
        formData.id = record.id
        formData.status = 2
        getAction(httpurl, formData).then((res) => {
          if (res.success) {
            this.loadData();
            that.$message.success('下架成功')
          } else {
            that.$message.warning(res.message)
          }
        }).finally(() => {
        })
      },
      handleOpen(record) {
        this.$refs.modalInfo.open(record)
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>
<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="标题">
              <a-input placeholder="请输入标题" v-model="queryParam.title"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="类型">
              <j-dict-select-tag v-model="queryParam.categoryId" placeholder="请选择类型" dictCode="base_train_category,title,id"/>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('训练表')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl"
                @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete"/>
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down"/>
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
        selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt=""
               style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a v-has="'relaxTrain:list:edit'" @click="handleDetail(record)">详情</a>
          <a-divider v-has="'relaxTrain:list:edit'" type="vertical"/>
          <a v-has="'relaxTrain:list:edit'" @click="handleEdit(record)">编辑</a>
          <a-divider v-has="'relaxTrain:list:edit'" type="vertical"/>
          <a v-if="!record.playStatus" @click="handlePlay(record)">播放</a>
          <a v-if="record.playStatus === 1" @click="handleEnd(record)">停止</a>
          <a-divider v-has="'relaxTrain:list:edit'" type="vertical"/>
          <a-popconfirm v-has="'relaxTrain:list:edit'" title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>

      </a-table>
    </div>
    <audio ref="music" muted controls="controls" style="display:none">
      <source :src="audioUrl" type="audio/mpeg" />
    </audio>
    <base-train-modal ref="modalForm" @ok="modalFormOk"></base-train-modal>
  </a-card>
</template>

<script>

  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import BaseTrainModal from './modules/BaseTrainModal'

  export default {
    name: 'BaseTrainList',
    mixins: [JeecgListMixin],
    components: {
      BaseTrainModal
    },
    data() {                                                                                                                                                                 
      return {
        audioUrlPrefix: window._CONFIG['measureAudioURL'], //音频前缀，默认为空，由于本地没有音频文件，本地调试的时候此处配置线上绝对路径  https://zcpm.zhisongkeji.com
        audioUrl: '',
        description: '训练表管理页面',
        // 表头
        columns: [
          {
            title: '标题',
            align: 'center',
            dataIndex: 'title'
          },
          {
            title: '标题图',
            align: 'center',
            dataIndex: 'titleImg',
            scopedSlots: { customRender: 'imgSlot' }
          },
          {
            title: '类别',
            align: 'center',
            dataIndex: 'categoryId_dictText'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 185,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: '/base/baseTrain/list',
          delete: '/base/baseTrain/delete',
          deleteBatch: '/base/baseTrain/deleteBatch',
          exportXlsUrl: '/base/baseTrain/exportXls',
          importExcelUrl: 'base/baseTrain/importExcel'

        },
        dictOptions: {},
        oldRecord: {},
        superFieldList: []
      }
    },
    created() {
      this.getSuperFieldList()
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {
      initDictConfig() {
      },
      getSuperFieldList() {
        let fieldList = []
        fieldList.push({ type: 'string', value: 'title', text: '标题', dictCode: '' })
        fieldList.push({ type: 'string', value: 'category', text: '类别（中文）', dictCode: '' })
        fieldList.push({ type: 'string', value: 'description', text: '描述', dictCode: '' })
        fieldList.push({ type: 'string', value: 'voicePath', text: '语音地址', dictCode: '' })
        fieldList.push({ type: 'int', value: 'status', text: '状态（1_启用，2_禁用）', dictCode: '' })
        fieldList.push({ type: 'string', value: 'tenantId', text: '租户id', dictCode: '' })
        this.superFieldList = fieldList
      },
      handlePlay(record) {
        this.audioUrl = record.voicePath
        if (this.oldRecord) {
          this.$set(this.oldRecord, 'playStatus', 0)
        }
        
        this.$refs.music.load()
        this.$nextTick(() => {
          this.$refs.music.play()
          this.$set(record, 'playStatus', 1)
        })
        this.oldRecord = record
      },
      handleEnd(record) {
        this.$refs.music.pause()
        this.$set(record, 'playStatus', 0)
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="选择机构">
          <j-select-depart v-decorator="['sysOrgIds']" :trigger-change="true" :multi="true"></j-select-depart>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="选择模板">
          <a-select
            style="width: 100%"
            placeholder="请选择视频模板"
            v-decorator="['templateId']">
            <a-select-option v-for="template in audioTemplates" :key="template.id">
              {{ template.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import JSelectDepart from '@/components/jeecgbiz/JSelectDepart'

  export default {
    name: 'AudioTemplateIssueModal',
    components: { JSelectDepart },
    data() {
      return {
        title: '操作',
        visible: false,
        model: {},
        audioTemplates: [],
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
        },
        url: {
          add: '/vr/audioTplDistribute/add', 
          list: '/vr/audioTpl/list'
        }
      }
    },
    created() {
      this.getList();
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'sysOrgIds', 'templateId'))
        })
      },
      getList() {
        getAction(this.url.list, { pageNo: 1, pageSize: 10000 }).then((res) => {
          if (res.success) {
            this.audioTemplates = res.result.records
          }
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let formData = Object.assign(this.model, values)
            formData.sysOrgIds = formData.sysOrgIds.split(',')
            httpAction(this.url.add, formData, 'post').then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      },
    }
  }
</script>

<style scoped>

</style>
<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="姓名">
          <a-input placeholder="请输入姓名" v-decorator="['name', {}]"/>
        </a-form-item>
        <a-form-item label="量表类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select
            mode="multiple"
            style="width: 100%"
            placeholder="请选择量表类别"
            v-model="selectedCategory">
            <a-select-option v-for="category in psCategorys" :key="category.id">
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="头像">
          <j-image-upload class="avatar-uploader" bizPath="temp/image/psychiatristAvatar" text="上传" v-model="fileList"></j-image-upload>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="标签">
          <a-input placeholder="请输入标签（多种标签用,隔开）" v-decorator="['tag', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="个人一句话简介">
          <a-input placeholder="请输入个人一句话简介" v-decorator="['briefIntroduce', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="从业年限">
          <a-input placeholder="请输入从业年限" v-decorator="['workingPeriod', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="手机号">
          <a-input placeholder="请输入手机号" v-decorator="['phone', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="咨询价格">
          <a-input-number v-decorator="[ 'price', {}]" style="width:100%"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="咨询经验/小时">
          <a-input-number v-decorator="[ 'experienceTime', {}]" style="width:100%"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="咨询人数">
          <a-input-number v-decorator="[ 'consultNum', {}]" style="width:100%"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="咨询方式">
          <a-input placeholder="请输入咨询方式（多种咨询方式用,隔开）" v-decorator="['consultWay', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="个人介绍">
          <a-textarea placeholder="请输入个人介绍" v-decorator="['introduce', {}]" :rows="4"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="可约时间">
          <a-textarea placeholder="请输入可约时间说明" v-decorator="['aboutTime', {}]" :rows="4"/>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { findPsCategoryAll } from '@/api/api'
  import JImageUpload from '../../../components/jeecg/JImageUpload'

  export default {
    name: 'PsPsychiatristModal',
    components: {
      JImageUpload,
    },
    data() {
      return {
        title: '操作',
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        fileList:[],
        psCategorys: [],
        selectedCategory: [],
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {},
        url: {
          add: '/psychology/psPsychiatrist/add',
          edit: '/psychology/psPsychiatrist/edit'
        }
      }
    },
    created() {
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.loadPsTemplate()
        this.visible = true
        if (this.model.categoryId) {
          this.selectedCategory = this.model.categoryId.split(",")
          setTimeout(() => {
            this.fileList = record.avatar;
          }, 5)
        }
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'name', 'avatar', 'tag', 'introduce', 'briefIntroduce', 'workingPeriod',
            'phone', 'price', 'experienceTime', 'consultNum', 'aboutTime', 'consultWay'))
          //时间格式化
        })

      },
      /**
       * 初始化量表类别下拉选
       */
      loadPsTemplate() {
        findPsCategoryAll({}).then((res) => {
          if (res.success) {
            this.psCategorys = res.result
          } else {
            // console.log(res.message)
          }
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
        this.fileList=[];
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            if(that.fileList != ''){
              formData.avatar = that.fileList;
            }else{
              formData.avatar = null;
            }
            //时间格式化
            formData.categoryId = this.selectedCategory.length > 0 ? this.selectedCategory.join(',') : ''
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      }


    }
  }
</script>

<style lang="less" scoped>

</style>
<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="标题">
          <a-input placeholder="请输入标题" v-decorator="['title', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="标题图（缩略图）">
          <j-image-upload class="avatar-uploader" bizPath="temp/image/relaxTrainImg" text="上传" v-model="titleImg"></j-image-upload>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="文章类型">
          <j-dict-select-tag v-model="model.categoryId" placeholder="请选择类别"
                                 dictCode="base_train_category,title,id"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="语音地址">
          <a-input placeholder="请输入语音地址" v-decorator="['voicePath', {}]"/>
        </a-form-item>
        <!-- <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="状态">
          <j-dict-select-tag v-decorator="['status', {}]" :triggerChange="true" placeholder="请选择状态" dictCode="enable_status"/>
        </a-form-item> -->
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="描述">
          <a-textarea placeholder="请输入描述" v-decorator="['description', {}]" :auto-size="{ minRows: 3, maxRows: 5 }"/>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>

  import { getAction, httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import JImageUpload from '../../../components/jeecg/JImageUpload'

  export default {
    name: 'BaseTrainModal',
    components: {
      JImageUpload
    },
    data() {
      return {
        title: '',
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        titleImg: "",
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {},
        url: {
          add: '/base/baseTrain/add',
          edit: '/base/baseTrain/edit',
          queryById: '/base/baseTrain/queryById'
        }
      }
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'title', 'titleImg', 'categoryId', 'voicePath', 'status', 'description'))
          //时间格式化
        })
        if (this.model.id) {
          this.loadResultReportData(this.model.id)
          setTimeout(() => {
            this.titleImg = record.titleImg;
          }, 5)
        }
      },
      loadResultReportData(id) {
        getAction(this.url.queryById, { id: id }).then((res) => {
          if (res.success) {
            this.model = res.result
          }
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
        this.titleImg = "";
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            if(that.titleImg != ''){
              formData.titleImg = that.titleImg;
            }else{
              formData.titleImg = null;
            }
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
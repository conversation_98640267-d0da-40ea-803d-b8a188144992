<template>
  <a-modal
    :title="title"
    width="100%"
    wrapClassName="full-modal"
    @ok="close"
    @cancel="close"
    :visible="visible"
  >
    <div class="info-content">
      <div v-if="model.type == 0" class="article-view">
        <p v-html='content'></p>
      </div>
      <div v-else-if="model.type == 1" class="video-view">
        <video
          class="video-mains"
          controls
          autoplay
          preload="auto"
          :src="content"
          ref="myVideo"
          @pause="pause"
          custom-cache="false"
        >
        </video>
      </div>
    </div>
  </a-modal>
</template>

<script>
  

  export default {
    name: 'PsArticleInfo',
    
    data() {
      return {
        content: '',
        visible: false,
        model: {},
        title: ''
      }
    },
    created() {
    },
    destroyed() {
      this.saveLearnTimeRecord();
    },
    methods: {
      open(record) {
        this.model = Object.assign({}, record)
        this.visible = true
        this.title = this.model.title
        this.content = this.model.content
        console.log(this.model)
      },
      close() {
        if (this.model.type == 1) {
          this.$refs.myVideo.pause()
        }
        this.visible = false
      },
      pause(e) {
      
      },
    }
  }
</script>

<style lang="less">
  .full-modal {
    .ant-modal {
      max-width: 100%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }
    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: calc(100vh);
    }
    .ant-modal-body {
      flex: 1;
      height: calc(100% - 150px);
    }

    .info-content {
      padding: 30px 90px;
      height: 100%;

      .article-view {
        width: 100%;
        height: 100%;
        padding: 0 90px;
        overflow-y: scroll;
      }
      .video-view {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
</style>
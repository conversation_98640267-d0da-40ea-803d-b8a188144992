<template>
  <a-modal
    :title="title"
    :width="1000"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="视频名称">
          <a-input placeholder="请输入视频名称" v-decorator="['videoName', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="视频地址">
          <a-input placeholder="请输入视频地址" v-decorator="['videoUrl', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="背景音乐">
          <a-select placeholder="请选择背景音乐" v-decorator="['audioId', {}]">
            <a-select-option
              v-for="(item,index) in audioList"
              :key="item.id"
              :value="item.id">
              {{ item.audioName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="封面图">
          <j-image-upload class="avatar-uploader" bizPath="temp/image/articleTitleImg" text="上传" v-model="fileList"></j-image-upload>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="视频持续播放时间（秒）">
          <a-input placeholder="请输入视频持续播放时间（秒）" v-decorator="['duration', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="音频延迟播放时间（秒）">
          <a-input placeholder="请输入音频延迟播放时间（秒）" v-decorator="['audioDelay', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="放松训练视频角度0-360">
          <a-input placeholder="请输入放松训练视频角度0-360" v-decorator="['angle', {}]"/>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { getAction, httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import JImageUpload from '@/components/jeecg/JImageUpload'

  export default {
    name: 'VideoModal',
    components: {
      JImageUpload
    },
    data() {
      return {
        title: '操作',
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        fileList:[],
        audioList: [],
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {},
        url: {
          add: '/vr/video/add',
          edit: '/vr/video/edit',
          list: '/vr/audio/list'
        }
      }
    },
    created() {
      this.getAudioList()
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'videoName', 'videoUrl', 'audioId', 'duration', 'audioDelay',
            'angle'))
          //时间格式化
        })
        if (this.model.id) {
          setTimeout(() => {
            this.fileList = record.coverUrl;
          }, 5)
        }
      },
      getAudioList() {
        getAction(this.url.list, {pageNo: 1, pageSize: 10000}).then((res) => {
          if (res.success) {
            this.audioList = res.result.records;
          }
        })

      },
      close() {
        this.$emit('close')
        this.visible = false
        this.fileList=[];
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            if(that.fileList != ''){
              formData.coverUrl = that.fileList;
            }else{
              formData.coverUrl = null;
            }
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })


          }
        })
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>

<style lang="less" scoped>
  .avatar-uploader > .ant-upload {
    width: 104px;
    height: 104px;
  }
</style>
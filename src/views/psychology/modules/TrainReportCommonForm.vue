<template>
  <a-spin :spinning='loading'>

    <div class='no-print' style='text-align: left;margin-left: 35px;'>
      <a-form layout='inline'>
        <a-form-item label='日期区间'>
          <a-range-picker
            v-model='searchTime'
            date-format='YYYY-MM-DD'
            valueFormat='YYYY-MM-DD'
            @change='onStartTimeChangeAddTime'
          />
        </a-form-item>
        <a-form-item>
          <a-button type='primary' @click='searchQuery' icon='search'>查询</a-button>
          <a-button type='primary' @click='searchReset' icon='reload' style='margin-left: 8px'>重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <div v-if='!isEdit'>
      <div class='no-print' style='text-align: right;margin-right: 35px;'>
        <a-button v-print="'#TrainReportCommonForm'" ghost type='primary' style='margin-right: 10px;'>打印</a-button>
        <a-button ghost type='primary' @click='viewEdit' style='margin-right: 10px;'>编辑</a-button>
      </div>
      <div id='TrainReportCommonForm'>
        <p v-html='datas'></p>
      </div>
    </div>
    <div v-if='isEdit'>
      <a-spin :spinning='loading'>
        <a-row>
          <j-editor :j-height='700' v-model='editContent' />
        </a-row>
        <a-row style='margin-top: 15px;'>
          <a-col :span='18'></a-col>
          <a-col :span='6'>
            <a-button ghost type='primary' @click='saveResport' style='margin-right: 10px;'>确定</a-button>
            <a-button ghost type='primary' @click='cancelResport'>取消</a-button>
          </a-col>
        </a-row>
      </a-spin>
    </div>
  </a-spin>
</template>

<script>
import { getAction } from '@/api/manage'
import JEditor from '@/components/jeecg/JEditor'
import { mapGetters } from 'vuex'
import moment from 'moment'

export default {
  name: 'TrainReportCommonForm.vue',
  components: {
    JEditor
  },
  data() {
    return {
      title: '操作',
      loading: false,
      drawerWidth: 700,
      visible: false,
      datas: '',
      isEdit: false,
      editContent: '',
      url: {
        generatePrintHtml: '/train/cogTrainReport/generatePrintHtml'
      },
      id: '',
      queryParam: {
        startTime: moment(new Date()).startOf('month').format('YYYY-MM-DD'),
        endTime: moment(new Date()).endOf('month').format('YYYY-MM-DD')
      },
      searchTime: [moment(new Date()).startOf('month').format('YYYY-MM-DD'), moment(new Date()).endOf('month').format('YYYY-MM-DD')]
    }
  },
  methods: {
    ...mapGetters(['userInfo']),
    handleCancel() {
      this.close()
    },
    edit(id) {
      let that = this
      that.resetScreenSize() // 调用此方法,根据屏幕宽度自适应调整抽屉的宽度
      that.visible = true
      that.id = id
      that.generatePrintHtml(id)
    },
    batchPrint(ids) {
      let that = this
      that.resetScreenSize() // 调用此方法,根据屏幕宽度自适应调整抽屉的宽度
      that.visible = true
      that.generatePrintHtml(ids)
    },
    generatePrintHtml(ids) {
      this.loading = true
      getAction(this.url.generatePrintHtml, {
        userId: ids,
        ...this.queryParam
      }).then((res) => {
        if (res.success) {
          this.datas = res.result
          this.loading = false
        }
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
      this.isEdit = false
    },
    // 根据屏幕变化,设置抽屉尺寸
    resetScreenSize() {
      let screenWidth = document.body.clientWidth
      if (screenWidth < 500) {
        this.drawerWidth = screenWidth
      } else {
        this.drawerWidth = 900
      }
    },
    viewEdit() {
      let that_ = this
      this.isEdit = true
      this.editContent = this.datas
      this.loading = true
      setTimeout(function() {
        that_.loading = false
      }, 250)
    },
    saveResport() {
      this.isEdit = false
      this.datas = this.editContent
    },
    cancelResport() {
      this.isEdit = false
    },
    onOrgOption(checked) {
      if (checked) {
        document.getElementById('showOrgOptionStr').style.display = 'block'
      } else {
        document.getElementById('showOrgOptionStr').style.display = 'none'
      }
    },
    onIntroduction(checked) {
      if (checked) {
        document.getElementById('briefIntroducteStr').style.display = 'block'
      } else {
        document.getElementById('briefIntroducteStr').style.display = 'none'
      }
    },
    onStartTimeChangeAddTime(value, dateString) {
      this.queryParam.startTime = dateString[0] ? dateString[0] : null
      this.queryParam.endTime = dateString[1] ? dateString[1] : null
    },
    searchQuery() {
      this.generatePrintHtml(this.id)
    },
    searchReset() {
      this.queryParam = {
        startTime: moment(new Date()).startOf('month').format('YYYY-MM-DD'),
        endTime: moment(new Date()).endOf('month').format('YYYY-MM-DD')
      }
      this.searchTime = [
        moment(new Date()).startOf('month').format('YYYY-MM-DD'),
        moment(new Date()).endOf('month').format('YYYY-MM-DD')
      ]
      this.generatePrintHtml(this.id)
    }
  }
}
</script>

<style scoped>

</style>
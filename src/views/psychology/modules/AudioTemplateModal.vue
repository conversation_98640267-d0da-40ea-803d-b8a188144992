<template>
  <a-modal
    :title="title"
    :width="1000"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel">
    <!-- 主表单区域 -->
    <a-form :form="form">
      <a-row>
        <a-col :span="24" :gutter="8">
          <a-form-item
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            label="模板名称">
            <a-input placeholder="请输入模板名称" v-decorator="['templateName', {}]"/>
          </a-form-item>
          <a-form-item
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            label="音频">
            <a-select placeholder="请选择音频" mode="tags" v-decorator="['audioIds', {}]">
              <a-select-option
                v-for="(item) in audioList"
                :key="item.id"
                :value="item.id">
                {{ item.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>
<script>

  import pick from 'lodash.pick'
  import { getAction, httpAction } from '@/api/manage'

  export default {
    name: 'AudioTemplateModal',
    data() {
      return {
        title: '量表单价设置',
        visible: false,
        validatorRules: {},
        confirmLoading: false,
        audioList: [],
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        model: {
        },
        form: this.$form.createForm(this),
        url: {
          add: '/vr/audioTpl/add',
          edit: '/vr/audioTpl/edit',
          audioList: '/vr/audio/findAudioList',
          queryById: '/vr/audioTpl/queryById'
        },
      }
    },
    created() {
      this.getVideoList()
    },
    methods: {
      add() {
        this.edit({})
      },
      getVideoList() {
        getAction(this.url.audioList, null).then((res) => {
          if (res.success) {
            this.audioList = res.result
          }
        })
      },
      edit(record) {
        this.form.resetFields()
        this.visible = true
        if (record.id) this.getDetail(record.id)
      },
      getDetail(id) {
        getAction(this.url.queryById, { id }).then((res) => {
          if (res.success) {
            const data = {
              templateName: res.result.name,
              audioIds: res.result.audioList.map(item => item.id),
              templateId: res.result.id
            }
            this.model = Object.assign({}, data)
            this.form.setFieldsValue(pick(data, 'templateName', 'audioIds'))
          }
        })

      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.templateId) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
</style>
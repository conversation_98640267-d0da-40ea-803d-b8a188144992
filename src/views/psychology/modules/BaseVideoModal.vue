<template>
  <a-modal
    :title="title"
    :width="width"
    :visible="visible"
    :closable="false"
    switchFullscreen
    okText="关闭"
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    cancelText="关闭">
    <div class="video-player vjs-custom-skin">
      <video-player class="video-player vjs-custom-skin"
                    ref="videoPlayer"
                    @loadeddata="onPlayerLoadeddata($event)"
                    @timeupdate="onPlayerTimeupdate($event)"
                    :playsinline="true"
                    :options="playerOptions"> 
      </video-player>
      <!-- <a class="download-btn" :href="videoUrl" download="video.mp4" @click="downloadVideo">下载视频</a> -->
    </div>
  </a-modal>
</template>

<script>
  import { videoPlayer } from 'vue-video-player'
  import 'video.js/dist/video-js.css'
  import 'vue-video-player/src/custom-theme.css'

  export default {
    name: 'SchemeModal',
    components: {
      videoPlayer
    },
    data() {
      return {
        title: '',
        schemeId: '',
        schemeInfo: {},
        playerOptions: {},
        width: 1200,
        visible: false,
        disableSubmit: false,
        duration: 0,
        currentTime: 0,
        videoUrl: ''
      }
    },
    methods: {
      playVideo(videoPath) {
        this.videoUrl = videoPath
        this.visible = true
        this.$nextTick(() => {
          this.playerOptions = {
            playbackRates: [0.7, 1.0, 1.5, 2.0], // 播放速度
            autoplay: false, // 如果true,浏览器准备好时开始回放。
            muted: false, // 默认情况下将会消除任何音频。
            loop: false, // 导致视频一结束就重新开始。
            preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
            language: 'zh-CN',
            aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
            fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
            sources: [{
              type: 'video/mp4', // 这里的种类支持很多种：基本视频格式、直播、流媒体等，具体可以参看git网址项目
              src: videoPath // url地址
            }],
            poster: '', // 你的封面地址
            notSupportedMessage: '此视频暂无法播放，请稍后再试', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
            controlBar: {
              timeDivider: true,
              durationDisplay: true,
              remainingTimeDisplay: false,
              fullscreenToggle: true // 全屏按钮
            }
          }
        })
      },
      onPlayerLoadeddata(player) {
        this.duration = player.cache_.duration
        this.currentTime = player.cache_.currentTime
      },
      onPlayerTimeupdate(player) {
        this.duration = player.cache_.duration
        this.currentTime = player.cache_.currentTime
      },
      handleOk() {
        this.visible = false
      },
      downloadVideo() {
        // 创建一个 HTTP 请求对象
        const xhr = new XMLHttpRequest()
        xhr.open('GET', this.videoUrl, true)
        xhr.responseType = 'blob'

        // 监听请求完成事件
        xhr.onload = () => {
          // 将响应结果转换为 Blob 对象
          const blob = new Blob([xhr.response])

          // 创建一个可下载的 URL
          const url = window.URL.createObjectURL(blob)

          // 创建一个 <a> 标签并模拟点击事件
          const link = document.createElement('a')
          link.href = url
          link.download = 'video.mp4'
          link.click()

          // 释放 URL 对象
          window.URL.revokeObjectURL(url)
        }

        // 发送 HTTP 请求
        xhr.send()
      }
    }
  }
</script>

<style lang="scss" scoped>
::v-deep .ant-btn {
  display: none;
}
::v-deep .ant-btn-primary {
  display: inline-block;
}

.video-player {
  position: relative;
  .download-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 5px 12px;
    background: #fff;
    color: #333;
    border-radius: 5px;
    cursor: pointer;
  }
}
</style>
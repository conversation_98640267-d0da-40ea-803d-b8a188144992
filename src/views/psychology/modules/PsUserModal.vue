<template>
  <a-modal
    :title="title"
    :width="1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row class="form-row" :gutter="16">
          <a-col :lg='8'>
              <a-form-item
                :labelCol='labelCol'
                :wrapperCol='wrapperCol'
                label='编号'>
                <a-auto-complete
                  option-label-prop='value'
                  v-model='model.userNumber'
                >
                  <a-input placeholder='请输入编号' v-decorator="['userNumber', validatorRules.userNumber ]"
                           autocomplete='off'>
                    <a-tooltip slot='suffix' title='生成编号'>
                      <a-icon type='redo' style='color: rgba(0,0,0,.45)' @click='generateNumber' />
                    </a-tooltip>
                  </a-input>
                </a-auto-complete>
              </a-form-item>
            </a-col>
          <a-col :lg="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="姓名">
              <a-input placeholder="请输入姓名" v-decorator="['name', validatorRules.name ]"/>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="性别">
              <j-dict-select-tag v-decorator="['sex', validatorRules.sex]" :triggerChange="true" placeholder="请输入用户性别"
                                 dictCode="sex"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row class="form-row" :gutter="16">
          <a-col :lg="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="年龄">
              <a-input-number placeholder="请输入年龄" v-decorator="['age', validatorRules.age ]" style="width: 100%"/>
            </a-form-item>
          </a-col>
          <a-col :lg='8'>
            <a-form-item
              :labelCol='labelCol'
              :wrapperCol='wrapperCol'
              label='联系电话'>
              <a-input placeholder='请输入联系电话' v-decorator="['telphone', validatorRules.telphone ]"
                        autocomplete='off' />
            </a-form-item>
          </a-col>
          <a-col :lg='8'>
            <a-form-item
              :labelCol='labelCol'
              :wrapperCol='wrapperCol'
              label='住院号'>
              <a-input placeholder="请输入住院号" v-decorator="['legalCaseNo', validatorRules.legalCaseNo ]"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row class="form-row" :gutter="16">
          <a-col :lg="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="测试类型">
              <j-dict-select-tag v-decorator="['preDiagnosis', validatorRules.preDiagnosis]" :triggerChange="true"
                                 placeholder="请输入测试类型" dictCode="pre_diagnosis"/>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="婚姻状况">
              <j-dict-select-tag v-decorator="['maritalStatus', validatorRules.maritalStatus]" :triggerChange="true"
                                 placeholder="请选择婚姻状况" dictCode="marital_status"/>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="文化程度">
              <j-dict-select-tag v-decorator="['cultural', validatorRules.cultural]" :triggerChange="true"
                                 placeholder="请选择文化程度" dictCode="cultural"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row class="form-row" :gutter="16">
          <a-col :lg="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="职业">
              <a-input placeholder="请输入职业" v-decorator="['profession', validatorRules.profession ]"/>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="被试来源">
              <j-dict-select-tag v-decorator="['testerSource', validatorRules.testerSource]" :triggerChange="true"
                                 placeholder="请输入被试来源" dictCode="tester_source"/>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="民族">
              <j-dict-select-tag v-decorator="['nationality', validatorRules.nationality]" :triggerChange="true"
                                 placeholder="请选择民族" dictCode="nationality"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :lg="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="科室">
              <a-select
                style='width: 100%'
                placeholder='请选择科室'
                show-search
                mode="SECRET_COMBOBOX_MODE_DO_NOT_USE"
                v-decorator="['departmentName', {}]">
                <a-select-option v-for='item in departmentNameList' :key='item.id' :value='item.name'>
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :lg="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="生日">
              <a-date-picker placeholder='请选择生日' style="width: 102%" v-decorator="[ 'birthday',{}]"/>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="籍贯">
              <a-input placeholder="请输入籍贯" v-decorator="['nativePlace', validatorRules.nativePlace ]"/>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import moment from 'moment';

  export default {
    name: 'PsUserModal',
    data() {
      return {
        title: '操作',
        visible: false,
        birthday: '',
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          maritalStatus: { rules: [{ required: false, message: '请输入婚否!' }] },
          cultural: { rules: [{ required: false, message: '请输入文化程度!' }] },
          profession: { rules: [{ required: false, message: '请输入职业!' }] },
          userNumber: { rules: [{ required: true, message: '请输入编号!' }] },
          name: { rules: [{ required: true, message: '请输入姓名!' }] },
          sex: { rules: [{ required: true, message: '请输入性别!' }] },
          age: { rules: [{ required: true, message: '请输入年龄!' }] },
          legalCaseNo: { rules: [{ required: false, message: '请输入住院号！' }] },
          preDiagnosis: { rules: [{ required: false, message: '请输入测试类型!' }] },
          testerSource: { rules: [{ required: false, message: '请输入被试来源!' }] },
          nationality: { rules: [{ required: false, message: '请输入民族!' }] },
          nativePlace: { rules: [{ required: false, message: '请输入籍贯!' }] },
          telphone: { rules: [{ required: false, pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号!' }] },
          birthday: { rules: [{ required: false, message: '请输入生日信息!' }] }
        },
        url: {
          add: '/psychology/psUser/add',
          edit: '/psychology/psUser/edit',
          hospitalOfficeList: '/system/sysDepartHospitalOffice/list',
          generNumber: '/psychology/psUser/generNumber',
        },
        departmentNameList: [],
      }
    },
    created() {
      this.handleSearchDepartment()
    },
    methods: {
      moment,
      add() {
        this.edit({})
      },
      edit(record) {
        console.log('record======', record)
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          console.log('1------', this.form)
          this.form.setFieldsValue(pick(this.model, 'maritalStatus', 'cultural', 'profession',
            'userNumber', 'legalCaseNo', 'name', 'sex', 'age', 'preDiagnosis', 'departmentName',
            'testerSource', 'nationality', 'nativePlace', 'telphone'))
          this.form.setFieldsValue({birthday: this.model.birthday ? moment(this.model.birthday) : null}) //时间格式化
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            //时间格式化
            formData.birthday = formData.birthday ? formData.birthday.format('YYYY-MM-DD HH:mm:ss') : null;
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      },
      handleSearchDepartment(value) {
        getAction(this.url.hospitalOfficeList, { pageNo:1, pageSize: 50 }).then((res) => {
          if (res.success) {
            this.departmentNameList = res.result.records || []
          } else {
            this.$message.error(res.message)
          }
        })
      },
      generateNumber() {
        let that = this
        that.form.resetFields()
        that.userId = ''
        getAction(that.url.generNumber, {}).then((res) => {
          if (res.success) {
            this.model.userNumber = res.result
            this.$nextTick(() => {
              this.form.setFieldsValue(pick(this.model, 'userNumber'))
            })
          } else {
            that.$message.error(res.message)
          }
        })
      },
    }
  }
</script>

<style scoped>

</style>
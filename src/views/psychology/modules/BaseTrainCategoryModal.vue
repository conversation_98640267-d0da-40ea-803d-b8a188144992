<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="分类名称">
          <a-input placeholder="请输入分类名称" v-decorator="['title', {}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="展示图">
          <j-image-upload class="avatar-uploader" bizPath="temp/image/relaxTrainImg" text="上传" v-model="titleImg"></j-image-upload>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>

  import { getAction, httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import JImageUpload from '../../../components/jeecg/JImageUpload'

  export default {
    name: 'BaseTrainModal',
    components: {
      JImageUpload
    },
    data() {
      return {
        title: '',
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        titleImg: "",
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {},
        url: {
          add: '/base/baseTrainCategory/add',
          edit: '/base/baseTrainCategory/edit',
          queryById: '/base/baseTrainCategory/queryById'
        }
      }
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'title', 'titleImg'))
          //时间格式化
        })
        if (this.model.id) {
          this.loadResultReportData(this.model.id)
          setTimeout(() => {
            this.titleImg = record.titleImg;
          }, 5)
        }
      },
      loadResultReportData(id) {
        getAction(this.url.queryById, { id: id }).then((res) => {
          if (res.success) {
            this.model = res.result
          }
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
        this.titleImg = "";
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            if(that.titleImg != ''){
              formData.titleImg = that.titleImg;
            }else{
              formData.titleImg = null;
            }
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>
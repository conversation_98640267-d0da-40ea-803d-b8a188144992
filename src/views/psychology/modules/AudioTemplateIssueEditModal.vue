<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row>
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="视频">
              <a-select placeholder="请选择视频" mode="multiple" v-decorator="['audioIds', {}]">
                <a-select-option
                  v-for="(item) in audioList"
                  :key="item.id"
                  :value="item.id">
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import JSelectDepart from '@/components/jeecgbiz/JSelectDepart'

  export default {
    name: 'PsCategoryModal',
    components: { JSelectDepart },
    data() {
      return {
        title: '操作',
        visible: false,
        model: {},
        audioTemplates: [],
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
        },
        url: {
          edit: '/vr/audioTplDistribute/edit', 
          list: '/vr/audioTplDistribute/findAudioList/',
          audioList: '/vr/audio/findAudioList'
        },
        audioList: []
      }
    },
    created() {
      this.findAudioList()
    },
    methods: {
      edit(record) {
        this.form.resetFields()
        this.visible = true
        this.model = Object.assign({}, record)
        this.getList(record.id)
      },
      findAudioList() {
        getAction(this.url.audioList).then((res) => {
          if (res.success) {
            this.audioList = res.result
          }
        })
      },
      getList(id) {
        getAction(this.url.list + id).then((res) => {
          if (res.success) {
            const data = {
              audioIds: res.result.map(item => item.id)
            }
            this.form.setFieldsValue(pick(data, 'audioIds'))
          }
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let formData = Object.assign(this.model, values)
            httpAction(this.url.edit, formData, 'put').then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      },
      addRowCustom() {
        this.model.audioIds.push({})
        this.$forceUpdate()
      },
      delRowCustom(index) {
        this.model.audioIds.splice(index, 1)
        this.$forceUpdate()
      },
    }
  }
</script>

<style scoped>

</style>
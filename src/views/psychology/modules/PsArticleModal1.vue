<template>
  <a-modal
    :title="title"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">

    <a-spin :spinning="confirmLoading">
      <a-form ref="form" :model="model" :rules="validatorRules">
        <a-row>
          <a-form-item label="类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type">
            <j-dict-select-tag v-model="model.type" placeholder="请选择素材类型" dictCode="article_type"/>
          </a-form-item>
          <a-form-item label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="title">
            <a-input v-model="model.title" placeholder="请输入标题"></a-input>
          </a-form-item>
          <a-form-item label="副标题" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="subTitle">
            <a-input v-model="model.subTitle" placeholder="请输入副标题"></a-input>
          </a-form-item>
          <a-form-item label="作者" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="author">
            <a-input v-model="model.author" placeholder="请输入作者"></a-input>
          </a-form-item>
          <a-form-item label="是否置顶" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isTop">
            <j-dict-select-tag v-model="model.isTop" placeholder="请选择是否置顶" dictCode="yn"/>
          </a-form-item>
          <a-form-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="description">
            <a-input v-model="model.description" placeholder="请输入描述"></a-input>
          </a-form-item>
          <a-form-item label="标题图(缩略图)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="videoPath">
            <j-image-upload class="avatar-uploader" bizPath="temp/image/articleTitleImg" text="上传" v-model="model.titleImg"></j-image-upload>
          </a-form-item>
          <a-form-item label="文章内容" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="content" style="min-height: 600px;" v-if="model.type == 0">
            <j-editor :j-height="600" v-model="model.content"/>
          </a-form-item>
          <a-form-item label="视频内容" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="content" v-else-if="model.type == 1">
            <j-upload v-model="model.content" :uploadParams="uploadParams" :multiple="false" :number="1" :returnUrl="true"></j-upload>
          </a-form-item>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { getAction, httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import JEditor from '@/components/jeecg/JEditor'
  import JImageUpload from '../../../components/jeecg/JImageUpload'
  import JUpload from '@/components/jeecg/JUpload'

  export default {
    name: 'PsArticleModal',
    components: {
      JImageUpload,
      JEditor,
      JUpload
    },
    data() {
      return {
        title: '操作',
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        fileList:[],
        uploadParams: {
          'biz': 'temp',
          'category': 'psArticle'
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {},
        url: {
          add: '/psychology/psArticle/add',
          edit: '/psychology/psArticle/edit',
          queryById: '/psychology/psArticle/queryById'
        }
      }
    },
    created() {
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.model = Object.assign({}, record)
        this.visible = true
        if (this.model.id) {
          this.loadResultReportData(this.model.id)
          setTimeout(() => {
            this.fileList = record.titleImg;
          }, 5)
        }
      },
      loadResultReportData(id) {
        getAction(this.url.queryById, { id: id }).then((res) => {
          if (res.success) {
            this.model = res.result
          }
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
        this.fileList=[];
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            that.confirmLoading = false
            console.log(this.model)
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })


          }
        })
      },
      handleCancel() {
        this.close()
      }
    }
  }
</script>

<style lang="less" scoped>
  .avatar-uploader > .ant-upload {
    width: 104px;
    height: 104px;
  }
</style>
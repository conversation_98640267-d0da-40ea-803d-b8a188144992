<template>
  <a-modal
    title="答题"
    :width="1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-tabs :default-active-key="1" @change="handleTabChange">
      <a-tab-pane :key="1" tab="心理评估">
        <a-spin :spinning="confirmLoading">
          <a-row>
            <a-col :span="12" :offset="1">
              <div class="search">
                <div class="input">
                  <span class="label">量表名称：</span>
                  <a-input placeholder="请输入量表" v-model="measureName"></a-input>
                </div>
                
                <div class="btn-group">
                  <a-button type='primary' @click='searchQuery(1)' icon='search'>查询</a-button>
                  <!-- <a-button type='primary' @click='searchReset(1)' icon='reload' style='margin-left: 8px'>重置</a-button> -->
                </div>
              </div>
              <!-- <a-table
                ref="table"
                size="middle"
                bordered
                rowKey="key"
                :columns="queryTreeColumns"
                :dataSource="queryTreeDataSource"
                :pagination="false"
                :loading="queryTreeLoading"
                :rowSelection="{selectedRowKeys: selectedQueryTreeRowKeys, onChange: onSelectQueryTreeChange, onSelect:onSelect, onSelectAll:onSelectAll}"
                class="j-table-force-nowrap">
              </a-table> -->
              <!-- 树-->
              <a-tree
                checkable
                @check="onCheck"
                :selectedKeys=[]
                :checkedKeys="selectedQueryTreeRowKeys"
                :treeData="queryTreeDataSource"
                :checkStrictly="true"
                :expandedKeys="iExpandedKeys"
                :autoExpandParent="autoExpandParent"
                @expand="onExpand" />
            </a-col>
            <a-col :span="2"></a-col>
            <a-col :span="9">
              <p class="title">已选量表</p>
              <a-table rowKey="key" :columns="queryTreeTable.columns" :data-source="queryTreeTable.dataSource"
                      size="small">
                <template slot="operate" slot-scope="scope">
                  <a-button type='primary' @click='deleteQueryTree(scope)'>删除</a-button>
                </template>
              </a-table>
            </a-col>
          </a-row>
        </a-spin>
      </a-tab-pane>
      <a-tab-pane :key="2" tab="量表套餐" force-render>
        <a-spin :spinning="confirmLoading">
          <a-row>
            <a-col :span="11" :offset="1">
              <!-- <div class="search">
                <div class="input">
                  <span class="label">量表名称：</span>
                  <a-input placeholder="请输入量表" v-model="name"></a-input>
                </div>
                
                <div class="btn-group">
                  <a-button type='primary' @click='searchQuery(2)' icon='search'>查询</a-button>
                  <a-button type='primary' @click='searchReset(2)' icon='reload' style='margin-left: 8px'>重置</a-button>
                </div>
              </div> -->
              <a-table
                ref="table"
                size="middle"
                bordered
                rowKey="id"
                :columns="listHomeCloumns"
                :dataSource="listHomeDataSource"
                :pagination="false"
                :loading="listHomeLoading"
                :rowSelection="{selectedRowKeys: selectedListHomeRowKeys, onChange: onSelectListHomeChange,onSelect:onSelect,onSelectAll:onSelectAll}"
                class="j-table-force-nowrap">
              </a-table>
            </a-col>
            <a-col :span="2"></a-col>
            <a-col :span="9">
              <p class="title">已选套餐</p>
              <a-table rowKey="id" :columns="listHomeTable.columns" :data-source="listHomeTable.dataSource"
                      size="small">
                <template slot="operate" slot-scope="scope">
                  <a-button type='primary' @click='deleteListHome(scope)'>删除</a-button>
                </template>
              </a-table>
            </a-col>
          </a-row>
        </a-spin>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script>

  import ARow from 'ant-design-vue/es/grid/Row'
  import ACol from 'ant-design-vue/es/grid/Col'
  import { getAction, postAction } from '@/api/manage'
  import { mixinDevice } from '@/utils/mixin.js'

  export default {
    name: 'ScSchemeForm',
    components: { ACol, ARow },
    mixins: [mixinDevice],
    props: {
      chooseData: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        visible: false,
        confirmLoading: false,
        labelCol: {
          xs: { span: 2 },
          sm: { span: 3 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        labelCol2: {
          xs: { span: 24 },
          sm: { span: 3 }
        },
        wrapperCol2: {
          xs: { span: 24 },
          sm: { span: 20 }
        },
        // 心理评估
        queryTreeTable: {
          loading: false,
          dataSource: [],
          columns: [
            {
              title: '量表名称',
              align: 'center',
              dataIndex: 'title'
            },
            {
              title: '操作',
              align: 'center',
              scopedSlots: { customRender: 'operate' }
            },
          ]
        },
        // 量表套餐
        listHomeTable: {
          loading: false,
          dataSource: [],
          columns: [
            {
              title: '量表套餐',
              align: 'center',
              dataIndex: 'name'
            },
            {
              title: '操作',
              align: 'center',
              scopedSlots: { customRender: 'operate' }
            },
          ]
        },
        url: {
          listHome: '/wx/wxAuthorizationRecords/listHome',
          queryTreeList: '/psychology/psCategory/queryTreeList',
          addForHome: '/psychology/psUser/addForHome',
          checkUnfinishedMeasure: '/psychology/psUser/checkUnfinishedMeasure',
        },
        /*心理评估*/
        queryTreeColumns: [
          {
            title: '量表名称',
            align: 'left',
            dataIndex: 'title'
          }
        ],
        queryTreeDataSource: [],
        queryTreeLoading: false,
        selectedQueryTreeRowKeys: [],
        selectionQueryTreeRows: [],

        // 量表套餐
        listHomeCloumns: [
          {
            title: '量表套餐',
            align: 'center',
            dataIndex: 'name'
          }
        ],
        listHomeDataSource: [],
        listHomeLoading: false,
        selectedListHomeRowKeys: [],
        selectionListHomeRows: [],

        measureName: '',
        name: '',
        activeKey: 1,
        isEdit: false,
        currSelectedIds: [],
        terminalId: '',
        answerType: 0,
        iExpandedKeys: [],
        autoExpandParent: true
      }
    },
    created() {
    },
    methods: {
      onExpand(expandedKeys) {
        this.iExpandedKeys = expandedKeys
        this.autoExpandParent = false
      },
      setThisExpandedKeys(node) {
        if (node.children && node.children.length > 0) {
          this.iExpandedKeys.push(node.key)
          for (let a = 0; a < node.children.length; a++) {
            this.setThisExpandedKeys(node.children[a])
          }
        }
      },
      onCheck(checkedKeys, info) {
        this.selectedQueryTreeRowKeys = checkedKeys.checked
        if (!info.checked) {
          this.queryTreeTable.dataSource = this.queryTreeTable.dataSource.filter(it => this.selectedQueryTreeRowKeys.includes(it.key))
        }
        info.checkedNodes.forEach(item => {
          const has = this.selectedQueryTreeRowKeys.filter(it => it === item.key).length
          if (has) {
            const have = this.queryTreeTable.dataSource.filter(it => it.key === item.key).length
            if (!have) {
              this.queryTreeTable.dataSource.push({
                title: item.data.props.title,
                key: item.key
              })
            }
          }
        })
      },
      handleTabChange(key) {
        this.activeKey = Number(key)
        this.loadMaterialData()
      },
      // onSelectQueryTreeChange(selectedRowKeys, selectionRows) {
      //   this.selectedQueryTreeRowKeys = selectedRowKeys
      //   this.selectionQueryTreeRows = selectionRows
      // },
      onSelectListHomeChange(selectedRowKeys, selectionRows) {
        this.selectedListHomeRowKeys = selectedRowKeys
        this.selectionListHomeRows = selectionRows
      },
      onSelect(record, selected, selectedRows) {
        if (selected) {
          this.listHomeTable.dataSource.push(record)
        } else {
          this.listHomeTable.dataSource = this.listHomeTable.dataSource.filter(item => item.id != record.id)
        }
      },
      onSelectAll(selected, selectedRows, changeRows) {
        if (selected) {
          selectedRows.forEach(record => {
            let has = this.listHomeTable.dataSource.filter(item => item.id === record.id).length
            if (!has) {
              this.listHomeTable.dataSource.push(record)
            }
          })
        } else {
          changeRows.forEach(record => {
            this.listHomeTable.dataSource = this.listHomeTable.dataSource.filter(item => item.id != record.id)
          })
        }
      },

      loadMaterialData() {
        let param = {}
        let url = ''
        if (this.activeKey === 1) {
          this.queryTreeLoading = true
          param.measureName = this.measureName
          url = this.url.queryTreeList
        } else if (this.activeKey === 2) {
          this.listHomeLoading = true
          param.isTop = 1
          url = this.url.listHome
        }
        
        getAction(url, param).then(res => {
          let { result } = res
          if (this.activeKey === 1) {
            this.queryTreeDataSource = []
            for (let i = 0; i < result.length; i++) {
              let temp = result[i]
              this.queryTreeDataSource.push(temp)
              this.setThisExpandedKeys(temp)
            }
          } else if (this.activeKey === 2) {
            this.listHomeDataSource = result
          }
        }).finally(() => {
          this.queryTreeLoading = false
          this.listHomeLoading = false
        })
      },

      async handleOk() {
        let measureIds = []
        if (this.activeKey === 1) {
          measureIds = this.selectedQueryTreeRowKeys
        } else {
          this.listHomeTable.dataSource.forEach(item => {
            measureIds = measureIds.concat(item.measureIds.split(','))
          })
        }
        const params = {
          ...this.chooseData,
          measureIds: measureIds
        }
        let res = await postAction(this.url.checkUnfinishedMeasure, params)
        if (res.success) {
          this.pushAnswerPage(params)
        } else {
          if (res.code == 10001) {
            this.$confirm({
              title: '提示',
              content: res.message,
              okText: '新增',
              cancelText: '覆盖',
              onOk() {
                params.id = null
                this.pushAnswerPage(params)
              },
              onCancel() {
                this.pushAnswerPage(params)
              }
            })
          } else {
            this.$message.error(res.message)
          }
        }
      },

      pushAnswerPage(formData) {
        let httpurl = this.url.addForHome
        formData.answerType = this.answerType
        if (this.terminalId) {
          formData.terminalId = this.terminalId
        }
        postAction(httpurl, formData).then((res) => {
          if (res.success) {
            this.$notification['success']({
              message: '添加成功',
              duration: 3,
              description: '已在选择的终端推送答题信息'
            })
            this.confirmLoading = false
            this.handleCancel()
          } else {
            this.$message.error(res.message)
          }
        })
      },

      handleCancel() {
        this.visible = false
        this.activeKey = 1
        this.measureName = ''
        this.queryTreeTable.dataSource = []
        this.queryTreeDataSource = []
        this.selectedQueryTreeRowKeys = []
        this.selectionQueryTreeRows = []

        this.listHomeTable.dataSource = []
        this.listHomeDataSource = []
        this.selectedListHomeRowKeys = []
        this.selectionListHomeRows = []
      },

      searchQuery() { // 1-心理评估 2-套餐量表
        this.loadMaterialData()
      },

      searchReset(type) { // 1-心理评估 2-套餐量表
        if (type === 1) {
          this.measureName = ''
        } else {
          this.name = ''
        }
      },

      // 删除心理评估
      deleteQueryTree(scoped) {
        this.queryTreeTable.dataSource = this.queryTreeTable.dataSource.filter(item => item.key != scoped.key)
        this.selectedQueryTreeRowKeys = this.selectedQueryTreeRowKeys.filter(item => item != scoped.key)
      },

      // 删除套餐量表
      deleteListHome(scoped) {
        this.listHomeTable.dataSource = this.listHomeTable.dataSource.filter(item => item.id != scoped.id)
        this.selectedListHomeRowKeys = this.selectedListHomeRowKeys.filter(item => item != scoped.id)
      }
    }
  }
</script>

<style lang="scss" scoped>
.title-item {
  display: flex;
  justify-content: center;
  align-items: center;

  .ant-select {
    width: 50px;
    margin: 0 8px;
  }
}

.search {
  display: flex;
  align-items: center;
  padding: 10px 0 20px 0;

  .input {
    display: inline-flex;
    align-items: center;
    width: 450px;

    .label {
      display: inline-block;
      padding-right: 15px;
    }

    .ant-input {
      width: 300px;
    }
  }
}

.j-table-force-nowrap, .ant-tree {
  height: 1000px;
  overflow: auto;
}
</style>
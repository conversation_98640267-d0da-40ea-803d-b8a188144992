<template>
  <a-modal
    :title="title"
    width="100%"
    wrapClassName="full-modal"
    @ok="close"
    @cancel="close"
    :visible="visible"
  >
    <div class="info-content">
      <div class="video-view">
        <video
          class="video-mains"
          controls
          autoplay
          preload="auto"
          :src="content"
          ref="myVideo"
          @pause="pause"
          custom-cache="false"
        >
        </video>
      </div>
    </div>
  </a-modal>
</template>

<script>
  

  export default {
    name: 'VideoInfo',
    
    data() {
      return {
        content: '',
        visible: false,
        model: {},
        title: ''
      }
    },
    created() {
    },
    destroyed() {
    },
    methods: {
      open(record) {
        this.model = Object.assign({}, record)
        this.visible = true
        this.title = this.model.title
        this.content = this.model.videoUrl
      },
      close() {
        if (this.model.type == 1) {
          this.$refs.myVideo.pause()
        }
        this.visible = false
      },
      pause(e) {
      
      },
    }
  }
</script>

<style lang="less">
  .full-modal {
    .ant-modal {
      max-width: 100%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }
    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: calc(100vh);
    }
    .ant-modal-body {
      flex: 1;
      height: calc(100% - 150px);
    }

    .info-content {
      padding: 30px 90px;
      height: 100%;

      .article-view {
        width: 100%;
        height: 100%;
        padding: 0 90px;
        overflow-y: scroll;
      }
      .video-view {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
</style>
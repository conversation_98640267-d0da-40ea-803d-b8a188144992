<template>
  <a-modal
    :title="title"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row>
          <a-col :span="24">
            <a-form-item label="类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type">
              <j-dict-select-tag v-model="model.type" placeholder="请选择素材类型" dictCode="material_type" :disabled="formDisabled"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="title">
              <a-input v-model="model.title" placeholder="请输入标题" :disabled="formDisabled"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="副标题" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="subTitle">
              <a-input v-model="model.subTitle" placeholder="请输入副标题" :disabled="formDisabled"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="素材分类" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="materialCategoryId">
              <j-dict-select-tag v-model="model.materialCategoryId" placeholder="请选择素材分类"
                                 dictCode="base_material_category,name,id" :disabled="formDisabled"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="描述" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="description">
              <a-input v-model="model.description" placeholder="请输入描述" :disabled="formDisabled"></a-input>
            </a-form-item>
          </a-col>
          <a-col v-if="model.type == 2" :span="24">
            <a-form-item label="视频地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="videoPath">
              <a-textarea row="5" v-model="model.videoPath" placeholder="请输入视频地址" :disabled="formDisabled"></a-textarea>
            </a-form-item>
          </a-col>
          <a-col v-if="model.type == 1" :span="24">
            <a-form-item label="文章内容" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="content" style="min-height: 600px;">
              <j-editor :j-height="600" v-model="model.content" :disabled="formDisabled"/>
            </a-form-item>
          </a-col>
          <a-col v-if="model.type == 6" :span="24">
            <a-form-item label="引导页选择" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="content">
              <j-dict-select-tag v-model="model.content" placeholder="请选择引导页" dictCode="introductory_type"/>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { getAction, httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import JEditor from '@/components/jeecg/JEditor'
  export default {
    name: 'BaseMaterialModal',
    components: {
      JEditor
    },
    data () {
      return {
        title:'',
        width:1200,
        visible: false,
        disableSubmit: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        model: {},
        validatorRules: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        url: {
          add: '/base/baseMaterial/add',
          edit: '/base/baseMaterial/edit',
          queryById: '/base/baseMaterial/queryById'
        }
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      }
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.model = Object.assign({}, record)
        this.visible = true
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'delFlag'))
          //时间格式化
        })
      },
      loadResultReportData(id) {
        getAction(this.url.queryById, { id: id }).then((res) => {
          if (res.success) {
            this.model = res.result
          }
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            that.confirmLoading = false
            console.log(this.model)
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })


          }
        })
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
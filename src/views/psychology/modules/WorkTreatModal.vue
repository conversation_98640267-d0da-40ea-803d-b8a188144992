<template>
  <a-modal
    :title="title"
    :width="1000"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    :okButtonProps="{ props: {disabled: disableSubmit} }"
    @ok="handleOk"
    @cancel="handleCancel">
    <!-- 主表单区域 -->
    <a-form :form="form">
      <a-row>
        <a-col :span="24" :gutter="8">
          <a-form-item
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            label="作业名称">
            <a-input placeholder="请输入作业名称" v-decorator="['fileName', {rules: [{required: true, message: '请输入作业名称'}]}]" :disabled="disableSubmit" />
          </a-form-item>
          <a-form-item 
            label="上传" 
            :labelCol="labelCol" 
            :wrapperCol="wrapperCol">
            <a-upload
              v-decorator="['file', {rules: [{required: true, message: '请上传文件'}]}]"
              :fileList="fileList"
              name="file"
              class="file-uploader"
              accept=".pdf, .doc, .docx, .xls, .xlsx"
              :before-upload="beforeUpload"
              :headers="tokenHeader"
              isMultiple="false"
              :data="{biz: 'temp/files'}"
              :action="importExcelUrl"
              :disabled="disableSubmit"
              @change="handleChange"
            >
              <a-button> <a-icon type="upload" /> 上传 </a-button>
            </a-upload>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>
<script>

  import pick from 'lodash.pick'
  import { getAction, httpAction } from '@/api/manage'
  import Vue from 'vue'
  import {ACCESS_TOKEN, USER_INFO} from '@/store/mutation-types'

  export default {
    name: 'VideoTemplateModal',
    data() {
      return {
        title: '量表单价设置',
        visible: false,
        validatorRules: {},
        confirmLoading: false,
        disableSubmit: false,
        videoList: [],
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        model: {
        },
        form: this.$form.createForm(this),
        url: {
          add: '/ps/homeworkFile/save',
          edit: '/ps/homeworkFile/edit',
        },
        tokenHeader: {'X-Access-Token': Vue.ls.get(ACCESS_TOKEN)},
        importExcelUrl:`${window._CONFIG['domianURL']}/sys/common/upload`,
        filePath: null,
        fileList: [],
      }
    },
    methods: {
      add() {
        this.edit({})
      },
      edit(record) {
        this.form.resetFields()
        this.visible = true
        this.model = Object.assign({}, record)
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model, 'fileName'))
          this.fileList = []
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
        this.form.resetFields()
        this.fileList = []
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      },

      handleChange(info) {
        this.fileList = [info.file]
        if (info.file.status === 'done') {
          if(info.file.response.success){
            this.model.fileUrl = `${window._CONFIG['domianURL']}/sys/common/static/${info.file.response.message}`
            this.$message.success(`${info.file.name} 文件上传成功`);
          } else {
            this.$message.error(`${info.file.name} ${info.file.response.message}.`);
          }
        } else if (info.file.status === 'error') {
          this.$message.error(`文件上传失败: ${info.file.msg} `);
        }
      },

      beforeUpload(file) {
        const fileName = file.name.toLowerCase();
        const isPdf = fileName.endsWith('.pdf');
        const isExcel = fileName.endsWith('.xls') || fileName.endsWith('.xlsx');
        const isWord = fileName.endsWith('.doc') || fileName.endsWith('.docx');
        const isValidFileType = isPdf || isExcel || isWord;
        if (!isValidFileType) {
          this.$message.error('只要上传pdf、excel、wrod文件!')
        }
        const isLt100M = file.size / 1024 / 1024 < 100
        if (!isLt100M) {
          this.$message.error('文件大小需小于100MB!')
        }
        return isValidFileType && isLt100M
      },
    }
  }
</script>

<style scoped>
  .ant-btn {
    padding: 0 10px;
    margin-left: 3px;
  }

  .ant-form-item-control {
    line-height: 0px;
  }

  /** 主表单行间距 */
  .ant-form .ant-form-item {
    margin-bottom: 10px;
  }

  /** Tab页面行间距 */
  .ant-tabs-content .ant-form-item {
    margin-bottom: 0px;
  }
</style>
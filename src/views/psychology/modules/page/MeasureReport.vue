<template>
  <div>
    <a-spin :spinning="loading">
      <div class='no-print' style='text-align: right;margin-right: 35px;'>
        <a-button v-print="'#ReportCommonForm'" ghost type='primary' style='margin-right: 10px;'>打印</a-button>
      </div>
      <div id="ReportCommonForm">
        <p v-html='datasHeader'></p>
        <line-hv-chart-multid title="" :dataSource="dataSource" :height="550" />
        <p v-html='datasFooter'></p>
      </div>
    </a-spin>
  </div>
</template>
<script>

import { getAction } from '@/api/manage'
import LineHvChartMultid from '@/components/chart/LineHvChartMultid'

export default {
  name: 'MeasureReport',
  components: {
    LineHvChartMultid
  },
  data() {
    return {
      description: '心理趋势',
      loading: false,
      userId: '',
      datasHeader: '',
      datasFooter: '',
      dataSource: [],
      // 请求参数
      url: {
        userResultReport: '/psychology/psUser/userResultReport',
        generatePrintHtmlHeader: '/train/cogTrainReport/generatePrintHtmlHeader'
      }
    }
  },
  created() {
  },
  methods: {
    onload(id) {
      this.userId = id
      this.loadData()
      this.generatePrintHtml()
    },
    generatePrintHtml(ids) {
      this.loading = true
      getAction(this.url.generatePrintHtmlHeader, { userId: this.userId }).then((res) => {
        if (res.success) {
          // this.datas = res.result
          this.datasHeader = res.result.header
          this.datasFooter = res.result.footer
          this.loading = false
        }
      })
    },
    loadData() {
      this.loading = true
      getAction(this.url.userResultReport, { 'psUserId': this.userId }).then((res) => {
        if (res.success) {
          this.dataSource = res.result
        }
        this.loading = false
      })
    }
  }
}
</script>
<style lang="less" scoped>

</style>
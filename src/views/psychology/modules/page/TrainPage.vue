<template>
  <div>
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="训练分类">
              <a-select v-model="queryParam.trainType" size="small" placeholder="请选择训练分类">
                <a-select-option
                  value="plane">
                  平面游戏
                </a-select-option>
                <a-select-option
                  value="vr">
                  VR游戏
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <a-table
      ref="table"
      size="middle"
      bordered
      rowKey="id"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      :loading="loading"
      :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
      @change="handleTableChange">

      <span slot="action" slot-scope="text, record">
        <a v-if="record.status == '0'" @click="continueTrain(record)">继续训练</a>
        <a v-if="record.status == '1'" @click="handlePrintForm(record)">报告预览</a>
      </span>

    </a-table>
    <!-- table区域-end -->
    <!--  报告显示  -->
    <train-report-common-view ref="TrainReportCommonView"></train-report-common-view>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@api/manage'
import TrainReportCommonView from '@views/train/modules/TrainReportCommonView'

export default {
  name: 'TrainPage',
  mixins: [JeecgListMixin],
  components: {
    TrainReportCommonView
  },
  data() {
    return {
      description: '训练结果管理页面',
      userId: '',
      // 表头
      columns: [
        {
          title: '测试者名称',
          align: 'center',
          dataIndex: 'userName'
        },
        {
          title: '训练分类',
          align: 'center',
          dataIndex: 'trainType',
          customRender: function(text) {
            return text === 'vr' ? 'VR游戏' : '平面游戏'
          }
        },
        {
          title: '训练名称',
          align: 'center',
          dataIndex: 'trainName'
        },
        {
          title: '等级',
          align: 'center',
          dataIndex: 'grade'
        },
        {
          title: '训练状态',
          align: 'center',
          dataIndex: 'status_dictText'
        },
        {
          title: '测试时间',
          align: 'center',
          dataIndex: 'time'
        },
        {
          title: '总分',
          align: 'center',
          dataIndex: 'totalPoints'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/train/cogTrainResult/list',
        continueTrain: '/psychology/psUser/continueTrain'
      }
    }
  },
  methods: {
    onload(id) {
      this.userId = id
      this.loadData()
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams()//查询条件
      if (!this.userId) {
        return
      }
      params.userId = this.userId
      delete params.createTimeRange // 时间参数不传递后台
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records
          this.ipagination.total = res.result.total
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    continueTrain(record) {
      let that = this
      let httpurl = that.url.continueTrain
      let formData = {}
      formData.id = record.id
      formData.trainId = record.trainId
      getAction(httpurl, formData).then((res) => {
        if (res.success) {
          this.$notification['success']({
            message: '添加成功',
            duration: 3,
            description: '已在选择的终端推送答题信息'
          })
        } else {
          that.$message.warning(res.message)
        }
      }).finally(() => {
      })
    },
    handlePrintForm(record) {
      if (record.status != '1') {
        this.$message.error('未完成训练不能预览！')
        return
      }
      this.$refs.TrainReportCommonView.edit(record)
      this.$refs.TrainReportCommonView.title = '内容预览'
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
<template>
  <div>
    <a-spin :spinning='downLoading' tip='下载中...'>
      <!-- 操作按钮区域 -->
      <div class='table-operator'>
        <a-button type='primary' icon='printer' @click='batchPrint'>批量打印</a-button>
        <a-button type='primary' icon='arrow-down' @click="batchExportDoc('批量下载报告')">批量下载报告</a-button>
        <a-button v-if="!hasRole3" type='primary' icon='arrow-up' @click="handleExportXls('测评结果')">测评数据导出</a-button>
        <a-button type='primary' @click='searchReset' icon='reload' style='margin-left: 8px'>刷新</a-button>
      </div>
      <!-- table区域-begin -->
      <div>
        <div class='ant-alert ant-alert-info' style='margin-bottom: 16px;'>
          <i class='anticon anticon-info-circle ant-alert-icon'></i>
          <span>已选择</span>
          <a style='font-weight: 600'>
            {{ selectedRowKeys.length }}
          </a>
          <span>项</span>
          <a style='margin-left: 24px' @click='onClearSelected'>清空</a>
        </div>

        <a-table
          ref='table'
          size='middle'
          bordered
          rowKey='id'
          :columns='columns'
          :dataSource='dataSource'
          :pagination='ipagination'
          :loading='loading'
          :rowSelection='{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}'
          @change='handleTableChange'>
            <span slot='degree' slot-scope='text'>
              <a-badge :color='text | statusTypeFilter' :text='text | statusFilter' />
            </span>
            <span slot='action' slot-scope='text, record'>
              <a @click='handlePrintForm(record)'>内容预览</a>
              <a-divider v-if="isAssessType(record)" type='vertical' />
              <a v-if="record.status == '2' && isAssessType(record)" @click='handleExportDoc(record)'>报告下载</a>
              <a v-if="record.status != '2' && isAssessType(record)" @click='continueQuestions(record)'>继续答题</a>
              <template v-if="!hasRole3 && isAssessType(record)">
                <a-divider type='vertical' />
                <a-dropdown>
                  <a class='ant-dropdown-link'>更多 <a-icon type='down' /></a>
                  <a-menu slot='overlay'>
                    <a-menu-item>
                      <a @click='handleView(record)'>查看</a>
                    </a-menu-item>
                    <a-menu-item>
                      <a @click='handleEdit(record)'>编辑</a>
                    </a-menu-item>
                    <a-menu-item v-if='record.signImgPath'>
                      <a @click='downUserSign(record)'>下载签名</a>
                    </a-menu-item>
                    <a-menu-item>
                      <a @click='exportExcelOption(record)'>导出答题详情</a>
                    </a-menu-item>
                  </a-menu>
                </a-dropdown>
              </template>
            </span>
        </a-table>
      </div>
      <!-- table区域-end -->
      <!-- 表单区域 -->
      <dxResult-modal ref='modalForm' @ok='modalFormOk' />
      <!-- 打印表单页 -->
      <report-common-form ref='ReportForm'></report-common-form>
      <!-- 眼动筛查报告 -->
      <dx-em-report-model ref="emReportFrom"></dx-em-report-model>
    </a-spin>
  </div>
</template>
<script>
import Vue from 'vue'
import { USER_ROLE } from '@/store/mutation-types'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { downFile, getAction } from '@/api/manage'
import ReportCommonForm from '@views/dashboard/ReportCommonForm'
import DxResultModal from '@views/diagnosis/modules/DxResultModal'
import DxEmReportModel from '@views/diagnosis/modules/DxEmReportModel'
import {globalShowedAuth} from '@/utils/authFilter'

const degreeMap = {
  0: {
    color: '#D9D9D9',
    text: '未答完'
  },
  1: {
    color: 'green',
    text: '正常'
  },
  2: {
    color: 'yellow',
    text: '轻度'
  },
  3: {
    color: 'red',
    text: '中度'
  },
  4: {
    color: 'purple',
    text: '重度'
  },
  5: {
    color: '#D9D9D9',
    text: '未知'
  }
}

export default {
  name: 'Result',
  mixins: [JeecgListMixin],
  components: {
    ReportCommonForm,
    DxResultModal,
    DxEmReportModel
  },
  data() {
    return {
      description: '测评结果管理页面',
      downLoading: false,
      userId: '',
      /* 排序参数 */
      isorter: {
        column: 'updateTime',
        order: 'desc'
      },
      // 表头
      columns: [
        {
          title: '量表',
          align: 'center',
          dataIndex: 'measureName'
        },
        {
          title: '总粗分',
          align: 'center',
          dataIndex: 'totalPoints'
        },
        {
          title: '测评用时',
          align: 'center',
          dataIndex: 'timeStr'
        },
        {
          title: '答题状态',
          align: 'center',
          dataIndex: 'status_dictText'
        },
        {
          title: '预警结果',
          align: 'center',
          dataIndex: 'degree',
          scopedSlots: { customRender: 'degree' }
        },
        {
          title: '修改时间',
          align: 'center',
          dataIndex: 'updateTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 请求参数
      url: {
        list: '/diagnosis/dxResult/list',
        delete: '/diagnosis/dxResult/delete',
        deleteBatch: '/diagnosis/dxResult/deleteBatch',
        exportXlsUrl: 'diagnosis/dxResult/exportXls',
        batchExportDoc: 'diagnosis/dxResult/batchExportDoc',
        importExcelUrl: 'diagnosis/dxResult/importExcel',
        treeList: '/psychology/psCategory/queryTreeSelectList',
        exportDocUrl: 'diagnosis/dxResult/exportSingleDoc',
        goContinueQuestionPage: '/psychology/psUser/goContinueQuestionPage',
        exportOptionXls: 'diagnosis/dxResult/exportOptionXls'
      },
      hasRole1: Vue.ls.get(USER_ROLE, []).includes('myyywswbi'),
      hasRole3: Vue.ls.get(USER_ROLE, []).includes('myyyts'),
    }
  },
  filters: {
    statusFilter(type) {
      return degreeMap[type].text
    },
    statusTypeFilter(type) {
      return degreeMap[type].color
    }
  },
  created() {
    if (this.hasRole3) {
      this.columns = this.columns.filter(item => item.dataIndex !== 'timeStr' && item.dataIndex !== 'updateTime')
    }

    if (!this.isShowAuth("psUsesrDetail:resultList:timeStr")) {
      this.columns = this.columns.filter(item => item.dataIndex !== 'timeStr')
    }

    if (!this.isShowAuth("psUsesrDetail:resultList:updateTime")) {
      this.columns = this.columns.filter(item => item.dataIndex !== 'updateTime')
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    onload(id) {
      this.userId = id
      this.loadData()
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams()//查询条件
      if (!this.userId) {
        return
      }
      params.userId = this.userId
      delete params.createTimeRange // 时间参数不传递后台
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          this.dataSource = res.result.records
          this.ipagination.total = res.result.total
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
    handleAdd: function() {
      this.$refs.modalForm.add(this.$route.params.id)
      this.$refs.modalForm.title = '新增'
    },
    handleExportDoc(record) {
      if (record.status != '2') {
        this.$message.error('未答完题目不能查看报告！')
        return
      }
      this.downLoading = true
      var fileName = record.userName + '_' + record.measureName + '.pdf'
      let param = { ...this.queryParam }
      delete param.createTimeRange // 时间参数不传递后台
      param.ids = record.id
      downFile(this.url.exportDocUrl, param).then((data) => {
        if (!data) {
          this.$message.warning('文件下载失败')
          this.downLoading = false
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), fileName)
          this.downLoading = false
        } else {
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(url) //释放掉blob对象
          this.downLoading = false
        }
      })
    },
    exportExcelOption(record) {
      if (record.status != '2') {
        this.$message.error('未答完题目不能导出答题数据！')
        return
      }
      let url = `${window._CONFIG['domianURL']}/${this.url.exportOptionXls}?id=` + record.id
      window.location.href = url
    },
    continueQuestions(record) {
      let that = this
      let httpurl = that.url.goContinueQuestionPage
      let formData = {}
      formData.resultId = record.id
      getAction(httpurl, formData).then((res) => {
        if (res.success) {
          this.$notification['success']({
            message: '添加成功',
            duration: 3,
            description: '已在选择的终端推送答题信息'
          })
        } else {
          that.$message.warning(res.message)
        }
      }).finally(() => {
      })
    },
    isShowAuth(code) {
      return globalShowedAuth(code)
    },
    handlePrintForm(record) {
      if (record.status != '2') {
        this.$message.error('未答完题目不能预览！')
        return
      }
      // 眼动筛查报告
      if (record.assessType === 'em') {
        this.$refs.emReportFrom.edit(record)
        this.$refs.emReportFrom.title = '内容预览'
      } else {
        this.$refs.ReportForm.edit(record)
        this.$refs.ReportForm.title = '内容预览'
      }
    },
    batchPrint: function() {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择至少一条记录！')
        return
      } else if (this.selectedRowKeys.length > 30) {
        this.$message.warning('请不要选择超过三十条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        this.$refs.ReportForm.batchPrint(ids)
        this.$refs.ReportForm.title = '批量打印'
      }
    },
    handleExportXls(fileName) {
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      if(!fileName || typeof fileName != "string"){
        fileName = "导出文件"
      }
      let param = {...this.queryParam};
      delete param.createTimeRange; // 时间参数不传递后台
      if(this.selectedRowKeys && this.selectedRowKeys.length>0){
        param['selections'] = this.selectedRowKeys.join(",")
      }
      downFile(this.url.exportXlsUrl,param).then((data)=>{
        if (!data) {
          this.$message.warning("文件下载失败")
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), fileName+'.xls')
        }else{
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName+'.xls')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link); //下载完成移除元素
          window.URL.revokeObjectURL(url); //释放掉blob对象
        }
      })
    },
    batchExportDoc(fileName) {
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.downLoading = true
      if (!fileName || typeof fileName != 'string') {
        fileName = '导出文件'
      }
      let param = { ...this.queryParam }
      delete param.createTimeRange // 时间参数不传递后台
      if (this.selectedRowKeys && this.selectedRowKeys.length > 0) {
        param['ids'] = this.selectedRowKeys.join(',')
      }
      downFile(this.url.batchExportDoc, param).then((data) => {
        if (!data) {
          this.$message.warning('文件下载失败')
          this.downLoading = false
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), fileName + '.zip')
          this.downLoading = false
        } else {
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName + '.zip')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(url) //释放掉blob对象
          this.downLoading = false
        }
      })
    },
    handleView: function(record) {
      this.$refs.modalForm.view(record)
      this.$refs.modalForm.title = '查看'
      this.$refs.modalForm.disableSubmit = true
    },
    onDateChange: function(value, dateString) {
      this.queryParam.startTime = dateString[0]
      this.queryParam.endTime = dateString[1]
    },
    downUserSign(record) {
      let fileName = '测试者签名'
      let url = record.signImgPath
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', fileName + '.png')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link) //下载完成移除元素
      window.URL.revokeObjectURL(url) //释放掉blob对象
    },
    isAssessType(record) {
      if (!record.assessType || record.assessType === 'scale') {
        return true
      }
      return false
    }
  }
}
</script>
<style lang='less' scoped>
/** Button按钮间距 */
.ant-btn {
  margin-left: 3px
}

.ant-card-body .table-operator {
  margin-bottom: 18px;
}

.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

.anty-row-operator button {
  margin: 0 5px
}

.ant-btn-danger {
  background-color: #ffffff
}

.ant-modal-cust-warp {
  height: 100%
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden
}
</style>
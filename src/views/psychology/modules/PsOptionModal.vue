<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    :okButtonProps="{ props: {disabled: disableSubmit} }"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">

    <!-- 编辑 -->
    <a-spin :spinning="confirmLoading" v-if="editStatus">
      <a-form :form="form">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="标题">
          <a-input placeholder="标题" v-decorator="['title', {rules: [{ required: true, message: '请输入标题!' }]}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="标题类型">
          <j-dict-select-tag @change="handleChangeTitleType"
                             v-decorator="['type', { rules: [{ required: true, message: '请选择选项类型!' }] }]"
                             :triggerChange="true" placeholder="请选择标题类型" dictCode="quesion_type"/>
        </a-form-item>
        <a-form-item
          v-if="model.type == 1"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="选项内容">
          <a-input placeholder="请输入选项内容"
                   v-decorator="['content', { rules: [{ required: true, message: '请输入选项内容!' }] } ]"/>
        </a-form-item>
        <a-form-item
          v-if="model.type == 2"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="选项内容">
          <j-image-upload class="avatar-uploader" text="上传" v-model="fileList"></j-image-upload>
          <br/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="得分">
          <a-input placeholder="得分" v-decorator="['score', {rules: [{ required: true, message: '请输入分数!' }]}]"/>
        </a-form-item>
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="排序">
          <a-input placeholder="排序" v-decorator="['sort', {rules: [{ required: true, message: '请输入排序!' }]}]"/>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import Vue from 'vue'
  import { ACCESS_TOKEN } from '@/store/mutation-types'
  import JDictSelectTag from '@/components/dict/JDictSelectTag.vue'
  import JImageUpload from '@/components/jeecg/JImageUpload.vue'

  export default {
    name: 'PsOptionModal',
    components: {
      JDictSelectTag,
      JImageUpload
    },
    data() {
      return {
        title: '操作',
        visible: false,
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        fileList: [],
        disableSubmit: false,
        selectedRowKeys: [],
        quesionId: '',
        hiding: false,
        headers: {},
        picUrl: '',
        picArray: [],
        previewVisible: false,
        previewImage: '',
        addStatus: false,
        editStatus: false,
        confirmLoading: false,
        form: this.$form.createForm(this),
        url: {
          add: '/psychology/psOption/add',
          edit: '/psychology/psOption/edit',
          imgerver: window._CONFIG['domianURL'],
          fileUpload: window._CONFIG['domianURL'] + '/sys/common/upload',
          getOrderCustomerList: '/psychology/psOption/list'
        }
      }
    },
    computed: {},
    created() {
      const token = Vue.ls.get(ACCESS_TOKEN)
      this.headers = { 'X-Access-Token': token }
    },
    methods: {
      add(quesionId) {
        this.hiding = true
        if (quesionId) {
          this.quesionId = quesionId
          this.edit({ quesionId: quesionId }, '')
        } else {
          this.$message.warning('请选择一个问题信息')
        }
      },
      detail(record) {
        this.edit(record, 'd')
      },
      edit(record, v) {
        this.hiding = true
        this.disableSubmit = false
        this.form.resetFields()
        this.quesionId = record.quesionId
        this.model = Object.assign({}, record)
        if (record.id) {
          this.hiding = false
          this.addStatus = false
          this.editStatus = true
          this.$nextTick(() => {
            this.form.setFieldsValue(pick(this.model, 'title', 'type', 'content', 'score', 'sort'))
          })
          setTimeout(() => {
            this.fileList = record.content
          }, 5)
        } else {
          this.addStatus = false
          this.editStatus = true
          this.model.type = '1'
        }
        this.visible = true
      },
      close() {
        this.$emit('close')
        this.visible = false
        this.picUrl = ''
        this.fileList = []
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!this.model.id) {
              httpurl += this.url.add
              method = 'post'
            } else {
              httpurl += this.url.edit
              method = 'put'
            }
            let formData = Object.assign(this.model, values)
            formData.quesionId = this.quesionId
            if (this.model.type == 2) {
              formData.content = this.fileList
            }
            httpAction(httpurl, formData, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      },
      handleChangeTitleType(e) {
        this.model.type = e
      }
    }
  }
</script>

<style scoped>
  /* tile uploaded pictures */
  .upload-list-inline > > > .ant-upload-list-item {
    float: left;
    width: 200px;
    margin-right: 8px;
  }

  .upload-list-inline > > > .ant-upload-animate-enter {
    animation-name: uploadAnimateInlineIn;
  }

  .upload-list-inline > > > .ant-upload-animate-leave {
    animation-name: uploadAnimateInlineOut;
  }
</style>
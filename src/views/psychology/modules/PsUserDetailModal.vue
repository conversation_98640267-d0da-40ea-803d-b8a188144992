<template>
  <div class='page-header-index-wide page-header-wrapper-grid-content-main'>
    <a-row :gutter='24'>
      <a-col :md='24' :lg='7'>
        <a-card :bordered='false'>
          <div class='account-center-avatarHolder'>
            <div class='avatar'>
              <img :src='psUserInfo.headUrl' />
            </div>
            <div class='username'>{{ this.psUserInfo.name }}</div>
          </div>
          <div class='account-center-detail'>
            <p>
              <a-icon type='user' />
              编号：{{ this.psUserInfo.userNumber }}
            </p>
            <p>
              <a-icon type='man' />
              性别：{{ this.psUserInfo.sex }}
            </p>
            <p>
              <a-icon type='unordered-list' />
              年龄：{{ this.psUserInfo.age }} 岁
            </p>
            <p>
              <a-icon type='mobile' />
              联系电话：{{ this.psUserInfo.telphone }}
            </p>
            <p>
              <a-icon type='contacts' />
              文化程度：{{ this.psUserInfo.cultural }}
            </p>
            <p>
              <a-icon type='project' />
              婚姻状态：{{ this.psUserInfo.maritalStatus }}
            </p>
            <p>
              <a-icon type='idcard' />
              职业：{{ this.psUserInfo.profession }}
            </p>
          </div>
        </a-card>
      </a-col>
      <a-col :md='24' :lg='17'>
        <a-card
          style='width:100%'
          :bordered='false'
          :tabList='tabListNoTitle'
          :activeTabKey='noTitleKey'
          @tabChange="key => handleTabChange(key, 'noTitleKey')"
        >
          <result-page ref='ResultPage' v-show="noTitleKey === 'Result'"></result-page>
          <measure-report ref='MeasureReport' v-show="noTitleKey === 'MeasureReport'"></measure-report>
          <train-page ref='TrainPage' v-show="noTitleKey === 'Train'"></train-page>
          <report-common-form ref='ReportForm' v-show="noTitleKey === 'PersonalReport'"></report-common-form>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import PageLayout from '@/components/page/PageLayout'
import RouteView from '@/components/layouts/RouteView'
import ReportCommonForm from './TrainReportCommonForm.vue'
import { MeasureReport, ResultPage, TrainPage } from './page'
import { getAction } from '@api/manage'
import { globalShowedAuth } from '@/utils/authFilter'

export default {
  name: 'PsUserDetailModal',
  components: {
    RouteView,
    PageLayout,
    ResultPage,
    MeasureReport,
    TrainPage,
    ReportCommonForm
  },
  watch: {
    $route() {
      if (this.$route.params.id) {
        this.loadData()
        this.$refs.ResultPage.onClearSelected()
        this.$refs.TrainPage.onClearSelected()
      }
    }
  },
  data() {
    return {
      psUserId: '',
      psUserInfo: {},
      tagInputVisible: false,
      tagInputValue: '',
      teams: [],
      teamSpinning: true,
      tabListNoTitle: [],
      noTitleKey: 'Result',
      url: {
        queryByIdAllInfo: '/psychology/psUser/queryByIdAllInfo'
      }
    }
  },
  mounted() {
    this.loadData()
    this.isShowAuth('home:list:cognize:action')
  },
  methods: {
    loadData() {
      this.psUserId = this.$route.params.id
      //获取个人信息
      this.getUserInfo()
    },
    getUserInfo() {
      getAction(`${this.url.queryByIdAllInfo}?id=${this.psUserId}`).then((res) => {
        if (res.success) {
          this.psUserInfo = res.result
          this.$refs.ResultPage.onload(this.psUserId)
          this.$refs.TrainPage.onload(this.psUserId)
          if (!this.psUserInfo.headUrl) {
            if (this.psUserInfo.sex == '男') {
              this.psUserInfo.headUrl = '/avatar_1.png'
            } else {
              this.psUserInfo.headUrl = '/avatar_2.png'
            }
          }
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleTabChange(key, type) {
      this[type] = key
      if (key == 'Result') {
        this.$refs.ResultPage.onload(this.psUserId)
      }
      if (key == 'MeasureReport') {
        this.$refs.MeasureReport.onload(this.psUserId)
      }
      if (key == 'Train') {
        this.$refs.TrainPage.onload(this.psUserId)
      }
      if (key == 'PersonalReport') {
        this.$refs.ReportForm.edit(this.psUserId)
        this.$refs.ReportForm.title = '内容预览'
      }
    },
    isShowAuth(code) {
      let showAuth = globalShowedAuth(code)
      if (showAuth) {
        this.tabListNoTitle = [
          {
            key: 'Result',
            tab: '测试记录'
          },
          {
            key: 'Train',
            tab: '训练记录'
          },
          {
            key: 'MeasureReport',
            tab: '心理趋势图'
          },
          {
            key: 'PersonalReport',
            tab: '个人认知报告'
          }
        ]
      } else {
        this.tabListNoTitle = [
          {
            key: 'Result',
            tab: '测试记录'
          },
          {
            key: 'MeasureReport',
            tab: '心理趋势图'
          }
        ]
      }
      return showAuth
    },
    close() {
      this.$emit('close')
      this.userId = ''
    }
  }
}
</script>

<style lang='scss' scoped>
.page-header-wrapper-grid-content-main {
  width: 100%;
  height: 100%;
  min-height: 100%;
  transition: .3s;

  .account-center-avatarHolder {
    text-align: center;
    margin-bottom: 24px;

    & > .avatar {
      margin: 0 auto;
      width: 104px;
      height: 104px;
      margin-bottom: 20px;
      border-radius: 50%;
      overflow: hidden;

      img {
        height: 100%;
        width: 100%;
      }
    }

    .username {
      color: rgba(0, 0, 0, 0.85);
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
      margin-bottom: 4px;
    }
  }

  .account-center-detail {

    p {
      margin-bottom: 8px;
      padding-left: 26px;
      position: relative;
    }

    i {
      position: absolute;
      height: 14px;
      width: 14px;
      left: 0;
      top: 4px;
    }

    .title {
      background-position: 0 0;
    }

    .group {
      background-position: 0 -22px;
    }

    .address {
      background-position: 0 -44px;
    }
  }

  .account-center-tags {
    .ant-tag {
      margin-bottom: 8px;
    }
  }

  .account-center-team {

    .members {
      a {
        display: block;
        margin: 12px 0;
        line-height: 24px;
        height: 24px;

        .member {
          font-size: 14px;
          color: rgba(0, 0, 0, .65);
          line-height: 24px;
          max-width: 100px;
          vertical-align: top;
          margin-left: 12px;
          transition: all 0.3s;
          display: inline-block;
        }

        &:hover {
          span {
            color: #1890ff;
          }
        }
      }
    }
  }

  .tagsTitle, .teamTitle {
    font-weight: 500;
    color: rgba(0, 0, 0, .85);
    margin-bottom: 12px;
  }

}

</style>
<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row>
          <a-col :span="24" :gutter="8">
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="视频">
              <a-select placeholder="请选择视频" mode="multiple" v-decorator="['videoIds', {}]">
                <a-select-option
                  v-for="(item) in videoList"
                  :key="item.id"
                  :value="item.id">
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import JSelectDepart from '@/components/jeecgbiz/JSelectDepart'

  export default {
    name: 'PsCategoryModal',
    components: { JSelectDepart },
    data() {
      return {
        title: '操作',
        visible: false,
        model: {},
        videoTemplates: [],
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 }
        },
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
        },
        url: {
          edit: '/vr/videoTplDistribute/edit', 
          list: '/vr/videoTplDistribute/findVideoList/',
          videoList: '/vr/video/findVideoList'
        },
        videoList: []
      }
    },
    created() {
      this.findVideoList()
    },
    methods: {
      edit(record) {
        this.form.resetFields()
        this.visible = true
        this.model = Object.assign({}, record)
        this.getList(record.id)
      },
      findVideoList() {
        getAction(this.url.videoList).then((res) => {
          if (res.success) {
            this.videoList = res.result
          }
        })
      },
      getList(id) {
        getAction(this.url.list + id).then((res) => {
          if (res.success) {
            const data = {
              videoIds: res.result.map(item => item.id)
            }
            this.form.setFieldsValue(pick(data, 'videoIds'))
          }
        })
      },
      close() {
        this.$emit('close')
        this.visible = false
      },
      handleOk() {
        const that = this
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true
            let formData = Object.assign(this.model, values)
            httpAction(this.url.edit, formData, 'put').then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            }).finally(() => {
              that.confirmLoading = false
              that.close()
            })
          }
        })
      },
      handleCancel() {
        this.close()
      },
      addRowCustom() {
        this.model.videoIds.push({})
        this.$forceUpdate()
      },
      delRowCustom(index) {
        this.model.videoIds.splice(index, 1)
        this.$forceUpdate()
      },
    }
  }
</script>

<style scoped>

</style>
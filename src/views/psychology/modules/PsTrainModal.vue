<template>
  <a-modal
    :title='title'
    :width='800'
    :visible='visible'
    :confirmLoading='confirmLoading'
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <a-form :form='form'>
        <a-form-item label='训练套餐名称' :labelCol='labelCol' :wrapperCol='wrapperCol'>
          <a-select
            show-search
            filterOption
            style='width:100%'
            :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
            v-decorator="['cogTrainPackageTemplateId', {}]"
            placeholder='请选择训练套餐'>
            <a-select-option v-for="item in trainTemplateList" :key="item.id">
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'
import { mapGetters } from 'vuex'
import pick from 'lodash.pick'

export default {
  name: 'PsTrainModal',
  data() {
    return {
      title: '操作',
      visible: false,
      model: {},
      isAdd: true,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      url: {
        list: '/train/cogTrainPackageTemplate/list',
        add: '/psychology/psUser/edit',
      },
      trainTemplateList: []
    }
  },
  created() {
    
  },
  methods: {
    ...mapGetters(['nickname', 'userInfo']),
    loadTrainList() {
      this.confirmLoading = true
      this.trainTemplateList = []
      getAction(this.url.list, {pageNo: 1, pageSize: 1000}).then((res) => {
        if (res.success) {
          this.trainTemplateList = res.result.records
        }
      }).finally(() => {
        this.confirmLoading = false
      })
    },
    add(id) {
      this.isAdd = false
      this.edit({id})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'cogTrainPackageTemplateId'))
      });
      this.loadTrainList()
    },
    close() {
      this.$emit('close')
      this.isAdd = true
      this.model = {}
      this.visible = false
    },
    handleOk() {
      this.form.validateFields((err, values) => {
        if (!values.cogTrainPackageTemplateId) {
          this.$message.error('请选择套餐训练')
          return
        }

        this.confirmLoading = true
        let formData = Object.assign(this.model, values);
        httpAction(this.url.add, formData, 'put').then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.$emit('ok')
          } else {
            this.$message.warning(res.message)
          }
        }).finally(() => {
          this.close()
        })
      })
    },
    handleCancel() {
      this.confirmLoading = false
      this.close()
    }
  }
}
</script>

<style lang='less' scoped>

</style>
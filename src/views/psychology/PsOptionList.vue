<template>
  <a-card :bordered="false">
    <!-- 操作按钮区域 -->
    <div class="table-operator" :md="24" :sm="24" style="margin: -25px 0px 10px 0px;padding-left: 10px;">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete" />
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div style="width: 99%;padding-left: 10px;">
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
          selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <template slot="avatarslot" slot-scope="text, record, index">
          <div class="anty-img-wrap">
            <a-avatar v-if="record.type==2" shape="square" :src="getAvatarView(record.content)" icon="user" />
            <span v-if="record.type==1">{{ record.content }}</span>
          </div>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->
    <!-- 表单区域 -->
    <PsOptionModal ref="modalForm" @ok="modalFormOk"></PsOptionModal>
  </a-card>
</template>

<script>
import PsOptionModal from './modules/PsOptionModal'
import PsQuesionList from './PsQuesionList'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, getFileAccessHttpUrl } from '@/api/manage'

export default {
  name: 'PsOptionList',
  mixins: [JeecgListMixin],
  components: {
    PsQuesionList,
    PsOptionModal
  },
  data() {
    return {
      description: '选项信息',
      dataSource: [],
      // 表头
      columns: [
        {
          title: '标题',
          align: 'center',
          dataIndex: 'title'
        },
        {
          title: '选项类型',
          align: 'center',
          dataIndex: 'typeDicText'
        },
        {
          title: '选项内容',
          align: 'center',
          dataIndex: 'content',
          scopedSlots: { customRender: 'avatarslot' }
        },
        {
          title: '得分',
          align: 'center',
          dataIndex: 'score'
        },
        {
          title: '排序',
          align: 'center',
          dataIndex: 'sort'
        },
        {
          title: '操作',
          key: 'operation',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/psychology/psOption/listOptionByMainId',
        delete: '/psychology/psOption/delete',
        deleteBatch: '/psychology/psOption/deleteBatch',
        imgerver: window._CONFIG['domianURL']
      }
    }
  },
  methods: {
    getAvatarView: function(avatar) {
      return getFileAccessHttpUrl(avatar, this.url.imgerver, 'http')
    },
    loadData(arg) {
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams()
      getAction(this.url.list, { mainId: params.mainId }).then((res) => {
        if (res.success) {
          this.dataSource = res.result
        } else {
          this.dataSource = null
        }
      })
    },
    getOrderMain(quesionId) {
      this.queryParam.mainId = quesionId
      this.loadData(1)
    },
    handleAdd: function() {
      this.$refs.modalForm.add(this.queryParam.mainId)
      this.$refs.modalForm.title = '添加选项信息'
    }
  }
}
</script>
<style scoped>
.ant-card {
  margin-left: -30px;
  margin-right: -30px;
}
</style>
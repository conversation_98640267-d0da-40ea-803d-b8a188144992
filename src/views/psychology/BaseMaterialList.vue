<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="标题">
              <j-input placeholder="请输入标题" v-model="queryParam.title" />
            </a-form-item>
          </a-col>
          <!-- <a-col :md="6" :sm="8">
            <a-form-item label="素材类型">
              <a-select v-model="queryParam.type" placeholder="请选择素材类型" allowClear style="width: 100%;">
                <a-select-option :value="1">文章</a-select-option>
                <a-select-option :value="2">视频</a-select-option>
                <a-select-option :value="6">引导页</a-select-option>
              </a-select>
            </a-form-item>
          </a-col> -->
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <!-- <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"
                             style="margin-left: 8px"></j-super-query> -->
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button> -->
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete"/>
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down"/>
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">
        {{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <span slot="action" slot-scope="text, record">
          <template v-if="record.type === 2">
            <a @click="handlePlay(record)">播放</a>
            <!-- <a-divider type="vertical"/> -->
          </template>
          <!-- <a @click="handleEdit(record)">编辑</a> -->
          <!-- <a-divider type="vertical"/>
          <a @click="handleDetail(record)">详情</a>
          <a-divider type="vertical"/>
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm> -->
        </span>
      </a-table>
    </div>

    <base-material-modal ref="modalForm" @ok="modalFormOk"></base-material-modal>
    <base-video-modal ref="schemeModal"></base-video-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import BaseMaterialModal from './modules/BaseMaterialModal'
  import BaseVideoModal from './modules/BaseVideoModal.vue'
  import JInput from '@/components/jeecg/JInput'

  export default {
    name: 'BaseMaterialList',
    mixins: [JeecgListMixin, mixinDevice],
    components: {
      BaseMaterialModal,
      BaseVideoModal,
      JInput
    },
    data() {
      return {
        description: '素材表管理页面',
        // 表头
        columns: [
          {
            title: '标题',
            align: 'center',
            dataIndex: 'title'
          },
          // {
          //   title: '素材类型',
          //   align: 'center',
          //   dataIndex: 'type_dictText'
          // },
          {
            title: '素材分类',
            align: 'center',
            dataIndex: 'materialCategoryId_dictText'
          },
          // {
          //   title: '状态',
          //   align: 'center',
          //   dataIndex: 'status_dictText'
          // },
          {
            title: '创建时间',
            align: 'center',
            dataIndex: 'createTime'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 200,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: '/base/baseMaterial/defaultList',
          delete: '/base/baseMaterial/delete',
          deleteBatch: '/base/baseMaterial/deleteBatch',
          exportXlsUrl: '/base/baseMaterial/exportXls',
          importExcelUrl: 'base/baseMaterial/importExcel'

        },
        dictOptions: {},
        superFieldList: [],
        queryParam: {
          type: 2
        }
      }
    },
    created() {
      this.getSuperFieldList()
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {
      initDictConfig() {
      },
      searchReset() {
        this.queryParam = {type: 2}
        this.searchTime = []
        this.loadData(1);
      },
      getSuperFieldList() {
        let fieldList = []
        fieldList.push({ type: 'string', value: 'title', text: '标题' })
        fieldList.push({ type: 'string', value: 'subTitle', text: '副标题' })
        // fieldList.push({ type: 'select', value: 'type', text: '素材类型', dictCode:"material_type" })
        fieldList.push({ type: 'select', value: 'materialCategoryId', text: '素材分类',dictCode:"base_material_category,name,id"})
        // fieldList.push({ type: 'select', value: 'status', text: '状态',dictCode:"enable_status" })
        fieldList.push({ type: 'string', value: 'description', text: '描述' })
        this.superFieldList = fieldList
      },
      handlePlay(record) {
        this.$refs.schemeModal.playVideo(record.videoPath);
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
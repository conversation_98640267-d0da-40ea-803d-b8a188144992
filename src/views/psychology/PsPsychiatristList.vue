<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="姓名">
              <a-input placeholder="请输入姓名" v-model="queryParam.name"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="标签">
              <a-input placeholder="请输入标签" v-model="queryParam.tag"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <!--      <a-button type="primary" icon="download" @click="handleExportXls('心理咨询师表')">导出</a-button>
            <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl"
                      @change="handleImportExcel">
              <a-button type="primary" icon="import">导入</a-button>
            </a-upload>-->
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete"/>
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down"/>
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
        selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">
        <template slot="avatarslot" slot-scope="text, record, index">
          <div class="anty-img-wrap">
            <a-avatar shape="square" :src="getAvatarView(record.avatar)" icon="user"/>
          </div>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical"/>
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <psPsychiatrist-modal ref="modalForm" @ok="modalFormOk"></psPsychiatrist-modal>
  </a-card>
</template>

<script>
  import PsPsychiatristModal from './modules/PsPsychiatristModal'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'

  export default {
    name: 'PsPsychiatristList',
    mixins: [JeecgListMixin],
    components: {
      PsPsychiatristModal
    },
    data() {
      return {
        description: '心理咨询师表管理页面',
        // 表头
        columns: [
          {
            title: '姓名',
            align: 'center',
            dataIndex: 'name'
          },
          {
            title: '头像',
            align: 'center',
            dataIndex: 'avatar',
            scopedSlots: { customRender: 'avatarslot' }
          },
          {
            title: '标签',
            align: 'center',
            dataIndex: 'tag'
          },
          {
            title: '从业年限',
            align: 'center',
            dataIndex: 'workingPeriod'
          },
          {
            title: '手机号',
            align: 'center',
            dataIndex: 'phone'
          },
          {
            title: '咨询价格',
            align: 'center',
            dataIndex: 'price'
          },
          {
            title: '咨询经验/小时',
            align: 'center',
            dataIndex: 'experienceTime'
          },
          {
            title: '咨询人数',
            align: 'center',
            dataIndex: 'consultNum'
          },
          {
            title: '咨询方式（多种咨询方式用,隔开）',
            align: 'center',
            dataIndex: 'consultWay'
          },
          {
            title: '状态',
            align: 'center',
            dataIndex: 'status_dictText'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          imgerver: window._CONFIG['staticDomainURL'],
          list: '/psychology/psPsychiatrist/list',
          delete: '/psychology/psPsychiatrist/delete',
          deleteBatch: '/psychology/psPsychiatrist/deleteBatch',
          exportXlsUrl: 'psychology/psPsychiatrist/exportXls',
          importExcelUrl: 'psychology/psPsychiatrist/importExcel'
        }
      }
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
      }
    },
    methods: {
      getAvatarView: function(avatar) {
        return this.url.imgerver + '/' + avatar
      },
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
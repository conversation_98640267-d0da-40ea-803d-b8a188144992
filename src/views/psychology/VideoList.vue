<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="视频名称">
              <a-input placeholder="请输入视频名称" v-model="queryParam.title"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete"/>
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down"/>
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
        selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">
        <template slot="avatarslot" slot-scope="text, record">
          <div class="anty-img-wrap">
            <img v-if="record.coverUrl" :src="getAvatarView(record.coverUrl)" style="width:80px;" alt="">
          </div>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical"/>
          <a @click="handleOpen(record)">查看</a>
          <a-divider type="vertical"/>
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <video-modal ref="modalForm" @ok="modalFormOk"></video-modal>
    <video-info ref="modalInfo"></video-info>
  </a-card>
</template>

<script>
  import VideoModal from './modules/VideoModal'
  import VideoInfo from './modules/VideoInfo'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'

  export default {
    name: 'PsArticleList',
    mixins: [JeecgListMixin],
    components: {
      VideoModal,
      VideoInfo
    },
    data() {
      return {
        description: '科普文章表管理页面',
        // 表头
        columns: [
          {
            title: '视频名称',
            align: 'center',
            dataIndex: 'videoName'
          },
          {
            title: '封面图',
            align: 'center',
            dataIndex: 'coverUrl',
            scopedSlots: { customRender: 'avatarslot' }
          },
          {
            title: '音频名称',
            align: 'center',
            dataIndex: 'audioName'
          },
          {
            title: '视频持续播放时间（秒）',
            align: 'center',
            dataIndex: 'duration'
          },
          {
            title: '音频延迟播放时间（秒）',
            align: 'center',
            dataIndex: 'audioDelay'
          },
          {
            title: '放松训练视频角度',
            align: 'center',
            dataIndex: 'angle'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          imgerver: window._CONFIG['staticDomainURL'],
          list: '/vr/video/list',
          delete: '/vr/video/delete',
          deleteBatch: '/vr/video/deleteBatch',
        }
      }
    },
    methods: {
      getAvatarView: function(avatar) {
        return this.url.imgerver + '/' + avatar
      },
      handleOpen(record) {
        this.$refs.modalInfo.open(record)
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>
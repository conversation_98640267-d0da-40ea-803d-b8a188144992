<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="音频名称">
              <a-input placeholder="请输入音频名称" v-model="queryParam.audioName" allowClear></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="类型">
              <a-select placeholder="请选择音频类型" v-model="queryParam.audioCategoryId" allowClear>
                <a-select-option
                  v-for="item in typeList"
                  :key="item.id"
                  :value="item.id">
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <!-- 高级查询区域 -->
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete"/>
            删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作
          <a-icon type="down"/>
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
        selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <span slot="audioCategoryId" slot-scope="text, record">
          {{ record.audioCategoryId && typeList.filter(item => item.id === record.audioCategoryId)[0].name }}
        </span>

        <span slot="action" slot-scope="text, record">
          <a v-has="'relaxTrain:list:edit'" @click="handleDetail(record)">详情</a>
          <a-divider v-has="'relaxTrain:list:edit'" type="vertical"/>
          <a v-has="'relaxTrain:list:edit'" @click="handleEdit(record)">编辑</a>
          <a-divider v-has="'relaxTrain:list:edit'" type="vertical"/>
          <a v-if="!record.playStatus" @click="handlePlay(record)">播放</a>
          <a v-if="record.playStatus === 1" @click="handleEnd(record)">停止</a>
          <a-divider v-has="'relaxTrain:list:edit'" type="vertical"/>
          <a-popconfirm v-has="'relaxTrain:list:edit'" title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>

      </a-table>
    </div>
    <audio ref="music" muted controls="controls" style="display:none">
      <source :src="audioUrl" type="audio/mpeg" />
    </audio>
    <audio-modal ref="modalForm" @ok="modalFormOk" :typeList="typeList"></audio-modal>
  </a-card>
</template>

<script>

  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import AudioModal from './modules/AudioModal'
  import { getAction } from '@/api/manage'

  export default {
    name: 'AudioList',
    mixins: [JeecgListMixin],
    components: {
      AudioModal
    },
    data() {                                                                                                                                                                 
      return {
        audioUrlPrefix: window._CONFIG['measureAudioURL'], //音频前缀，默认为空，由于本地没有音频文件，本地调试的时候此处配置线上绝对路径  https://zcpm.zhisongkeji.com
        audioUrl: '',
        description: '训练表管理页面',
        // 表头
        columns: [
          {
            title: '音频名称',
            align: 'center',
            dataIndex: 'audioName'
          },
          {
            title: '类别',
            align: 'center',
            dataIndex: 'audioCategoryId',
            scopedSlots: { customRender: 'audioCategoryId' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 185,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: '/vr/audio/list',
          delete: '/vr/audio/delete',
          deleteBatch: '/vr/audio/deleteBatch',
          typeList: '/vr/audioCategory/findAudioCategoryList'

        },
        dictOptions: {},
        oldRecord: {},
        superFieldList: [],
        typeList: []
      }
    },
    created() {
      this.getTypeList()
    },
    methods: {
      initDictConfig() {
      },
      getTypeList() {
        getAction(this.url.typeList).then((res) => {
          if (res.success) {
            this.typeList = res.result
          }
        })
      },
      handlePlay(record) {
        this.audioUrl = record.audioUrl
        if (this.oldRecord) {
          this.$set(this.oldRecord, 'playStatus', 0)
        }
        
        this.$refs.music.load()
        this.$nextTick(() => {
          this.$refs.music.play()
          this.$set(record, 'playStatus', 1)
        })
        this.oldRecord = record
      },
      handleEnd(record) {
        this.$refs.music.pause()
        this.$set(record, 'playStatus', 0)
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
/** init domain config */
import Vue from 'vue'
//设置全局API_BASE_URL
Vue.prototype.API_BASE_URL = process.env.VUE_APP_API_BASE_URL
// Vue.prototype.API_BASE_URL = 'https://zcpm.zhisongkeji.com/mentality-ct'
// Vue.prototype.API_BASE_URL = 'http://3c6ede12.r7.cpolar.top/mentality-ct'
window._CONFIG['domianURL'] = Vue.prototype.API_BASE_URL
window._CONFIG['staticDomainURL'] = window._CONFIG['domianURL'] + '/sys/common/static'
window._CONFIG['pdfDomainURL'] = window._CONFIG['domianURL'] + '/sys/common/pdf/pdfPreviewIframe';
window._CONFIG['websocketURL'] = process.env.VUE_APP_API_WSS_URL;
window._CONFIG['measureAudioURL'] = (typeof (process.env.VUE_APP_API_MEASURE_AUDIO_URL) == 'undefined' || process.env.VUE_APP_API_MEASURE_AUDIO_URL == null) ? '' : process.env.VUE_APP_API_MEASURE_AUDIO_URL;
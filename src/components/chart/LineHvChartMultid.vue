<template>
  <div>
    <h4 :style="{ marginBottom: '20px' }">{{ title }}</h4>
    <v-chart :forceFit="true" :height="height" :data="dataSource" :scale="scale">
      <v-tooltip :crosshairs="crosshairs" />
      <v-axis dataKey="totalPoints" />
      <v-legend/>
      <v-line position="time*totalPoints" color="measureName" />
      <v-point position="time*totalPoints" color="measureName" size="4" shape="circle" :style="style" />
    </v-chart>
  </div>
</template>

<script>

export default {
  name: 'LineHvChartMultid',
  props: {
    title: {
      type: String,
      default: ''
    },
    dataSource: {
      type: Array,
      default: () => []
    },
    height: {
      type: Number,
      default: 400
    }
  },
  data() {
    return {
      scale: [{
        dataKey: 'time',
        range: [0, 1]
      }],
      crosshairs: {
        type: 'line'
      },
      style: {
        stroke: '#fff',
        lineWidth: 1
      }
    }
  }
}
</script>

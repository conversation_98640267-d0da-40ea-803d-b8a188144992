<template>
  <div class="components-input-demo-presuffix">
    <!---->
    <a-input @click="openModal" placeholder="请点击选择患者" v-model="patientNames" readOnly :disabled="disabled">
      <a-icon v-if="patientNames && !disabled" slot="suffix" type="close-circle" @click="handleEmpty" title="清空"/>
    </a-input>

    <SelectPatientModal ref="SelectPatientModal" :isRadio="isRadio" @selectFinished="handleOK"></SelectPatientModal>
  </div>
</template>

<script>
import SelectPatientModal from './modal/SelectPatientModal'

export default {
  name: 'JSelectPatient',
  components: {
    SelectPatientModal
  },
  props: {
    value: {
      type: String,
      required: false
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    // 自定义返回字段，默认返回 id
    customReturnField: {
      type: String,
      default: 'id'
    },
    backInfo: {
      type: Boolean,
      default: false,
      required: false
    },
    isRadio: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      visible: false,
      confirmLoading: false,
      patientNames: '',
      patientIds: ''
    }
  },
  mounted() {
    this.patientNames = this.value
  },
  watch: {
    value: {
      handler(val) {
        this.patientNames = val
        if (!val) this.patientIds = ''
      },
      immediate: true
    }
  },
  methods: {
    //返回选中的部门信息
    backPatientInfo() {
      if (this.backInfo === true) {
        if (this.patientIds && this.patientIds.length > 0) {
          let arr1 = this.patientIds.split(',')
          let arr2 = this.patientNames.split(',')
          let info = []
          for (let i = 0; i < arr1.length; i++) {
            info.push({
              value: arr1[i],
              text: arr2[i]
            })
          }
          this.$emit('back', info)
        }
      }
    },
    openModal() {
      this.$refs.SelectPatientModal.visible = true
      this.$refs.SelectPatientModal.setRowKeys(this.patientIds ? this.patientIds.split(',') : [])
    },
    handleOK(rows) {
      let value = ''
      if (!rows && rows.length <= 0) {
        this.patientNames = ''
        this.patientIds = ''
      } else {
        value = rows.map(row => row[this.customReturnField]).join(',')
        this.patientNames = rows.map(row => row['name']).join(',')
        this.patientIds = rows.map(row => row['id']).join(',')
      }
      this.$emit('update:value', value)
      this.backPatientInfo()
    },
    getPatientNames() {
      return this.patientNames
    },
    handleEmpty() {
      this.handleOK('')
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  }
}
</script>

<style scoped>
.components-input-demo-presuffix .anticon-close-circle {
  cursor: pointer;
  color: #ccc;
  transition: color 0.3s;
  font-size: 12px;
}

.components-input-demo-presuffix .anticon-close-circle:hover {
  color: #f5222d;
}

.components-input-demo-presuffix .anticon-close-circle:active {
  color: #666;
}
</style>
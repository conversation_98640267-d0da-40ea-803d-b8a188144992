<template>
  <div class="tinymce-editor">
    <editor
      v-model="myValue"
      :init="init"
      :disabled="disabled"
      @onClick="onClick">
    </editor>
  </div>
</template>

<script>
import tinymce from 'tinymce/tinymce'
import Editor from '@tinymce/tinymce-vue'
import 'tinymce/icons/default/icons.min.js'
import 'tinymce/themes/silver/theme'
import 'tinymce/plugins/image'
import 'tinymce/plugins/media'
import 'tinymce/plugins/table'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/contextmenu'
import 'tinymce/plugins/wordcount'
import 'tinymce/plugins/colorpicker'
import 'tinymce/plugins/textcolor'
import 'tinymce/plugins/fullscreen'
import 'tinymce/plugins/preview'
import 'tinymce/plugins/link'
import 'tinymce/plugins/hr'
import 'tinymce/plugins/pagebreak'
import 'tinymce/plugins/code'

export default {
  components: {
    Editor
  },
  props: {
    value: {
      type: String,
      required: false
    },
    triggerChange: {
      type: Boolean,
      default: false,
      required: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    plugins: {
      type: [String, Array],
      default: 'lists image media table textcolor wordcount contextmenu fullscreen preview link hr pagebreak code'
    },
    toolbar: {
      type: [String, Array],
      default: 'code fullscreen undo redo |  formatselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | lists image media table | removeformat'
    },
    jHeight: {
      type: Number,
      default: 300
    }
  },
  data() {
    return {
      //初始化配置
      init: {
        language_url: '/tinymce/langs/zh_CN.js',
        language: 'zh_CN',
        skin_url: '/tinymce/skins/lightgray',
        relative_urls: false,
        remove_script_host: false,
        height: this.jHeight,
        plugins: this.plugins,
        toolbar: this.toolbar,
        branding: false,
        force_br_newlines: false,
        force_p_newlines: false,
        forced_root_block: '',
        menu: {
          file: { title: '文件', items: 'newdocument | preview' },
          edit: { title: '编辑', items: 'undo redo | cut copy paste pastetext | selectall' },
          insert: { title: '插入', items: 'link media | hr pagebreak' },
          view: { title: '查看', items: 'visualaid' },
          format: {
            title: '格式',
            items: 'bold italic underline strikethrough superscript subscript | formats | removeformat | textcolor '
          },
          table: { title: '表格', items: 'inserttable tableprops deletetable | cell row column' },
          tools: { title: '工具', items: 'spellchecker code' }
        },
        images_upload_handler: (blobInfo, success) => {
          const img = 'data:image/jpeg;base64,' + blobInfo.base64()
          success(img)
        }
      },
      myValue: this.value
    }
  },
  mounted() {
    tinymce.init({})
  },
  methods: {
    onClick(e) {
      this.$emit('onClick', e, tinymce)
    },
    //可以添加一些自己的自定义事件，如清空内容
    clear() {
      this.myValue = ''
    }
  },
  watch: {
    value(newValue) {
      this.myValue = newValue
    },
    myValue(newValue) {
      if (this.triggerChange) {
        console.log(1)
        this.$emit('change', newValue)
      } else {
        console.log(2)
        this.$emit('input', newValue)
      }
    }
  }
}

</script>
<style scoped>
</style>
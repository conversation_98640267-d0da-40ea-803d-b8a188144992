<template>
  <div class="setting-drawer">
    <a-drawer
      width="300"
      placement="right"
      :closable="false"
      @close="onClose"
      :visible="visible"
      :style="{}"
    >
      <div class="setting-drawer-index-content">

        <div :style="{ marginBottom: '24px' }">
          <h3 class="setting-drawer-index-title">整体风格设置</h3>

          <div class="setting-drawer-index-blockChecbox">
            <a-tooltip>
              <template slot="title">
                暗色菜单风格
              </template>
              <div class="setting-drawer-index-item" @click="handleMenuTheme('dark')">
                <img src="https://gw.alipayobjects.com/zos/rmsportal/LCkqqYNmvBEbokSDscrm.svg" alt="dark">
                <div class="setting-drawer-index-selectIcon" v-if="navTheme === 'dark'">
                  <a-icon type="check"/>
                </div>
              </div>
            </a-tooltip>

            <a-tooltip>
              <template slot="title">
                亮色菜单风格
              </template>
              <div class="setting-drawer-index-item" @click="handleMenuTheme('light')">
                <img src="https://gw.alipayobjects.com/zos/rmsportal/jpRkZQMyYRryryPNtyIC.svg" alt="light">
                <div class="setting-drawer-index-selectIcon" v-if="navTheme !== 'dark'">
                  <a-icon type="check"/>
                </div>
              </div>
            </a-tooltip>
          </div>
        </div>

        <div :style="{ marginBottom: '24px' }">
          <h3 class="setting-drawer-index-title">主题色</h3>

          <div style="height: 20px">
            <a-tooltip class="setting-drawer-theme-color-colorBlock" v-for="(item, index) in colorList" :key="index">
              <template slot="title">
                {{ item.key }}
              </template>
              <a-tag :color="item.color" @click="changeColor(item.color)">
                <a-icon type="check" v-if="item.color === primaryColor"></a-icon>
              </a-tag>
            </a-tooltip>

          </div>
        </div>
        <a-divider/>

        <div :style="{ marginBottom: '24px' }">
          <h3 class="setting-drawer-index-title">导航模式</h3>

          <div class="setting-drawer-index-blockChecbox">
            <a-tooltip>
              <template slot="title">
                侧边栏导航
              </template>
              <div class="setting-drawer-index-item" @click="handleLayout('sidemenu')">
                <img src="/navigation1.svg" alt="sidemenu">
                <div class="setting-drawer-index-selectIcon" v-if="layoutMode === 'sidemenu'">
                  <a-icon type="check"/>
                </div>
              </div>
            </a-tooltip>

            <a-tooltip>
              <template slot="title">
                顶部栏导航
              </template>
              <div class="setting-drawer-index-item" @click="handleLayout('topmenu')">
                <img src="/navigation2.svg" alt="topmenu">
                <div class="setting-drawer-index-selectIcon" v-if="layoutMode !== 'sidemenu'">
                  <a-icon type="check"/>
                </div>
              </div>
            </a-tooltip>
          </div>
          <div :style="{ marginTop: '24px' }">
            <a-list :split="false">
              <a-list-item>
                <a-tooltip slot="actions">
                  <template slot="title">
                    该设定仅 [顶部栏导航] 时有效
                  </template>
                  <a-select size="small" style="width: 80px;" :defaultValue="contentWidth"
                            @change="handleContentWidthChange">
                    <a-select-option value="Fixed">固定</a-select-option>
                    <a-select-option value="Fluid" v-if="layoutMode !== 'sidemenu'">流式</a-select-option>
                  </a-select>
                </a-tooltip>
                <a-list-item-meta>
                  <div slot="title">内容区域宽度</div>
                </a-list-item-meta>
              </a-list-item>
              <a-list-item>
                <a-switch slot="actions" size="small" :defaultChecked="fixedHeader" @change="handleFixedHeader"/>
                <a-list-item-meta>
                  <div slot="title">固定 Header</div>
                </a-list-item-meta>
              </a-list-item>
              <!--              <a-list-item>
                              <a-switch slot="actions" size="small" :disabled="!fixedHeader" :defaultChecked="autoHideHeader" @change="handleFixedHeaderHidden" />
                              <a-list-item-meta>
                                <div slot="title" :style="{ textDecoration: !fixedHeader ? 'line-through' : 'unset' }">下滑时隐藏 Header</div>
                              </a-list-item-meta>
                            </a-list-item>
                            <a-list-item >
                              <a-switch slot="actions" size="small" :disabled="(layoutMode === 'topmenu')" :checked="dataFixSiderbar" @change="handleFixSiderbar" />
                              <a-list-item-meta>
                                <div slot="title" :style="{ textDecoration: layoutMode === 'topmenu' ? 'line-through' : 'unset' }">固定侧边菜单</div>
                              </a-list-item-meta>
                            </a-list-item>-->
            </a-list>
          </div>
        </div>
        <a-divider/>
        <div :style="{ marginBottom: '24px' }">
          <h3 class="setting-drawer-index-title">其他设置</h3>
          <div>
            <a-list :split="false">
              <a-list-item>
                <a-switch slot="actions" size="small" :defaultChecked="colorWeak" @change="onColorWeak"/>
                <a-list-item-meta>
                  <div slot="title">色弱模式</div>
                </a-list-item-meta>
              </a-list-item>
              <a-list-item>
                <a-switch slot="actions" size="small" :defaultChecked="multipage" @change="onMultipageWeak"/>
                <a-list-item-meta>
                  <div slot="title">多页签模式</div>
                </a-list-item-meta>
              </a-list-item>
            </a-list>
          </div>
        </div>
        <a-divider/>
        <div :style="{ marginBottom: '24px' }">
          <h3 class="setting-drawer-index-title">报告设置</h3>
          <div>
            <a-list :split="false">
              <a-list-item>
                <a-switch slot="actions" size="small" :defaultChecked="this.userInfo().isShowIntroduction == '1'" @change="onIntroduction"/>
                <a-list-item-meta>
                  <div slot="title">报告简介</div>
                </a-list-item-meta>
              </a-list-item>
              <a-list-item>
                <a-switch slot="actions" size="small" :defaultChecked="this.userInfo().isShowOrgOption == '1'" @change="onOrgOption"/>
                <a-list-item-meta>
                  <div slot="title">原始数据</div>
                </a-list-item-meta>
              </a-list-item>
            </a-list>
          </div>
        </div>
        <a-divider/>
        <div :style="{ marginBottom: '24px' }">
          <h3 class="setting-drawer-index-title">电子签名</h3>
          <div>
            <a-upload
              name="signature"
              list-type="picture-card"
              class="avatar-uploader"
              :show-upload-list="false"
              :action="signatureUpload"
              :before-upload="beforeUpload"
              :headers="tokenHeader"
              @change="handleChange"
              @preview="handlePreview"
              isMultiple="false"
              :data="{biz:'temp/image/signature'}"
            >
              <div :style="{'width':'200px','height':'104px'}">
                <img v-if="picUrl" :src="getAvatarView()" style="width:100%;height:100%"/>
                <div v-else style="padding-top: 30px;">
                  <a-icon :type="uploadLoading ? 'loading' : 'plus'"/>
                  <div class="ant-upload-text">上传</div>
                </div>
              </div>
              <a-modal :visible="previewVisible" :footer="null" @cancel="onClose()">
                <img alt="example" style="width: 100%" :src="previewImage"/>
              </a-modal>
            </a-upload>
          </div>
        </div>
        <a-divider/>
        <div :style="{ marginBottom: '24px' }">
          <h3 class="setting-drawer-index-title">上传logo</h3>
          <div>
            <j-image-upload class="avatar-uploader" bizPath="temp/image/orgLogo" text="上传"
                            v-model="logoPath"></j-image-upload>
          </div>
        </div>
        <a-divider/>
        <!--        <div :style="{ marginBottom: '24px' }">
                  <a-alert type="warning">
                    <span slot="message">
                      配置栏只在开发环境用于预览，生产环境不会展现，请手动修改配置文件
                      <a href="https://github.com/sendya/ant-design-pro-vue/blob/master/src/defaultSettings.js" target="_blank">src/defaultSettings.js</a>
                    </span>
                  </a-alert>
                </div>-->
      </div>
      <div class="setting-drawer-index-handle" @click="toggle">
        <a-icon type="setting" v-if="!visible"/>
        <a-icon type="close" v-else/>
      </div>
    </a-drawer>
  </div>
</template>

<script>
import DetailList from '@/components/tools/DetailList'
import SettingItem from '@/components/setting/SettingItem'
import config from '@/defaultSettings'
import {colorList, updateColorWeak, updateTheme} from '@/components/tools/setting'
import {mixin, mixinDevice} from '@/utils/mixin.js'
import {triggerWindowResizeEvent} from '@/utils/util'
import Vue from 'vue'
import {ACCESS_TOKEN, USER_INFO} from '@/store/mutation-types'
import {getFileAccessHttpUrl} from '@api/manage'
import JImageUpload from '@/components/jeecg/JImageUpload'
import {putAction} from '@/api/manage'
import {mapGetters} from "vuex";

export default {
  components: {
    DetailList,
    SettingItem,
    JImageUpload
  },
  mixins: [mixin, mixinDevice],
  data() {
    return {
      //token header
      tokenHeader: {'X-Access-Token': Vue.ls.get(ACCESS_TOKEN)},
      visible: true,
      colorList,
      dataFixSiderbar: false,
      signatureUploadUrl: '/sys/user/signatureUpload',
      updateById: '/sys/user/updateById',
      loading: false,
      picUrl: false,
      filePath: {},
      uploadLoading: false,
      previewVisible: false,
      previewImage: '',
      logoPath: ''
    }
  },
  watch: {
    logoPath(val) {
      putAction(this.updateById, {'logoPath': val}).then((res) => {
        if (res.success) {
          this.$message.success("上传成功！")
        } else {
          this.$message.warning(res.message)
        }
      })
    }
  },
  mounted() {
    const vm = this
    setTimeout(() => {
      vm.visible = false
    }, 16)
    // 当主题色不是默认色时，才进行主题编译
    if (this.primaryColor !== config.primaryColor) {
      updateTheme(this.primaryColor)
    }
    if (this.colorWeak !== config.colorWeak) {
      updateColorWeak(this.colorWeak)
    }
    if (this.multipage !== config.multipage) {
      this.$store.dispatch('ToggleMultipage', this.multipage)
    }
  },
  computed: {
    signatureUpload: function () {
      return `${window._CONFIG['domianURL']}/${this.signatureUploadUrl}`
    }
  },
  created() {
    if (Vue.ls.get(USER_INFO).signaturePath) {
      this.picUrl = true
      this.filePath.url = Vue.ls.get(USER_INFO).signaturePath
    }
  },
  methods: {
    ...mapGetters(['userInfo']),
    showDrawer() {
      this.visible = true
    },
    onClose() {
      this.visible = false
      this.previewVisible = false
    },
    toggle() {
      this.visible = !this.visible
    },
    onColorWeak(checked) {
      this.$store.dispatch('ToggleWeak', checked)
      updateColorWeak(checked)
    },
    onMultipageWeak(checked) {
      this.$store.dispatch('ToggleMultipage', checked)
    },
    handleMenuTheme(theme) {
      this.$store.dispatch('ToggleTheme', theme)
    },
    handleLayout(mode) {
      this.$store.dispatch('ToggleLayoutMode', mode)
      // 因为顶部菜单不能固定左侧菜单栏，所以强制关闭
      this.handleFixSiderbar(false)
      // 触发窗口resize事件
      triggerWindowResizeEvent()
    },
    handleContentWidthChange(type) {
      this.$store.dispatch('ToggleContentWidth', type)
    },
    changeColor(color) {
      if (this.primaryColor !== color) {
        this.$store.dispatch('ToggleColor', color)
        updateTheme(color)
      }
    },
    handleFixedHeader(fixed) {
      this.$store.dispatch('ToggleFixedHeader', fixed)
    },
    handleFixedHeaderHidden(autoHidden) {
      this.$store.dispatch('ToggleFixedHeaderHidden', autoHidden)
    },
    handleFixSiderbar(fixed) {
      if (this.layoutMode === 'topmenu') {
        fixed = false
      }
      this.dataFixSiderbar = fixed
      this.$store.dispatch('ToggleFixSiderbar', fixed)
    },
    handleChange(info) {
      this.picUrl = false
      let file = info.file
      //update-end-author:wangshuai date:20201022 for:LOWCOD-969 判断number是否大于0和是否多选，返回选定的元素。
      if (info.file.status === 'done') {
        if (info.file.response.success) {
          this.picUrl = true
          file.url = file.response.message
        }
        this.$message.success(`${info.file.name} 上传成功!`)
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 上传失败.`)
      }
      this.filePath = file
    },
    beforeUpload(file) {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
      if (!isJpgOrPng) {
        this.$message.error('You can only upload JPG file!')
      }
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message.error('Image must smaller than 2MB!')
      }
      return isJpgOrPng && isLt2M
    },
    getAvatarView() {
      if (this.filePath) {
        return getFileAccessHttpUrl(this.filePath.url)
      }
    },
    // 预览
    handlePreview(file) {
      this.previewImage = file.url || file.thumbUrl
      this.previewVisible = true
    },
    onOrgOption(checked) {
      let userInfo = this.userInfo();
      userInfo.isShowOrgOption = checked ? "1" : "0";
      putAction(this.updateById, {'id': this.userInfo().id, 'isShowOrgOption': checked ? '1' : '0'}).then((res) => {
        if (res.success) {
          Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)
          this.$message.success("操作成功！")
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    onIntroduction(checked){
      let userInfo = this.userInfo();
      userInfo.isShowIntroduction = checked ? "1" : "0";
      putAction(this.updateById, {'id': this.userInfo().id, 'isShowIntroduction': checked ? '1' : '0'}).then((res) => {
        if (res.success) {
          Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)
          this.$message.success("操作成功！")
        } else {
          this.$message.warning(res.message)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

.setting-drawer-index-content {

  .setting-drawer-index-blockChecbox {
    display: flex;

    .setting-drawer-index-item {
      margin-right: 16px;
      position: relative;
      border-radius: 4px;
      cursor: pointer;

      img {
        width: 48px;
      }

      .setting-drawer-index-selectIcon {
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        padding-top: 15px;
        padding-left: 24px;
        height: 100%;
        color: #1890ff;
        font-size: 14px;
        font-weight: 700;
      }
    }
  }

  .setting-drawer-theme-color-colorBlock {
    width: 20px;
    height: 20px;
    border-radius: 2px;
    float: left;
    cursor: pointer;
    margin-right: 8px;
    padding-left: 0px;
    padding-right: 0px;
    text-align: center;
    color: #fff;
    font-weight: 700;

    i {
      font-size: 14px;
    }
  }
}

.setting-drawer-index-handle {
  position: absolute;
  top: 240px;
  background: #1890ff;
  width: 48px;
  height: 48px;
  right: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  pointer-events: auto;
  z-index: 1001;
  text-align: center;
  font-size: 16px;
  border-radius: 4px 0 0 4px;

  i {
    color: rgb(255, 255, 255);
    font-size: 20px;
  }
}
</style>
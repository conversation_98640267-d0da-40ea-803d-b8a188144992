<template>
  <div class="games-container">
    <component :is="cmpName" :info="info" />
  </div>
</template>

<script>
import { GetRequest } from '@/components/_util/util'
import { getAction, postAction } from '@/api/manage'
export default {
  props: ["componentName"],
  components: {


    game10: () => import("./lib/game10"),
    game64: () => import("./lib/game64"),
    game78: () => import("./lib/game78"),
    game79: () => import("./lib/game79"),

    game1: () => import("./lib/game1"),
    game4: () => import("./lib/game4"),
    game6: () => import('./lib/game6'),
    game8: () => import('./lib/game8'),
    //张爱军  game10: () => import("./lib/game10"),
    game12: () => import('./lib/game12'),
    game14: () => import('./lib/game14'),
    game15: () => import('./lib/game15'),
    game16: () => import('./lib/game16'),
    game17: () => import('./lib/game17'),
    game18: () => import('./lib/game18'),
    game22: () => import('./lib/game22'),
    game23: () => import('./lib/game23'),
    game24: () => import('./lib/game24'),
    game25: () => import('./lib/game25'),
    game26: () => import('./lib/game26'),
    game27: () => import('./lib/game27'),
    game28: () => import('./lib/game28'),
    game29: () => import('./lib/game29'),
    game30: () => import('./lib/game30'),
    game31: () => import('./lib/game31'),
    game34: () => import('./lib/game34'),
    game35: () => import('./lib/game35'),
    game37: () => import('./lib/game37'),
    game38: () => import('./lib/game38'),
    game39: () => import('./lib/game39'),
    game40: () => import('./lib/game40'),
    game41: () => import('./lib/game41'),
    game43: () => import('./lib/game43'),

    game11: () => import('./lib/game11'),
    game19: () => import('./lib/game19'),
    game20: () => import('./lib/game20'),
    game21: () => import('./lib/game21'),
    game32: () => import('./lib/game32'),
    game33: () => import('./lib/game33'),
    game50: () => import('./lib/game50'),
    game51: () => import('./lib/game51'),
    game52: () => import('./lib/game52'),
    game55: () => import('./lib/game55'),
    game56: () => import('./lib/game56'),
    game57: () => import('./lib/game57'),
    game59: () => import('./lib/game59'),
    game60: () => import('./lib/game60'),
    game61: () => import('./lib/game61'),
    game66: () => import('./lib/game66'),
    game67: () => import('./lib/game67'),
    game68: () => import('./lib/game68'),
    game69: () => import('./lib/game69'),
    game70: () => import('./lib/game70'),
    game71: () => import('./lib/game71'),
    game73: () => import('./lib/game73'),
    game76: () => import('./lib/game76'),
    game77: () => import('./lib/game77'),
    game80: () => import('./lib/game80'),
    game81: () => import('./lib/game81'),
    game82: () => import('./lib/game82'),
    game83: () => import('./lib/game83'),
    game84: () => import('./lib/game84'),
    game85: () => import('./lib/game85'),
    game99: () => import('./lib/game99'),
    game100: () => import('./lib/game100'),

    game9: () => import('./lib/game9'),
    game36: () => import('./lib/game36'),
    game42: () => import('./lib/game42'),
    game44: () => import('./lib/game44'),
    game45: () => import('./lib/game45'),
    game46: () => import('./lib/game46'),
    game47: () => import('./lib/game47'),
    game48: () => import('./lib/game48'),
    game72: () => import('./lib/game72'),
    game74: () => import('./lib/game74'),
    game92: () => import('./lib/game92'),
    game95: () => import('./lib/game95'),
    game96: () => import('./lib/game96'),
    game97: () => import('./lib/game97'),
    game101: () => import('./lib/game101'),
    game102: () => import('./lib/game102'),
    game103: () => import('./lib/game103'),
    game104: () => import('./lib/game104'),
    game105: () => import('./lib/game105'),
    game107: () => import('./lib/game107'),
    game108: () => import('./lib/game108'),
    game109: () => import('./lib/game109'),
    game110: () => import('./lib/game110'),
    game111: () => import('./lib/game111'),
    game112: () => import('./lib/game112'),
    game113: () => import('./lib/game113'),
    game114: () => import('./lib/game114'),
    game115: () => import('./lib/game115'),
    game116: () => import('./lib/game116'),
    game117: () => import('./lib/game117'),
    game118: () => import('./lib/game118'),
    game119: () => import('./lib/game119'),
    game120: () => import('./lib/game120'),
    game121: () => import('./lib/game121'),
    game122: () => import('./lib/game122'),
    game123: () => import('./lib/game123'),
    game124: () => import('./lib/game124'),
    game125: () => import('./lib/game125'),
    game126: () => import('./lib/game126'),
    game127: () => import('./lib/game127'),
    game128: () => import('./lib/game128'),
    game129: () => import('./lib/game129'),
    game130: () => import('./lib/game130'),
    game131: () => import('./lib/game131'),
    game132: () => import('./lib/game132'),
    game133: () => import('./lib/game133'),
    game134: () => import('./lib/game134'),
    game135: () => import('./lib/game135'),
    game141: () => import('./lib/game141'),
    game142: () => import('./lib/game142'),
    game143: () => import('./lib/game143'),
    game144: () => import("./lib/game144"),
    game145: () => import("./lib/game145"),
    game146: () => import("./lib/game146"),
    game147: () => import('./lib/game147'),
    game148: () => import('./lib/game148'),
    game149: () => import("./lib/game149"),
    game150: () => import("./lib/game150"),
    game151: () => import("./lib/game151"),
    game152: () => import("./lib/game152"),
    game153: () => import("./lib/game153"),
    game154: () => import("./lib/game154"),
    game155: () => import("./lib/game155"),
    game156: () => import("./lib/game156"),
    game65: () => import("./lib/game65"),

    game136: () => import('./lib/game136'),
    game137: () => import('./lib/game137'),
    game138: () => import('./lib/game138'),
    game139: () => import('./lib/game139'),
    game140: () => import('./lib/game140'),
    game157: () => import('./lib/game157'),
    game158: () => import('./lib/game158'),
    game159: () => import('./lib/game159'),
    game160: () => import('./lib/game160'),
    game161: () => import('./lib/game161'),
    game162: () => import('./lib/game162'),
    game163: () => import('./lib/game163'),
    game164: () => import('./lib/game164'),
    game165: () => import('./lib/game165'),
    game166: () => import('./lib/game166'),
    game167: () => import('./lib/game167'),
    game168: () => import('./lib/game168'),
    game169: () => import('./lib/game169'),
    game170: () => import('./lib/game170'),
    game171: () => import('./lib/game171'),
    game172: () => import('./lib/game172'),
    game173: () => import('./lib/game173'),
    game174: () => import('./lib/game174'),
    game175: () => import('./lib/game175'),
    game176: () => import('./lib/game176'),
    game177: () => import('./lib/game177'),
    game178: () => import('./lib/game178'),
    game179: () => import('./lib/game179'),
    game180: () => import('./lib/game180'),
    game181: () => import('./lib/game181'),
    game182: () => import('./lib/game182'),
    game183: () => import('./lib/game183'),
    game184: () => import('./lib/game184'),
    game185: () => import('./lib/game185'),
    game186: () => import('./lib/game186'),
    game187: () => import('./lib/game187'),
    game188: () => import('./lib/game188'),
    game189: () => import('./lib/game189'),
    game190: () => import('./lib/game190'),
    game191: () => import('./lib/game191'),
    game192: () => import('./lib/game192'),
    game193: () => import('./lib/game193'),
    game194: () => import('./lib/game194'),
    game195: () => import('./lib/game195'),
    game196: () => import('./lib/game196'),
    game197: () => import('./lib/game197'),
    game198: () => import('./lib/game198'),
    game199: () => import('./lib/game199'),
    game200: () => import('./lib/game200'),
    game201: () => import('./lib/game201'),
    game202: () => import('./lib/game202'),
    game203: () => import('./lib/game203'),
    game204: () => import('./lib/game204'),
    game205: () => import('./lib/game205'),
    game206: () => import('./lib/game206'),
    game207: () => import('./lib/game207'),
    game208: () => import('./lib/game208'),
    game209: () => import('./lib/game209'),
    game210: () => import('./lib/game210'),
    game211: () => import('./lib/game211'),
    game212: () => import('./lib/game212'),
    game213: () => import('./lib/game213'),
    game214: () => import('./lib/game214'),
    game215: () => import('./lib/game215'),
    game216: () => import('./lib/game216'),
    game217: () => import('./lib/game217'),
    game218: () => import('./lib/game218'),

    game219: () => import('./lib/game219'),
    game220: () => import('./lib/game220'),
    game221: () => import('./lib/game221'),
    game222: () => import('./lib/game222'),
    game223: () => import('./lib/game223'),
  },
  watch: {
    '$router': {
      handler() {
        console.log(111)
      },
      // deep:true,
      immediate: true
    },
    componentName() {
      this.cmpName = this.componentName
    }
  },
  data() {
    return {
      cmpName: "",
      info: {
        level: null
      }
    }
  },
  mounted() {
    this.getInfo(GetRequest().id)
    this.cmpName = this.$route.params.gameName
  },
  methods: {
    getInfo(id) {
      getAction('/train/cogTrainResult/getTrainInfo', { id: id }).then((res) => {
        if (res.success) {
          this.info = res.result
          this.info.id = id
        } else {
          this.$message.warning(res.message)
        }
      }).finally(() => {
      })
    }
  }
}
</script>

<style lang="scss">
.games-container {
  width: 100vw;
  height: 100vh;
  position: absolute;
  left: 0;
  top: 0;

  .page-content4 {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .content {
      padding-bottom: 200px;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      .number-bg {
        width: 1148px;
        position: relative;
        z-index: 1;
        background-image: url('/static/game_assets/game1/images/number-bg.png');
        background-size: 100% auto;
        background-repeat: no-repeat;
      }

      .number {
        position: absolute;
        z-index: 2;
        top: 100px;
        display: flex;
        align-items: flex-end;

        .value {
          font-size: 170px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          line-height: 150px;
          color: #ffffff;
          background-image: linear-gradient(124deg, #ff7b00 0%, #e82f2f 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .unit {
          font-size: 48px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 67px;
          background-image: linear-gradient(121deg, #ffc500 0%, #ff5353 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .info {
        width: 950px;
        position: absolute;
        z-index: 2;
        top: 400px;
      }
    }
  }
}
</style>
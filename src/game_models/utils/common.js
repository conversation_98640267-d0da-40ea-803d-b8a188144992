import { postAction } from '@/api/manage'

const save = (params) => {
  return postAction('/train/cogTrainResult/complete', params)
}

// 数组乱序
const shuffle = (arr) => {
  for (let i = arr.length; i; i--) {
    let j = Math.floor(Math.random() * i)
    const tab = arr[i - 1]
    arr[i - 1] = arr[j]
    arr[j] = tab
  }
  return arr;
}

// 随机数
const randomNum = (min, max) => {
  return Math.round(Math.random() * (max - min)) + min;
}

// 随机数组
const getRandomArray = (arr, count) => {
  let shuffled = arr.slice(0)
  let i = arr.length
  let min = i - count
  let temp, index;
  while (i-- > min) {
    index = Math.floor((i + 1) * Math.random());
    temp = shuffled[index];
    shuffled[index] = shuffled[i];
    shuffled[i] = temp;
  }
  return shuffled.slice(min);
}

const getTime = (seconds) => {
  let hour = Math.floor(seconds / 3600) >= 10 ? Math.floor(seconds / 3600) : '0' + Math.floor(seconds / 3600);
  seconds -= 3600 * hour;
  let min = Math.floor(seconds / 60) >= 10 ? Math.floor(seconds / 60) : '0' + Math.floor(seconds / 60);
  seconds -= 60 * min;
  let sec = seconds >= 10 ? seconds : '0' + seconds;
  if (min == '00') {
    return seconds + '秒';
  } else {
    return + min + '分' + sec + '秒';
  }

}

export default {
  save,
  shuffle,
  randomNum,
  getRandomArray,
  getTime
}
<template>
  <div class="game67-page">
    <div class="page-bg" :class="status > 1 ? 'page-bg2': 'page-bg1'"></div>
    <settingPage title="图片排序" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">请将呈现的图片按正确的顺序拖到相应的数字框内，然后描述这个故事。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="title">游戏名称</div>

      <div class="content">
        <img :class="['reset-icon', 'reset-icon-' + item]" v-for="item in 4" :key="item + 'icon'" src="/static/game_assets/game66/icon.png" @click="resetDragItem(item - 1)" />

        <div class="top-list">
          <draggable class="top-item" v-model="dragItemTop[index]" v-for="(item, index) in 4" :key="item" :sort="false" :group="group1">
            <img class="img" v-for="it in dragItemTop[index]" :key="it.name + 'top'" :src="`/static/game_assets/game67/item_${number}_${it.name}.png`" />
          </draggable>
        </div>

        <div class="bottom-list">
          <draggable class="bottom-item" v-model="dragItemBottom[item - 1]" v-for="item in 4" :key="item + 'number'" :sort="false" :group="group2" @add="dragAdd(item)">
            <img class="number" :src="`/static/game_assets/game66/item_bg_${item}.png`">
            <img class="img" v-for="it in dragItemBottom[item - 1]" :key="it.name + 'bottom'" :src="`/static/game_assets/game67/item_${number}_${it.name}.png`" />
          </draggable>
        </div>
      </div>

      <div class="footer">
        <div class="left">
          <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        </div>
        <div class="right">
          <img v-if="isShowSubmit" class="img3" src="/static/game_assets/common/submit.png" @click="submit">
          <img class="img2" src="/static/game_assets/common/reset.png" @click="reset">
        </div>
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
 
export default {
  name: 'game67',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
    draggable
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowSubmit: false,
      dragItemTop: [],
      dragItemBottom: [[], [], [], []],
      group1: {
        name: 'itemList',
        pull: true,
        put: false
      },
      group2: {
        name: 'itemList',
        pull: false,
        put: true
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time() {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.startProcess()
      this.status = 3
      // this.timing()
    },

    startProcess() {
      const list = [1, 2, 3, 4]
      const correctAnswer = api.shuffle(list)
      correctAnswer.forEach((item, index) => {
        this.dragItemTop.push([{
          name: item,
          startIndex: index,
        }])
      })
      this.number++
    },

    dragAdd(index) {
      if (this.dragItemBottom[index - 1].length > 1) {
        const item = this.dragItemBottom[index - 1].pop()
        this.dragItemTop[item.startIndex].push(item)
      }

      const list = this.dragItemBottom.filter(item => item.length)
      if (list.length >= 4) this.isShowSubmit = true
    },

    resetDragItem(index) {
      const item = this.dragItemBottom[index].pop()
      this.dragItemTop[item.startIndex].push(item)
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    goOn() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isShowSubmit = false
      const answer = this.dragItemBottom.map(item => (item[0] && item[0].name) || null)
      answer.forEach((item, index) => {
        if (item === index + 1) {
          this.succesNum++
        } else {
          this.errorNum++
        }
      })

      if (this.number < 3) {
        this.dragItemTop = []
        this.dragItemBottom = [[], [], [], []]
        this.startProcess()
      } else {
        this.isStop = true
        this.store = this.succesNum <= 4 ? this.succesNum * 9 : 36 + (this.succesNum - 4) * 8
        this.infos[0].value = this.second
        this.infos[1].value = this.succesNum
        this.infos[2].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: '',
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    reset() {
      this.dragItemBottom.forEach(item => {
        const it = item[0]
        if (it) this.dragItemTop[it.startIndex].push(it)
      })
      this.dragItemBottom = [[], [], [], []]

      if (this.isStop) {
        this.isStop = false
        this.timing()
      }
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.dragItemTop = []
      this.dragItemBottom = [[], [], [], []]
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game67-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background-image: url("/static/game_assets/game1/images/bg1.png");
  }
  .page-bg2 {
    background-image: url("/static/game_assets/game1/images/bg2.png");
  }

  .game-synopsis {
    width: 707px;
    height: 444px;
    margin: 34px;
    padding: 33px 30px;
    background: #fff;
    border: 2px solid #7BBD41;
    border-radius: 36px;

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #5E381F;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 18px;
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 400;
      color: #5E381F;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-content: center;

    .title {
      position: relative;
      padding: 47px 0;
      font-size: 42px;
      line-height: 59px;
      text-align: center;
      font-weight: 500;
      user-select: none;
    }

    .content {
      position: relative;
      width: 1866px;
      height: 738px;
      padding-left: 70px;
      padding-right: 77px;
      padding-bottom: 5px;
      display: flex;
      flex-direction: column;

      .reset-icon {
        position: absolute;
        top: 340px;
        width: 67px;
        height: 67px;
        border-radius: 50%;
        background: #F1FFC7;
        cursor: pointer;
      }

      .reset-icon-1 {
        left: 446px;
      }

      .reset-icon-2 {
        left: 897px;
      }

      .reset-icon-3 {
        left: 1348px;
      }

      .reset-icon-4 {
        right: 0;
      }

      .top-list {
        width: 100%;
        display: flex;
        justify-content: space-between;

        .top-item {
          position: relative;
          width: 366px;
          height: 371px;
          padding: 58px 55px;
          background: #fff;
          border-radius: 37px;

          .img {
            width: 255px;
            height: 255px;
            background: chocolate;
            cursor: pointer;
          }
        }
      }

      .bottom-list {
        width: 100%;
        padding-top: 14px;
        display: flex;
        justify-content: space-between;

        .bottom-item {
          position: relative;
          width: 366px;
          height: 348px;
          border-radius: 37px;

          .number {
            position: absolute;
            top: 0;
            left: 0;
            width: 366px;
            height: 348px;
          } 

          .img {
            position: absolute;
            bottom: 20px;
            left: 56px;
            width: 255px;
            height: 255px;
            background: chocolate;
          }
        }
      }
    }

    .footer {
      position: relative;
      display: flex;
      justify-content: space-between;
      width: 1509px;
      padding-bottom: 47px;
      margin: 0 auto;

      .left {
        width: 566px;
        display: inline-flex;
        justify-content: space-between;
      }

      .right {
        width: 564px;
        display: inline-flex;
        justify-content: flex-end;
      }

      .img1 {
        width: 270px;
        height: 115px;
        cursor: pointer;
      }

      .img2 {
        width: 268px;
        height: 115px;
        cursor: pointer;
      }

      .img3 {
        width: 267px;
        height: 115px;
        cursor: pointer;
        margin-right: 30px;
      }
    }
  }
}
</style>
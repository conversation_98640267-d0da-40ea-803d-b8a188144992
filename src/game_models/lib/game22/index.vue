<template>
  <div class="game22-page">
    <div class="page-bg" :class="status === 1 || status === 4 ? 'page-bg1': 'page-bg2'"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPage title="视听分配-混合" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、这是一组训练视觉与听觉并用能力的作业。</p>
        <p class="synopsis-content">2、听觉注意训练，您将听到一系列数字，每当前后听到两个数字相同时，请尽快点击下面左方向键按钮。</p>
        <p class="synopsis-content">3、视觉注意训练，您将看到一系列图形，每当连续出现两个图形相同时，请尽快点击下面右方向键按钮。</p>
        <p class="synopsis-content">4、在熟练操作上述两种应答方式基础上，进行混合训练。分别遵照2、3操作，注意左右的区别。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <img :class="['content', isShowClass ? 'content-opacity' : '']" :src="`/static/game_assets/game22/graph_${imgList[index]}.png`">        

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        <div class="btn-group">
          <img class="img" src="/static/game_assets/game22/left.png" @click="clickLeft">
          <img class="img" src="/static/game_assets/game22/right.png" @click="clickRight">
        </div>
      </div>
    </div>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'

export default {
  name: 'game22',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data() {
    return {
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,
      index: 0,
      imgList: [],
      audioList: [],
      answer1: [],
      answer2: [],
      musicUrl: '',
      isError: false,

      timer: null,
      isShowClass: false,
      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    getClass() {
      if (this.isStop) return
      if (this.index >= 20) {
        this.submit()
        return
      }

      setTimeout(() => {
        this.isShowClass = false
      }, 1800)

      this.timer = setTimeout(() => {
        this.timer = null
        this.isError = false
        this.next()
      }, 2000)
    },

    next() {
      this.index++
      this.isShowClass = true
      this.getClass()
      this.playAudio(`/static/game_assets/audio/game22/audio_${this.audioList[this.index]}.mp3`)
    },

    playAudio(url) {  
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$nextTick(() => {
        this.$refs.music.play()
        this.isPlay = true
      })
    },

    pauseAudio() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnded() {
      this.isPlay = false
    },

    start() {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess() {
      const list1 = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
      const list2 = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
      this.imgList = api.getRandomArray(list1, 15)
      this.audioList = api.getRandomArray(list2, 15)
      let repeatList1 = api.getRandomArray(this.imgList, 5)
      let repeatList2 = api.getRandomArray(this.audioList, 5)

      for(let i = 0; i < 20; i++) {
        const item = this.imgList[i]
        if (repeatList1.includes(item)) {
          this.imgList.splice(i, 0, item)
          repeatList1 = repeatList1.filter(it => it !== item)
        }
      }

      for(let i = 0; i < 20; i++) {
        const item = this.audioList[i]
        if (repeatList2.includes(item)) {
          this.audioList.splice(i, 0, item)
          repeatList2 = repeatList2.filter(it => it !== item)
        }
      }
      
      setTimeout(() => {
        this.isShowClass = true
        this.getClass()
        this.playAudio(`/static/game_assets/audio/game22/audio_${this.audioList[this.index]}.mp3`)
      }, 200)
    },

    stop() {
      this.isStop = true
      this.show = true
      this.isPlay && this.pauseAudio()
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
    },

    clickLeft() {
      const item = this.audioList[this.index]
      if (this.answer2.includes(item)) return
      if (item === this.audioList[this.index - 1]) {
        this.succesNum++
        this.answer2.push(item)
      } else if (!this.isError) {
        this.errorNum++
        this.isError = true
      }
    },

    clickRight() {
      const item = this.imgList[this.index]
      if (this.answer1.includes(item)) return
      if (item === this.imgList[this.index - 1]) {
        this.succesNum++
        this.answer1.push(item)
      } else if (!this.isError) {
        this.errorNum++
        this.isError = true
      }
    },

    submit() {
      this.isStop = true
      this.errorNum = this.errorNum + 10 - this.succesNum
      this.store = parseInt(100 / (this.succesNum + this.errorNum) * this.succesNum)
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
      this.$refs.music.play()
      if (this.index < 19) {
        setTimeout(() => {
          this.next()
        }, 1000)
      }
    },

    again() {
      this.answer1 = []
      this.answer2 = []
      this.imgList = []
      this.audioList = []
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.index = 0
      this.store = 0
      this.isStop = false
      this.isShowClass = false
      this.isPlay = false
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game22-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background-image: url("/static/game_assets/game22/bg_1.png");
  }
  .page-bg2 {
    background: url("/static/game_assets/game22/bg_3.png") center center no-repeat;
    background-size: cover;
  }

  .game-synopsis {
    width: 1576px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      width: 483px;
      height: 483px;
      margin-bottom: 66px;
      margin-right: 77px;
      opacity: 0;
    }

    .content-opacity {
      opacity: 1;
      transition: opacity 1.8s;
    }

    .footer {
      position: absolute;
      bottom: 40px;
      display: flex;
      justify-content: space-between;
      width: 1509px;

      .img1 {
        width: 270px;
        height: 115px;
        margin: 21px 0;
        cursor: pointer;
      }

      .btn-group {
        width: 348px;
        height: 167px;
        display: flex;
        justify-content: space-between;

        .img {
          width: 154px;
          height: 167px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
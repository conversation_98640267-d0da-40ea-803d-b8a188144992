<template>
  <div class="game1-page">
    <div id="page-bg" :class="status > 1 ? 'page-bg2': 'page-bg1'"></div>
    <SettingPage @next="status = 1" v-if="status == 0" :title="gameData.title" :gameData="gameData"
      :info="gameData.info" :formData="formData" :musicUrl="'/static/game_assets/game1/mp3/project_mp3.mp3'" />
    <div class="page-content page-content2" v-else-if="status == 1">
      <div class="num">
        <div class="img"></div>
      </div>
    </div>
    <div class="page-content page-content3" v-else-if="status == 2">
      <div class="info_container">
        <div class="info">
          <div class="item time">
            <img src="/static/game_assets/game1/images/icon1.png" />
            <div class="label">{{getTime(time)}}</div>
          </div>
          <div class="music" :class="formData.sound?'type1':'type2'" @click="formData.sound = !formData.sound"></div>
        </div>
      </div>
      <div class="answer">
        <div v-for="(item,index) of question"
          :style="{width:`${100/formData.gridCol}%`,height:`${100/(question.length/formData.gridCol)}%`}" :key="index"
          class="item" :class="answer == index?'current':''" @click="clickAnswer(index)">
          <div class="box">
            <img class="pic" :src="item" />
            <img class="icon" src="/static/game_assets/game1/images/scuess-icon.png" />
          </div>
        </div>
      </div>
      <div class="actions">
        <img src="/static/game_assets/game1/images/btn7.png" class="btn" alt="" @click="submit">
      </div>
    </div>
    <ResultPage :info="'这是一个游戏'" :fraction="number" v-else-if="status == 3" @reset="reset" @nextPage="nextPage" />
  </div>
</template>

<script>
import gameData from "./data/data.json"
import ResultPage from "../../resultPage.vue"
import SettingPage from "../../settingPage.vue"
import Action from "../../action.vue"
export default {
  name: 'App',
  props: ['info'],
  components: { ResultPage, SettingPage },
  mixins: [Action],
  data () {
    let formData = {}
    for (const item of gameData.info) {
      formData[item.key] = item.default
      if (item.key == 'difficulty') {
        formData.gridCol = item.col
      }
    }
    return {
      number: 0,
      status: 0,
      gameData,
      formData,
      answer: null,
      time: 0,
      question: [],
      questionAnswer: null,
      stop: false
    }
  },
  watch: {
    status (newVal, oldVal) {
      if (this.status == 1) {
        setTimeout(() => {
          console.log(this.currentDifficulty)
          this.currentDifficulty = this.gameData.gameInfo
          this.setQuestion(this.formData.difficulty)
          this.status = 2
          let fun = () => {
            if (!this.stop) {
              this.time++;
              setTimeout(() => {
                fun()
              }, 1000);
            }
          }
          setTimeout(() => {
            fun()
          }, 1000);
        }, 3000);
      } else if (this.status == 3) {

      }
    },
  },
  mounted () {
  },
  methods: {
    reset () {
      this.stop = false;
      this.answer = null;
      this.time = 0
      this.status = 0;
    },
    submit () {
      this.stop = true;
      if (this.answer + 1 == this.questionAnswer) {
        this.number = 100
      } else {
        this.number = 0
      }
      this.save({ id: this.info.id, grade: "1", time: this.time, totalPoints: this.number })
      this.status = 3;
    },
    clickAnswer (index) {
      this.answer = index
    },
    setQuestion () {
      const { question, answer } = this.currentDifficulty
      const { difficulty } = this.formData
      this.questionAnswer = answer[difficulty]
      this.question = question[difficulty]
    },
    getTime (time) {
      let t = (time / 60 + '').split('.')
      let s = "00"
      if (t[1]) {
        s = (('0.' + t[1] - 0) * 60).toFixed(0)
      }
      if (s - 0 < 10 && s != "00") {
        s = "0" + s
      }
      return `${t[0]}:${s}`
    },

  },

}
</script>

<style lang="scss">
@import './index.scss';
</style>

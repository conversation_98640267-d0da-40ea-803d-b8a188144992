<template>
  <div class="game9-page">
    <div class="page-bg"></div>
    <settingPage @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、注意的广度又称注意的范围，是指在同一时间内一个人能看清楚地把握注意对象的数量。</p>
        <p class="synopsis-content">2、本组训练将有助于扩大您的注意范围，进而有助于提高学习和工作的效率。</p>
        <p class="synopsis-content">3、训练方法：笑脸将随机出现在不同的窗户上，您要按照呈现的位置，把它找出来。</p>
        <p class="synopsis-content">4、训练等级：依据注意的数量和目标呈现的时间长短分为六个等级。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <div class="item" v-for="item in 18" :key="item" @click="chooseItem(item)">
          <img class="item-img" src="/static/game_assets/game9/item.png">
          <img v-if="answerStatus === 'answer' && answer.includes(item)" class="item-icon" src="/static/game_assets/game9/icon.png"/>
          <img v-if="answerStatus === 'topic' && chooseArr.includes(item)" class="item-icon" src="/static/game_assets/game9/icon.png"/>
        </div>
      </div>
      
      <div v-show="answerStatus !== 'topic'" :class="['btn-group']">
        <div class="btn" @click="stop">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>

        <div v-show="answer.length && !showJudge" class="btn" @click="confirm">
          <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
          <span class="text">确定</span>
        </div>

        <template v-if="showJudge">
          <div class="btn" @click="goOn">
            <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
            <span class="text">继续训练</span>
          </div>
          <div class="btn" @click="reset">
            <img class="bg" src="/static/game_assets/game8/brown_bg.png">
            <span class="text">重做</span>
          </div>
        </template>
      </div>

      <div class="judge" v-if="showJudge">
        <img class="img" :src="`/static/game_assets/game8/${isCorrect ? 'success' : 'error'}.png`">
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 游戏分为6个等级，每轮游戏3个回合
// 1级：1-3个笑脸，时间1.5s
// 2级：1-3个笑脸，时间1s
// 3级：4-7个笑脸，时间1.5s
// 4级：4-7个笑脸，时间1s
// 5级：8-12个笑脸，时间1.5s
// 6级：8-12个笑脸，时间1s

export default {
  name: 'game9',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      show: false,
      status: 1,
      isStop: false,

      answerStatus: 'topic', // topic -- 题目 answer -- 回答
      answer: [],
      isCorrect: false,
      showJudge: false,
      chooseArr: [], // 答案位置
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    // 笑脸个数计算
    startProcess() {
      const list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]

      switch(this.level) {
        case 1: 
        case 2: {
          const randomNumber = api.randomNum(1, 3)
          this.chooseArr = api.getRandomArray(list, randomNumber)
          break
        }
        case 3:
        case 4: {
          const randomNumber = api.randomNum(4, 7)
          this.chooseArr = api.getRandomArray(list, randomNumber)
          break
        }
        case 5:
        case 6: {
          const randomNumber = api.randomNum(8, 12)
          this.chooseArr = api.getRandomArray(list, randomNumber)
          break
        }
        default:
          break
      }

      const time = this.level % 2 ? 1500 : 1000
      setTimeout(() => {
        this.answerStatus = 'answer'
      }, time)
    },

    chooseItem(item) {
      if (!this.answer.includes(item)) this.answer.push(item)
    },

    confirm() {
      this.showJudge = true
      if (this.answer.length === this.chooseArr.length) this.isCorrect = true
      this.answer.forEach(item => {
        if (!this.chooseArr.includes(item)) this.isCorrect = false
      })
      if (this.isCorrect) {
        this.succesNum++
      } else {
        this.errorNum++
      }
    },

    goOn() {
      this.showJudge = false
      this.number++
      if (this.number >= 3) {
        this.store = this.succesNum * 30 + 10
        this.isStop = true
        this.infos[0].value = this.level
        this.infos[1].value = this.second
        this.infos[2].value = this.succesNum
        this.infos[3].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: this.level,
          time: this.second,
          totalPoints: this.store
        }
      } else {
        this.answerStatus = 'topic'
        this.answer = []
        this.startProcess()
      }
    },

    reset() {
      this.showJudge = false
      this.answer = []
      if (this.isCorrect) {
        this.succesNum--
      } else {
        this.errorNum--
      }
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    again() {
      this.number = 0
      this.second = 0
      this.store = 0
      this.errorNum = 0
      this.succesNum = 0
      this.isStop = false
      this.isCorrect = false
      this.answerStatus = 'topic'
      this.answer = []
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game9-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game8/bg.png");
  }

  .game-synopsis {

    .synopsis-content {
      padding-top: 20px;
      margin: 0;
      font-size: 30px;
      line-height: 42px;
      font-weight: 400;
      color: #A83A01;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .content {
      width: 1400px;
      height: 664px;
      margin-bottom: 90px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;

      .item {
        position: relative;
        width: 201px;
        height: 188px;
        margin: 0 15px;
        cursor: pointer;

        .item-img {
          position: absolute;
          top: 0;
          left: 0;
          width: 201px;
        }

        .item-icon {
          position: absolute;
          left: 23px;
          bottom: 31px;
          width: 32px;
        }
      }
    }

    .btn-group {
      position: absolute;
      bottom: 82px;
      width: 1554px;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px;
        height: 84px;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px;
          height: 84px;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px 0 21px 0;
          font-size: 38px;
          line-height: 53px;
          text-align: center;
          color: #A83A01;
        }
      }
    }

    .judge {
      position: absolute;
      width: 460px;
      height: 460px;

      .img {
        position: absolute;
        width: 460px;
      }
    }
  }
}
</style>
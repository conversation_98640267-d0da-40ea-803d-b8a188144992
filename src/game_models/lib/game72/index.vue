<template>
  <div class="game72-page">
    <div class="page-bg" :class="status === 1 || status === 4 ? 'page-bg1': 'page-bg2'"></div>
    <settingPage title="立体方块" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、屏幕上有一些立体积 木方块 </p>
        <p class="synopsis-content">2、请您尽快确定这些立体积方块的数量。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <div class="content-top">
          <template v-for="item in 10">
            <img v-if="current === 1" :class="['top-item', 'top-item-1-' + item]" :key="item + 'top'" :src="`/static/game_assets/game72/item_${btnType}.png`" />
          </template>
          <template v-for="item in 5">
            <img v-if="current === 2" :class="['top-item', 'top-item-2-' + item]" :key="item + 'top'" :src="`/static/game_assets/game72/item_${btnType}.png`" />
          </template>
          <template v-for="item in 5">
            <img v-if="current === 3" :class="['top-item', 'top-item-3-' + item]" :key="item + 'top'" :src="`/static/game_assets/game72/item_${btnType}.png`" />
          </template>
          <template v-for="item in 6">
            <img v-if="current === 4" :class="['top-item', 'top-item-4-' + item]" :key="item + 'top'" :src="`/static/game_assets/game72/item_${btnType}.png`" />
          </template>
          <template v-for="item in 10">
            <img v-if="current === 5" :class="['top-item', 'top-item-5-' + item]" :key="item + 'top'" :src="`/static/game_assets/game72/item_${btnType}.png`" />
          </template>
          <template v-for="item in 8">
            <img v-if="current === 6" :class="['top-item', 'top-item-6-' + item]" :key="item + 'top'" :src="`/static/game_assets/game72/item_${btnType}.png`" />
          </template>
          <template v-for="item in 7">
            <img v-if="current === 7" :class="['top-item', 'top-item-7-' + item]" :key="item + 'top'" :src="`/static/game_assets/game72/item_${btnType}.png`" />
          </template>
          <template v-for="item in 9">
            <img v-if="current === 8" :class="['top-item', 'top-item-8-' + item]" :key="item + 'top'" :src="`/static/game_assets/game72/item_${btnType}.png`" />
          </template>
          <template v-for="item in 11">
            <img v-if="current === 9" :class="['top-item', 'top-item-9-' + item]" :key="item + 'top'" :src="`/static/game_assets/game72/item_${btnType}.png`" />
          </template>
          <template v-for="item in 12">
            <img v-if="current === 10" :class="['top-item', 'top-item-10-' + item]" :key="item + 'top'" :src="`/static/game_assets/game72/item_${btnType}.png`" />
          </template>
        </div>

        <div class="content-bottom">
          <div class="bottom-item" v-for="item in answerList" :key="item + 'bottom'" @click="chooseItem(item)">
            <img class="item-bg" src="/static/game_assets/game72/btn_bg1.png" />
            <img v-if="choose === item" class="item-bg" src="/static/game_assets/game72/btn_bg2.png" />
            <span class="item-text">{{item}}</span>
          </div>
        </div>
      </div>    

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        <img v-if="choose" class="img2" src="/static/game_assets/common/continue.png" @click="goon">
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 分为三个等级
// 1级 4
// 2级 6
// 3级 8

export default {
  name: 'game72',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,
      btnType: 0,
      current: 0,
      data: [10, 5, 5, 6, 10, 8, 7, 9, 11, 12],
      questionList: [],
      answerList: [],
      choose: 0,

     isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
      const list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
      this.questionList = api.getRandomArray(list, this.level * 2 + 2)
      // this.timing()
      this.startProcess()
    },

    startProcess() {
      this.current = this.questionList[this.number]
      this.btnType = api.randomNum(1, 5)
      this.number++

      this.answerList = []
      const index = api.randomNum(0, 3)
      const num = this.data[this.current - 1]
      if (index === 0) {
        this.answerList.push(num)
        this.answerList.push(num + 1)
        this.answerList.push(num + 2)
        this.answerList.push(num + 3)
      } else if (index === 1) {
        this.answerList.push(num - 1)
        this.answerList.push(num)
        this.answerList.push(num + 1)
        this.answerList.push(num + 2)
      } else if (index === 2) {
        this.answerList.push(num - 2)
        this.answerList.push(num - 1)
        this.answerList.push(num)
        this.answerList.push(num + 1)
      } else if (index === 3) {
        this.answerList.push(num - 3)
        this.answerList.push(num - 2)
        this.answerList.push(num - 1)
        this.answerList.push(num)
      }
    },

    chooseItem(item) {
      if (this.isShowCorrect || this.isShowError) return
      this.choose = item
    },

    goon() {
      if (this.isShowCorrect || this.isShowError) return
      if (this.choose === this.data[this.current - 1]) {
        this.isShowCorrect = true
        this.succesNum++
      } else {
        this.isShowError = true
        this.errorNum++
      }

      setTimeout(() => {
        this.isShowCorrect = false
        this.isShowError = false

        if (this.number < this.questionList.length) {
          this.choose = 0
          this.startProcess() 
        } else {
          this.isStop = true
          this.store = this.level === 1 ? 25 * this.succesNum : (this.succesNum > 0 ? this.succesNum * (this.level === 2 ? 16 : 12) + 4 : 0)
          this.infos[0].value = this.level
          this.infos[1].value = this.second
          this.infos[2].value = this.succesNum
          this.infos[3].value = this.errorNum
          this.status = 4
          this.params = {
            id: this.info.id,
            grade: this.level,
            time: this.second,
            totalPoints: this.store
          }
        }
      }, 800)
    },

    stop() {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.questionList = []
      this.answerList = []
      this.choose = 0
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game72-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background-image: url("/static/game_assets/game22/bg_1.png");
  }
  .page-bg2 {
    background: url("/static/game_assets/game72/bg.png") center center no-repeat;
    background-size: cover;
  }

  .game-synopsis {
    width: 1576px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      width: 840px;
      height: 720px;
      margin-bottom: 54px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .content-top {
        position: relative;

        .top-item {
          position: absolute;
          width: 235px;
          height: 220px;
        }

        .top-item-1-1 {
          top: 220px;
          left: 97px;
          z-index: 10;
        }

        .top-item-1-2 {
          top: 295px;
          left: 97px;
          z-index: 8;
        }

        .top-item-1-3 {
          top: 75px;
          left: 213px;
          z-index: 7;
        }

        .top-item-1-4 {
          top: 150px;
          left: 213px;
          z-index: 6;
        }

        .top-item-1-5 {
          top: 225px;
          left: 213px;
          z-index: 5;
        }

        .top-item-1-6 {
          top: 0px;
          left: 446px;
          z-index: 4;
        }

        .top-item-1-7 {
          top: 75px;
          left: 446px;
          z-index: 3;
        }

        .top-item-1-8 {
          top: 150px;
          left: 446px;
          z-index: 2;
        }

        .top-item-1-9 {
          top: 225px;
          left: 446px;
          z-index: 1;
        }

        .top-item-1-10 {
          top: 293px;
          left: 330px;
          z-index: 9;
        }

        .top-item-2-1 {
          top: 150px;
          left: 213px;
          z-index: 4;
        }

        .top-item-2-2 {
          top: 225px;
          left: 213px;
          z-index: 3;
        }

        .top-item-2-3 {
          top: 150px;
          left: 446px;
          z-index: 2;
        }

        .top-item-2-4 {
          top: 225px;
          left: 446px;
          z-index: 1;
        }

        .top-item-2-5 {
          top: 293px;
          left: 330px;
          z-index: 5;
        }

        .top-item-3-1 {
          top: 95px;
          left: 325px;
          z-index: 3;
        }

        .top-item-3-2 {
          top: 160px;
          left: 325px;
          z-index: 2;
        }

        .top-item-3-3 {
          top: 230px;
          left: 70px;
          z-index: 1;
        }

        .top-item-3-4 {
          top: 225px;
          left: 446px;
          z-index: 4;
        }

        .top-item-3-5 {
          top: 293px;
          left: 330px;
          z-index: 5;
        }

        .top-item-4-1 {
          top: 95px;
          left: 325px;
          z-index: 3;
        }

        .top-item-4-2 {
          top: 170px;
          left: 325px;
          z-index: 2;
        }

        .top-item-4-3 {
          top: 293px;
          left: 330px;
          z-index: 5;
        }

        .top-item-4-4 {
          top: 218px;
          left: 330px;
          z-index: 6;
        }

        .top-item-4-5 {
          top: 95px;
          left: 93px;
          z-index: 6;
        }

        .top-item-4-6 {
          top: 170px;
          left: 93px;
          z-index: 5;
        }

        .top-item-5-1 {
          top: 95px;
          left: 93px;
          z-index: 6;
        }

        .top-item-5-2 {
          top: 170px;
          left: 93px;
          z-index: 1;
        }

        .top-item-5-3 {
          top: 95px;
          left: 325px;
          z-index: 4;
        }

        .top-item-5-4 {
          top: 170px;
          left: 325px;
          z-index: 2;
        }

        .top-item-5-5 {
          top: 28px;
          left: 442px;
          z-index: 3;
        }

        .top-item-5-6 {
          top: 103px;
          left: 442px;
          z-index: 1;
        }

        .top-item-5-7 {
          top: 293px;
          left: 330px;
          z-index: 9;
        }

        .top-item-5-8 {
          top: 218px;
          left: 330px;
          z-index: 10;
        }

        .top-item-5-9 {
          top: 151px;
          left: 447px;
          z-index: 8;
        }

        .top-item-5-10 {
          top: 226px;
          left: 447px;
          z-index: 7;
        }

        .top-item-6-1 {
          top: 95px;
          left: 93px;
          z-index: 6;
        }

        .top-item-6-2 {
          top: 170px;
          left: 93px;
          z-index: 1;
        }

        .top-item-6-3 {
          top: 95px;
          left: 325px;
          z-index: 4;
        }

        .top-item-6-4 {
          top: 170px;
          left: 325px;
          z-index: 2;
        }

        .top-item-6-5 {
          top: 28px;
          left: 442px;
          z-index: 3;
        }

        .top-item-6-6 {
          top: 103px;
          left: 442px;
          z-index: 1;
        }

        .top-item-6-7 {
          top: 163px;
          left: 442px;
          z-index: 8;
        }

        .top-item-6-8 {
          top: 238px;
          left: 442px;
          z-index: 7;
        }

        .top-item-7-1 {
          top: 95px;
          left: 93px;
          z-index: 6;
        }

        .top-item-7-2 {
          top: 170px;
          left: 93px;
          z-index: 1;
        }

        .top-item-7-3 {
          top: 238px;
          left: 442px;
          z-index: 7;
        }

        .top-item-7-4 {
          top: 170px;
          left: 325px;
          z-index: 2;
        }

        .top-item-7-5 {
          top: 305px;
          left: 558px;
          z-index: 9;
        }

        .top-item-7-6 {
          top: 103px;
          left: 442px;
          z-index: 1;
        }

        .top-item-7-7 {
          top: 163px;
          left: 442px;
          z-index: 8;
        }

        .top-item-8-1 {
          top: 218px;
          left: 430px;
          z-index: 9;
        }

        .top-item-8-2 {
          top: 293px;
          left: 430px;
          z-index: 8;
        }

        .top-item-8-3 {
          top: 151px;
          left: 547px;
          z-index: 7;
        }

        .top-item-8-4 {
          top: 226px;
          left: 547px;
          z-index: 6;
        }

        .top-item-8-5 {
          top: 28px;
          left: 542px;
          z-index: 3;
        }

        .top-item-8-6 {
          top: 103px;
          left: 542px;
          z-index: 1;
        }

        .top-item-8-7 {
          top: 110px;
          left: 310px;
          z-index: 2;
        }

        .top-item-8-8 {
          top: 178px;
          left: 193px;
          z-index: 3;
        }

        .top-item-8-9 {
          top: 246px;
          left: 76px;
          z-index: 4;
        }

        .top-item-9-1 {
          top: 218px;
          left: 430px;
          z-index: 9;
        }

        .top-item-9-2 {
          top: 293px;
          left: 430px;
          z-index: 8;
        }

        .top-item-9-3 {
          top: 151px;
          left: 547px;
          z-index: 7;
        }

        .top-item-9-4 {
          top: 226px;
          left: 547px;
          z-index: 6;
        }

        .top-item-9-5 {
          top: 28px;
          left: 542px;
          z-index: 3;
        }

        .top-item-9-6 {
          top: 103px;
          left: 542px;
          z-index: 1;
        }

        .top-item-9-7 {
          top: 110px;
          left: 310px;
          z-index: 2;
        }

        .top-item-9-8 {
          top: 178px;
          left: 193px;
          z-index: 4;
        }

        .top-item-9-9 {
          top: 246px;
          left: 76px;
          z-index: 4;
        }

        .top-item-9-10 {
          top: 35px;
          left: 310px;
          z-index: 3;
        }

        .top-item-9-11 {
          top: 171px;
          left: 76px;
          z-index: 4;
        }

        .top-item-10-1 {
          top: 42px;
          left: 427px;
          z-index: 1;
        }

        .top-item-10-2 {
          top: 293px;
          left: 430px;
          z-index: 8;
        }

        .top-item-10-3 {
          top: 151px;
          left: 547px;
          z-index: 7;
        }

        .top-item-10-4 {
          top: 226px;
          left: 547px;
          z-index: 6;
        }

        .top-item-10-5 {
          top: 28px;
          left: 542px;
          z-index: 3;
        }

        .top-item-10-6 {
          top: 103px;
          left: 542px;
          z-index: 1;
        }

        .top-item-10-7 {
          top: 110px;
          left: 310px;
          z-index: 3;
        }

        .top-item-10-8 {
          top: 178px;
          left: 193px;
          z-index: 4;
        }

        .top-item-10-9 {
          top: 246px;
          left: 76px;
          z-index: 4;
        }

        .top-item-10-10 {
          top: 171px;
          left: 76px;
          z-index: 5;
        }

        .top-item-10-11 {
          top: -37px;
          left: 427px;
          z-index: 2;
        }

        .top-item-10-12 {
          top: 296px;
          left: 663px;
          z-index: 9;
        }
      }

      .content-bottom {
        display: flex;
        justify-content: space-between;
        width: 840px;

        .bottom-item {
          position: relative;
          width: 180px;
          height: 127px;
          cursor: pointer;

          .item-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 180px;
            height: 127px;
          }

          .item-text {
            position: relative;
            display: block;
            padding-bottom: 22px;
            font-size: 52px;
            line-height: 105px;
            text-align: center;
            font-family: Impact;
            color: #eee;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 57px;
      display: flex;
      justify-content: space-between;
      width: 1752px;
      padding-left: 105px;

      .img1 {
        height: 115px;
        cursor: pointer;
      }

      .img2 {
        height: 115px;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px;
      margin-bottom: 40px;
      z-index: 99;
    }
  }
}
</style>
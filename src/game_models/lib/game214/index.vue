<template>
  <div class="game214-page">
    <div class="page-bg"></div>
    <settingPage title="符号理解" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、本项目考察计算符号的理解，题型是多选题。</p>
        <p class="synopsis-content">2、一次四道题目全部答对可以终止这一项训练。</p>
        <p class="synopsis-content">3、有错误者复习以下答案，再训练一次。</p>
        <p class="synopsis-content">4、三次练习后仍有错误也不再重新练习，而是继续做其他项目，次日接着训练本一项目。</p>
      </div>
    </settingPage>

    <div class="game-content" v-if="status === 3">
      <div class="title">
        <img class="title-bg" src="/static/game_assets/game206/title_bg.png" />
        <span class="title-text">符号理解</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game214/content_bg.png" />
        <img class="content-icon" src="/static/game_assets/game214/icon.png" />
        <div class="content-title">{{`找出所有${type === 1 ? '加' : type === 2 ? '减' : type === 3 ? '乘' : '除'}法的式子`}}</div>

        <div :class="['content-main', level === 1 && 'content-small']">
          <div class="content-item" v-for="(item, index) in currect.answerList" :key="index + 'item'" @click="chooseItem(item.index)">
            <img class="item-bg" src="/static/game_assets/game214/btn_bg_1.png" />
            <img class="item-bg" v-if="answer.includes(item.index)" src="/static/game_assets/game214/btn_bg_2.png" />
            <span class="item-text">{{item.text}}</span>
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="footer-left">
          <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
          <img v-show="showNext" class="img" src="/static/game_assets/common/next.png" @click="toNext" />
          <img v-show="answer.length && !showNext" class="img" src="/static/game_assets/common/confirm.png" @click="confirm" />
        </div>
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 10道题 3个等级
// 1级 1个答案
// 2级 1-2个答案
// 3级 1-3个答案

export default {
  name: 'game214',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      level: 1,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,
      showNext: false,

      type: 1, // 训练模式
      answer: [],
      currect: {
        answer: [],
        answerList: []
      },  
      

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess() {
      this.currect.answerList = []
      this.currect.answer = []
      this.type = api.getRandomArray([1, 2, 3, 4], 1)[0]
      const arr = ['+', '-', '×', '÷']

      if (this.level === 1) {
        let item = ''
        for (let i = 0; i < 4; i++) {
          if (i === 0 || i === 2) {
            item = `${api.randomNum(1, 99)} ${arr[i]} ${api.randomNum(1, 99)}`
          } else if (i === 1) {
            const num1 = api.randomNum(1, 99)
            const num2 = api.randomNum(1, 99)
            item = `${Math.max(num1, num2)} ${arr[i]} ${Math.min(num1, num2)}`
          } else {
            const num1 = api.randomNum(1, 20)
            const num2 = api.randomNum(1, 10) * num1
            item = `${Math.max(num1, num2)} ${arr[i]} ${Math.min(num1, num2)}`
          }
          this.currect.answerList.push({
            text: item,
            index: i
          })
          this.currect.answer.push(this.type - 1)
        }
      } else if (this.level === 2) {
        const num = api.randomNum(1, 2)
        const list1 = arr.filter((item, index) => this.type !== index + 1)
        let list = []
        for (let j = 0; j < num; j++) {
          list.push(arr[this.type - 1])
          this.currect.answer.push(j)
        }
        list = list.concat(list1).concat(api.getRandomArray(list1, 3 - num))
        let item = ''
        for (let i = 0; i < 6; i++) {
          if (list[i] === '+' || list[i] === '×') {
            item = `${api.randomNum(1, 99)} ${list[i]} ${api.randomNum(1, 99)}`
          } else if (list[i] === '-') {
            const num1 = api.randomNum(1, 99)
            const num2 = api.randomNum(1, 99)
            item = `${Math.max(num1, num2)} ${list[i]} ${Math.min(num1, num2)}`
          } else {
            const num1 = api.randomNum(1, 20)
            const num2 = api.randomNum(1, 10) * num1
            item = `${Math.max(num1, num2)} ${list[i]} ${Math.min(num1, num2)}`
          }
          this.currect.answerList.push({
            text: item,
            index: i
          })
        }
      } else {
        const num = api.randomNum(1, 3)
        const list1 = arr.filter((item, index) => this.type !== index + 1)
        let list = []
        for(let j = 0; j < num; j++) {
          list.push(arr[this.type - 1])
          this.currect.answer.push(j)
        }
        list = list.concat(list1).concat(api.getRandomArray(list1.concat(list1), 6 - num))
        let item = ''
        for (let i = 0; i < 9; i++) {
          if (list[i] === '+' || list[i] === '×') {
            item = `${api.randomNum(1, 99)} ${list[i]} ${api.randomNum(1, 99)}`
          } else if (list[i] === '-') {
            const num1 = api.randomNum(1, 99)
            const num2 = api.randomNum(1, 99)
            item = `${Math.max(num1, num2)} ${list[i]} ${Math.min(num1, num2)}`
          } else {
            const num1 = api.randomNum(1, 20)
            const num2 = api.randomNum(1, 10) * num1
            item = `${Math.max(num1, num2)} ${list[i]} ${Math.min(num1, num2)}`
          }
          this.currect.answerList.push({
            text: item,
            index: i
          })
        }
      }
      this.currect.answerList = api.shuffle(this.currect.answerList)
    },

    chooseItem(index) {
      if (this.answer.includes(index) || this.isShowCorrect || this.isShowError) return
      this.answer.push(index)
    },

    confirm() {
      let flag = false
      this.currect.answer.forEach(item => {
        if (!this.answer.includes(item)) flag = true
      })
      if (new Set(this.currect.answer).size !== this.answer.length) flag = true
      if (!flag) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }
      
      this.showNext = true
    },

    toNext() {
      this.showNext = false
      this.isShowCorrect = false
      this.isShowError = false
      this.answer = []
      this.number++
      if (this.number >= 10) {
        this.submit()
      } else {
        this.startProcess()
      }
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isStop = true
      this.store = this.succesNum * 10
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.showNext = false
      this.answer = []
      this.currect = {
        answer: [],
        answerList: []
      },
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game214-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game126/bg.png");
  } 

  .game-synopsis {
    width: 1605px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .title {
      position: absolute;
      top: 0;
      width: 932px;
      height: 125px;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px;
        height: 125px;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 28px 0 56px 0;
        font-size: 36px;
        line-height: 36px;
        text-align: center;
        color: #1B1D2D;
      }
    }

    .content {
      position: relative;
      width: 1857px;
      height: 855px;
      margin-top: 150px;
      padding: 205px 360px 140px 310px;
      display: flex;
      justify-content: center;
      align-content: center;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1857px;
      }

      .content-icon {
        position: absolute;
        bottom: 114px;
        left: 217px;
        width: 214px;
      }

      .content-title {
        position: absolute;
        width: 100%;
        top: 94px;
        left: 0;
        padding-right: 48px;
        font-size: 30px;
        text-align: center;
        line-height: 30px;
        color: #312B4F;
      }

      .content-main {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-content: flex-start;

        .content-item {
          position: relative;
          width: 383px;
          height: 168px;
          padding: 15px;
          cursor: pointer;

          .item-bg {
            position: absolute;
            top: 15px;
            left: 15px;
            width: 353px;
          }

          .item-text {
            position: relative;
            display: block;
            font-size: 77px;
            line-height: 138px;
            text-align: center;
            color: #1B1D2D;
          }
        }
      }

      .content-small {
        width: 770px;
      }
    }

    .footer {
      position: absolute;
      bottom: 72px;
      display: flex;
      justify-content: space-between;
      width: 1480px;

      .footer-left {
        width: 620px;
        display: inline-flex;
        justify-content: space-between;
      }

      .img {
        height: 115px;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px;
      margin-bottom: 40px;
    }
  }
}
</style>
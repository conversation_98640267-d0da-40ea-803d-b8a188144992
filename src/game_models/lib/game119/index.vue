<template>
  <div class="game119-page">
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <div class="page-bg"></div>
		<settingPage @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、本训练用于提高训练者对各种物品认知；</p>
        <p class="synopsis-content">2、一共七关，请根据提示进行操作；</p>
        <p class="synopsis-content">3、本训练是实物图。</p>
      </div>
    </settingPage>
    <div :class="['game-content']" v-if="status === 3">
      <div class="content">
        <img class="left-btn" v-if="number > 0" @click="toPrevious" src="/static/game_assets/game107/left.png" />
        <img class="right-btn" v-if="number < 7" @click="toNext" src="/static/game_assets/game107/right.png" />
        
        <!-- 学习环节+训练1 -->
        <div class="content-1" v-if="number === 0 || number === 1">
          <div class="content-title" :style="{opacity: number === 1 ? '1' : '0'}">
            <template v-if="number === 1">
              <img v-if="isPlay" class="title-icon" src="/static/game_assets/game116/pause.png" />
              <img v-else class="title-icon" src="/static/game_assets/game116/play.png" />
              <span class="title-text">{{current.name}}</span>
            </template>
          </div>

          <div class="content-main">
            <div :class="['content-item', itemClass(1, index)]" v-for="(item, index) in question" :key="index + 'item'" @click="chooseItem(item.index)">
              <img class="item-img" :src="`/static/game_assets/game119/item_${item.index}.png`" />
              
              <template v-if="number === 1">
                <img class="item-icon" src="/static/game_assets/game116/img.png" />
              </template>

              <div class="item-choose" v-if="number === 1 && choose === item.index && isShowCorrect">
                <img class="icon" src="/static/game_assets/game8/success.png" />
              </div>
            </div>
          </div>
        </div>
        
        <!-- 训练2 -->
        <div class="content-2" v-if="number === 2">
          <div class="content-left">
            <draggable 
              :class="['left-item', 'left-item-' + item]" 
              :style="{'z-index': choose === item ? 99 : 0}" 
              v-for="item in 9" 
              :key="item + 'item'" 
              v-model="dragItemBottom[item-1]" 
              :group="group2" 
              @add="dragAdd(item)"
            >
              <img :class="['left-img', 'left-img-' + item]" :src="`/static/game_assets/game116/puzzle_${item}.png`" />
              <img v-for="it in dragItemBottom[item-1]" :key="it.name + 'img'" :class="['right-img', 'right-img-' + it.name]" :src="`/static/game_assets/game119/item_${current.index}_${it.name}.png`" />
            </draggable>
          </div>

          <div class="content-right">
            <div class="right-item" v-for="item in 9" :key="item + 'item'">
              <draggable 
                class="right-item-wapper" 
                  v-model="dragItemTop[item-1]"
                :group="group1" 
                @start="dragStart1(item)"
              >
                <img v-for="it in dragItemTop[item-1]" :key="it.name + 'img'" :class="['right-img', 'right-img-' + it.name]" :src="`/static/game_assets/game119/item_${current.index}_${it.name}.png`" />
              </draggable>
            </div>
          </div>
        </div>
      
        <!-- 训练3 -->
        <div class="content-3" v-if="number === 3">
          <div class="content-left">
            <div class="left-item" v-for="item in question" :key="item.index + 'item'">
              <div class="item-left">{{item.name}}</div>

              <div class="item-right">=</div>
            </div>
          </div>

          <div class="content-middle">
            <draggable
              class="middle-item"
              v-for="(item, index) in question"
              :key="item.index + 'item2'"
              v-model="dragItemLeft[index]" 
              :group="group2"
              @add="dragAdd2(index)"
            >
              <img class="item-icon" v-if="!dragItemLeft[index].length" src="/static/game_assets/game116/img.png" />
              <img v-for="it in dragItemLeft[index]" :key="it.name + 'img1'" class="right-img" :src="`/static/game_assets/game119/item_${it.name}.png`" />
            </draggable>
          </div>

          <div class="content-right">
            <div class="right-item" v-for="item in 3" :key="item + 'item3'">
              <draggable 
                class="right-item-wapper" 
                :group="group1" 
                v-model="dragItemRight[item-1]"
                @start="dragStart2(item)"
              >
                <img v-for="it in dragItemRight[item-1]" :key="it.name + 'img2'" class="right-img" :src="`/static/game_assets/game119/item_${it.name}.png`" />
              </draggable>
            </div>
          </div>
        </div>

        <!-- 训练4 -->
        <div class="content-4" v-if="number === 4">
          <div class="content-item" v-for="item in question" :key="item.index + 'item4'" @click="chooseItem(item.index)">
            <img class="item-img" :src="`/static/game_assets/game119/item_${item.index}.png`" />

            <div class="item-choose" v-if="choose === item.index && isShowCorrect">
              <img class="icon" src="/static/game_assets/game8/success.png" />
            </div>
          </div>
        </div>

        <!-- 训练5 + 训练7 -->
        <div class="content-5" v-if="number === 5 || number === 7">
          <div class="content-item" v-for="(item, index) in question" :key="index + 'item5'" @click="openItem(item, index)">
            <div :class="['item', itemClass(2, item, index)]">
              <img class="icon1" src="/static/game_assets/game116/img.png" />
            </div>
            <div :class="['item', itemClass(2, item, index)]">
              <img v-if="number === 5" class="main1" :src="`/static/game_assets/game119/item_${item.index}.png`" />

              <template v-if="number === 7">
                <img class="main2" src="/static/game_assets/game116/play.png" />
              </template>
            </div>
          </div>
        </div>

        <!-- 训练6 -->
        <div class="content-6" v-if="number === 6">
          <div class="content-top">
            <img v-if="isPlay" class="title-icon" src="/static/game_assets/game116/pause.png" />
            <img v-else class="title-icon" src="/static/game_assets/game116/play.png" />
            <span class="title-text">{{current.name}}</span>
          </div>

          <div class="content-bottom">
            <div class="bottom-item" v-for="item in question" :key="item.index + 'item'" @click="chooseItem(item.index)">
              <img class="item-img" :src="`/static/game_assets/game119/item_${item.index}.png`" />

              <div class="item-choose" v-if="choose === item.index && isShowCorrect">
                <img class="icon" src="/static/game_assets/game8/success.png" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div :class="['sucess', isShowCorrect ? 'translate-top' : '']" :style="{'opacity': isShowCorrect ? 1 : 0}">
        <img class="img" src="/static/game_assets/game29/hot_balloon.png">
      </div>
    </div>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import api from '../../utils/common.js'

export default {
  name: 'game119',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    draggable
  },

  data() {
    return {
      musicUrl: '',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,
      isShowCorrect: false,
      isCanOpen: true,

      timer: null,
      index: 0,
			question: [],
      current: {},
      choose: 0,
      answer: [],
      dragItemTop: [],
      dragItemBottom: [[], [], [], [], [], [], [], [], []],
      dragItemLeft: [[], [], []],
      dragItemRight: [],
      group1: {
        name: 'itemList',
        pull: true,
        put: false,
        sort: false
      },
      group2: {
        name: 'itemList',
        pull: false,
        put: true,
        sort: false
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    itemClass(type, item, index) {
      if (type === 1 && this.number === 0) {
        if (this.index > item) {
          return 'rotate-item'
        }
      } else {
        const list1 = this.answer.filter(it => it.index === item.index)
        const list2 = this.answer.filter(it => it.sort === index)
        if (list1.length && list2.length) {
          return 'flip-item'
        }
      }
    },

    // TODO: 返回首页
		goHome() {
      this.pause()
			this.$router.go(-1)
		},

		play(url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },
  
    handleEnded() {
      this.isPlay = false
      this.index++
      setTimeout(() => {
        if (this.number) return

        if (this.index < 6) {
          this.play(`/static/game_assets/audio/game119/audio_${this.question[this.index].index}.mp3`)
        } else {
          this.number++
          this.startProcess()
        }
      }, 1200)
    },

    start() {
      this.status = 3
      this.startProcess()
      // this.timing()
    },

    startProcess() {
      const list = [
        {
          name: '闹钟',
          index: 1
        },
        {
          name: '书',
          index: 2
        },
        {
          name: '篮球',
          index: 3
        },
        {
          name: '剪刀',
          index: 4
        },
        {
          name: '铅笔',
          index: 5
        },
        {
          name: '椅子',
          index: 6
        }
      ]

      this.index = 0
      if (this.number === 1 || this.number === 2) {
        this.current = api.getRandomArray(list, 1)[0]
      }
      if (this.number === 0 || this.number === 1) {
        this.question = api.shuffle(list)
      }
      if (this.number === 0) {
        this.play(`/static/game_assets/audio/game119/audio_${this.question[this.index].index}.mp3`)
      }
      if (this.number === 2) {
        let li = [1, 2, 3, 4, 5, 6, 7, 8, 9]
        li = api.shuffle(li)

        for (let i = 0; i < 9; i++) {
          const item = li[i]
          this.dragItemTop.push([{
            name: item,
            startIndex: i,
          }])
        }
      }
      if (this.number === 3 || this.number === 4 || this.number === 5 || this.number === 6 || this.number === 7) {
        this.question = api.getRandomArray(list, 3)
      }
      if (this.number === 3) {
        const li = api.shuffle([0, 1, 2])
        for (let i = 0; i < 3; i++) {
          const item = this.question[li[i]]
          this.dragItemRight.push([{
            name: item.index,
            index: li[i],
            startIndex: i,
          }])
        }
      }
      if (this.number === 4 || this.number === 6) {
        this.current = api.getRandomArray(this.question, 1)[0]
      }
      if (this.number === 5 || this.number === 7) {
        this.question = this.question.concat(this.question)
        this.question = api.shuffle(this.question)
      }
      if (this.number === 1 || this.number === 2 || this.number === 3 || this.number === 5  || this.number === 7) {
        this.play(`/static/game_assets/audio/game104/title_${this.number}.mp3`)
      }
      if (this.number === 4 || this.number === 6) {
        this.play(`/static/game_assets/audio/game119/audio_${this.current.index}.mp3`)
      }
    },

    toPrevious() {
      if (this.isShowCorrect) return
      clearTimeout(this.timer)
      this.choose = 0
      this.answer = []
      this.dragItemTop = []
      this.dragItemBottom = [[], [], [], [], [], [], [], [], []]
      this.dragItemLeft = [[], [], []]
      this.dragItemRight = []
      this.isShowCorrect = false
      this.number--
      this.startProcess()
    },

    toNext() {
      if (this.isShowCorrect) return
      clearTimeout(this.timer)
      this.choose = 0
      this.answer = []
      this.isShowCorrect = false
      this.number++
      if (this.number === 1 || this.number === 4 || this.number === 6) {
        this.errorNum++
      }
      if (this.number === 2) {
        const list = this.dragItemBottom.filter(item => item.length)
        this.errorNum = this.errorNum + 9 - list.length
      }
      if (this.number === 3) {
        const list = this.dragItemLeft.filter(item => item.length)
        this.errorNum = this.errorNum + 3 - list.length
      }
      if (this.number === 5) {
        const choose = this.answer.filter(it => it.sort === index)
        this.errorNum = this.errorNum + 3 - parseInt(choose.length / 2)
      }
      this.startProcess()
    },

    dragStart1(item) {
      this.choose = this.dragItemTop[item - 1][0].name
    },

    dragStart2(item) {
      this.choose = this.dragItemRight[item - 1][0].index
    },

    dragAdd(index) {
      if (this.choose !== index) {
        if (this.dragItemBottom[index - 1].length > 1) {
          const item = this.dragItemBottom[index - 1].filter(item => item.name !== index)[0]
          this.dragItemBottom[index - 1] = this.dragItemBottom[index - 1].filter(item => item.name === index)
          this.dragItemTop[item.startIndex].push(item)
        } else {
          const item = this.dragItemBottom[index - 1].pop()
          this.dragItemTop[item.startIndex].push(item)
        }
        this.errorNum++
        this.play('/static/game_assets/audio/error_audio.mp3')
      } else {
        this.succesNum++
      }

      const list = this.dragItemBottom.filter(item => item.length)
      if (list.length >= 9) this.goNext()
    },

    dragAdd2(index) {
      if (this.choose !== index) {
        if (this.dragItemLeft[index].length > 1) {
          const item = this.dragItemLeft[index].filter(item => item.index !== index)[0]
          this.dragItemLeft[index] = this.dragItemLeft[index].filter(item => item.index === index)
          this.dragItemRight[item.startIndex].push(item)
        } else {
          const item = this.dragItemLeft[index].pop()
          this.dragItemRight[item.startIndex].push(item)
        }
        this.errorNum++
        this.play('/static/game_assets/audio/error_audio.mp3')
      } else {
        this.succesNum++
      }

      const list = this.dragItemLeft.filter(item => item.length)
      if (list.length >= 3) this.goNext()
    },

    chooseItem(item) {
      if (!this.number) return

			this.choose = item
      if (this.choose === this.current.index) {
        this.succesNum++
        this.goNext()
      } else {
        this.errorNum++
        this.play('/static/game_assets/audio/error_audio.mp3')
      }
    },

    openItem(item, index) {
      const choose = this.answer.filter(it => it.sort === index)
      if (!this.isCanOpen || choose.length) return

      this.answer.push({
        ...item,
        sort: index
      })
      if (this.number === 7) this.play(`/static/game_assets/audio/game119/audio_${item.index}.mp3`)
      if (this.answer.length && !(this.answer.length % 2)) {
        const it = this.answer[this.answer.length - 2]
        if (it.index !== item.index) {
          this.errorNum++
          this.isCanOpen = false
          setTimeout(() => {
            this.answer.pop()
            this.answer.pop()
            this.isCanOpen = true

            if (this.answer.length >= 6) this.goNext()
          }, 500)
        } else {
          this.succesNum++
          if (this.answer.length >= 6) this.goNext()
        }
      }
    },

    goNext() {
      this.isShowCorrect = true
      this.timer = setTimeout(() => {
        this.timer = null
        this.choose = 0
        this.answer = []
        this.isShowCorrect = false
        this.number++

        if (this.number > 7) {
          this.submit()
        } else {
          this.startProcess()
        }
      }, 2200)
    },

    submit() {
      this.isStop = true
      this.store = parseInt(100 / (this.succesNum + this.errorNum) * this.succesNum)
			this.infos[0].value = this.second
			this.infos[1].value = this.succesNum
			this.infos[2].value = this.errorNum
			this.status = 4
			this.params = {
				id: this.info.id,
				grade: '',
				time: this.second,
				totalPoints: this.store
			}
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
			this.choose = 0
      this.answer = []
      this.dragItemTop = []
      this.dragItemBottom = [[], [], [], [], [], [], [], [], []]
      this.dragItemLeft = [[], [], []]
      this.dragItemRight = []
			this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game119-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game118/bg.png");
  } 

  .game-synopsis {
    .synopsis-content {
      padding-top: 20px;
      margin: 0;
      font-size: 30px;
      line-height: 42px;
      font-weight: 400;
      color: #A83A01;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    .content {
      position: relative;
      width: 1920px;
      height: 1080px;
      display: flex;
      justify-content: center;
      align-items: center;

      .left-btn {
        position: absolute;
        top: 525px;
        left: 80px;
        width: 64px;
        cursor: pointer;
      }

      .right-btn {
        position: absolute;
        top: 525px;
        right: 80px;
        width: 64px;
        cursor: pointer;
      }

      .content-1 {
        position: relative;
        width: 1047px;
        height: 843px;
        margin-bottom: 47px;
        
        .content-title {
          position: relative;
          width: 417px;
          height: 105px;
          border-radius: 57px;
          background: #57A8BB;
          display: flex;
          align-items: center;
          padding: 0 12px;
          margin: 0 auto;

          .title-icon {
            width: 91px;
            cursor: pointer;
          }

          .title-text {
            display: block;
            padding-left: 26px;
            font-size: 54px;
            line-height: 75px;
            font-weight: 500;
            color: #fff;
          }
        }

        .content-main {
          height: 721px;
          padding-top: 30px;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;

          .rotate-item {
            transform:rotate(1080deg) scale(0);
          }

          .content-item {
            position: relative;
            width: 286px;
            height: 286px;
            transition: All 1s ease-in-out;
            cursor: pointer;
            background: #fff;
            border-radius: 27px;

            .item-img {
              position: absolute;
              top: 10px;
              left: 10px;
              width: 266px;
            }

            .item-icon {
              position: absolute;
              top: 100px;
              left: 100px;
              width: 84px;
            }

            .item-choose {
              position: absolute;
              top: 10px;
              left: 10px;
              width: 266px;
              height: 266px;
              border-radius: 27px;
              background: rgba(70, 156, 182, 0.7);

              .icon {
                position: absolute;
                right: 18px;
                bottom: 18px;
                width: 230px;
              }
            }
          }
        }
      }

      .content-2 {
        position: relative;
        width: 1378px;
        height: 693px;
        margin-bottom: 80px;
        margin-left: 15px;
        display: flex;
        justify-content: space-between;

        .content-left {
          margin-top: 65px;
          width: 501px;
          height: 491px;
          background: #469CB6;
          padding: 12px;
          border-radius: 40px;

          .left-item {
            position: absolute;
            display: flex;
            justify-content: center;
            align-items: center;

            .left-img {
              position: absolute;
              top: 0;
              left: 0;
            }

            .left-img-1, .left-img-3, .left-img-4, .left-img-6, .left-img-7, .left-img-9 {
              width: 166px;
            }

            .left-img-2, .left-img-5, .left-img-8 {
              width: 241px;
            }

            .right-img {
              position: relative;
              z-index: 2;
            }

            .right-img-1, .right-img-3, .right-img-4, .right-img-6, .right-img-7, .right-img-9 {
              width: 153px;
            }

            .right-img-2, .right-img-5, .right-img-8 {
              width: 228px;
            }
          }

          .left-item-1 {
            top: 77px;
            left: 12px;
            width: 166px;
            height: 166px;
          }

          .left-item-2 {
            top: 77px;
            left: 130px;
            width: 241px;
            height: 166px;
          }

          .left-item-3 {
            top: 77px;
            left: 321px;
            width: 166px;
            height: 166px;
          }

          .left-item-4 {
            top: 194px;
            left: 12px;
            width: 166px;
            height: 200px;
          }

          .left-item-5 {
            top: 194px;
            left: 130px;
            width: 241px;
            height: 237px;
          }

          .left-item-6 {
            top: 194px;
            left: 321px;
            width: 166px;
            height: 236px;
          }

          .left-item-7 {
            top: 342px;
            left: 12px;
            width: 166px;
            height: 200px;
          }

          .left-item-8 {
            top: 380px;
            left: 130px;
            width: 241px;
            height: 163px;
          }

          .left-item-9 {
            top: 380px;
            left: 321px;
            width: 166px;
            height: 162px;
          }
        }

        .content-right {
          width: 693px;
          height: 693px;
          display: flex;
          flex-wrap: wrap;

          .right-item {
            width: 231px;
            height: 231px;
            display: flex;
            justify-content: center;
            align-items: center;

            .right-img {
              cursor: pointer;
            }

            .right-img-1, .right-img-3, .right-img-4, .right-img-6, .right-img-7, .right-img-9 {
              width: 166px;
            }

            .right-img-2, .right-img-5, .right-img-8 {
              width: 241px;
            }
          }
        }
      }

      .content-3 {
        position: relative;
        width: 1130px;
        height: 709px;
        margin-bottom: 150px;
        margin-right: 120px;
        display: flex;
        justify-content: space-between;

        .content-left {
          display: flex;
          flex-direction: column;
          width: 427px;
          justify-content: space-between;

          .left-item {
            display: inline-flex;
            justify-content: space-between;
            align-items: center;
            height: 223px;

            .item-left {
              position: relative;
              width: 258px;
              height: 120px;
              font-size: 86px;
              font-weight: 500;
              line-height: 120px;
              text-align: right;
              color: #31707E;
            }

            .item-right {
              width: 53px;
              font-size: 86px;
              font-weight: 500;
              line-height: 120px;
              color: #31707E;
            }
          }
        }

        .content-middle {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          width: 223px;

          .middle-item {
            position: relative;
            width: 223px;
            height: 223px;
            background: #fff;
            border-radius: 23px;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 5px solid #57A8BB;

            .item-icon {
              position: absolute;
              top: 60px;
              left: 60px;
              width: 84px;
            }
          }
        }

        .content-right {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          width: 223px;
          height: 100%;

          .right-item {
            position: relative;
            width: 223px;
            height: 223px;
            background: #57A8BB;
            border-radius: 23px;
            display: flex;
            justify-content: center;
            align-items: center;

            .right-item-wapper {
              width: 211px;
              height: 211px;

              .right-img {
                width: 211px;
                height: 211px;
                cursor: pointer;
              }
            }
          } 
        }

        .right-img {
          position: relative;
          width: 211px;
          height: 211px;
        }
      }

      .content-4 {
        position: relative;
        width: 900px;
        height: 270px;
        margin-top: 120px;
        display: flex;
        justify-content: space-between;

        .content-item {
          position: relative;
          width: 270px;
          height: 270px;
          border-radius: 27px;
          background: #fff;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          .item-img {
            width: 260px;
            height: 260px;
          }

          .item-choose {
            position: absolute;
            top: 0;
            left: 0;
            width: 270px;
            height: 270px;
            border-radius: 27px;
            background: rgba(70, 156, 182, 0.7);

            .icon {
              position: absolute;
              right: 18px;
              bottom: 18px;
              width: 230px;
            }
          }
        }
      }

      .content-5 {
        position: relative;
        width: 900px;
        height: 562px;
        margin-bottom: 70px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;

        .content-item > div:first-child {
          z-index: 1;
          backface-visibility: hidden;
        }

        .flip-item {
          transform: rotateY(180deg);
        }

        .content-item {
          position: relative;
          width: 266px;
          height: 266px;
          cursor: pointer;

          .item {
            position: absolute;
            top: 0;
            left: 0;
            width: 266px;
            height: 266px;
            background: #fff;
            border-radius: 27px;
            transition: all .5s;
            display: flex;
            justify-content: center;
            align-items: center;


            .icon1 {
              position: relative;
              width: 84px;
            }

            .main1 {
              position: relative;
              width: 250px;
            }

            .main2 {
              position: relative;
              width: 91px;
            }
          }
        }
      }

      .content-6 {
        position: relative;
        width: 900px;
        height: 694px;
        margin-bottom: 160px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .content-top {
          position: relative;
          width: 417px;
          height: 105px;
          border-radius: 57px;
          background: #57A8BB;
          display: flex;
          align-items: center;
          padding: 0 12px;
          margin: 0 auto;

          .title-icon {
            width: 91px;
            cursor: pointer;
          }

          .title-text {
            display: block;
            padding-left: 26px;
            font-size: 54px;
            line-height: 75px;
            font-weight: 500;
            color: #fff;
          }
        }

        .content-bottom {
          width: 100%;
          display: flex;
          justify-content: space-between;

          .bottom-item {
            position: relative;
            width: 270px;
            height: 270px;
            border-radius: 27px;
            background: #fff;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;

            .item-img {
              width: 260px;
              height: 260px;
            }

            .item-choose {
              position: absolute;
              top: 0;
              left: 0;
              width: 270px;
              height: 270px;
              border-radius: 27px;
              background: rgba(70, 156, 182, 0.7);

              .icon {
                position: absolute;
                right: 18px;
                bottom: 18px;
                width: 230px;
              }
            }
          }
        }
      }
    }

    .sucess {
      position: absolute;
      bottom: -762px;
      width: 774px;
      height: 762px;
      transition: transform 2s ease-in;
      z-index: 100;

      .img {
        width: 774px;
        height: 762px;
      }
    }

    .translate-top {
      transform: translateY(-2442px);
    }
  }

  .black-bg {
    background: rgba(0, 0, 0, 0.33);
  }
}
</style>
<template>
  <div class="game92-page">
    <audio ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <div class="page-bg"></div>
		<settingPage title="" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <img class="img1" src="/static/game_assets/game92/img1.png" />
        <img class="img2" src="/static/game_assets/game92/img2.png" />
      </div>
    </settingPage>
    <div :class="['game-content']" v-if="status === 3">
      <div class="top">
        <img v-if="!isPlay" class="top-icon" src="/static/game_assets/common/play.png" @click="play" />
				<img v-else class="top-icon" src="/static/game_assets/common/pause.png" @click="pause" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>

      <div class="content">
        <!-- 菜单页 -->
        <div class="content-1" v-if="number === 0">
          <div class="content-item" v-for="item in 12" :key="item + 'img1'" @click="chooseItem(item)">
            <img class="item-bg" src="/static/game_assets/game92/item_bg.png" />
            <img :class="['item-img', 'item-img-' + item]" :src="`/static/game_assets/game92/item_${item}.png`" />
            <img :class="['item-num', item > 9 && 'item-num-right']" :src="`/static/game_assets/game92/num_${item}.png`" />
          </div>
        </div>
        
        <!-- 训练 -->
        <div class="content-2" v-if="number === 1">
          <img class="content-bg" src="/static/game_assets/game92/content_bg.png" />

          <img class="content-btn1" src="/static/game_assets/game92/btn1.png" @click="goback" />
          <img class="content-btn2" src="/static/game_assets/game92/btn2.png" @click="reset" />

          <div class="content-left">
            <div :class="['left-item', 'left-item-' + current]">
              <img :class="[`left-item-bg`]" :src="`/static/game_assets/game92/item_bg_${current}.png`" />
              <draggable 
                :class="[`left-main`, `left-main-${item}`]" 
                :style="{'z-index': choose === item ? 99 : 0}" 
                v-for="item in 7" 
                :key="item + 'item1'" 
                v-model="dragItemLeft[item-1]" 
                :group="group2" 
                @add="dragAdd(item)"
              >
                <img v-for="it in dragItemLeft[item-1]" :key="it.name + 'bg1'" :class="[`left-img`, `left-img-${item}`]" :src="`/static/game_assets/game92/item_${current}_${it.name}.png`" />
              </draggable>
            </div>
          </div>

          <div class="content-right">
            <div :class="['right-item', 'right-item-' + current]">
              <draggable 
                :class="['right-img', 'right-img-' + item]"
                :style="{'z-index': choose === item ? 99 : 0}"
                v-for="item in 9" 
                :key="item + 'item'"
                v-model="dragItemRight[item-1]"
                :group="group1" 
                @start="dragStart(item)"
              >
                <img v-for="it in dragItemRight[item-1]" :key="it.name + 'img'" :src="`/static/game_assets/game92/item_${current}_${it.name}.png`" />
              </draggable>
            </div>
          </div>
        </div>
      </div>

      <div :class="['sucess', isShowCorrect ? 'translate-top' : '']" :style="{'opacity': isShowCorrect ? 1 : 0}">
        <img class="img" src="/static/game_assets/game29/hot_balloon.png">
      </div>
    </div>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'

export default {
  name: 'game92',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    draggable
  },

  data() {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isPlay: false,
      isShowCorrect: false,
      isCanOpen: true,

      timer: null,
      current: 0,
      choose: 0,
      dragItemLeft: [[], [], [], [], [], [], []],
      dragItemRight: [],
      group1: {
        name: 'itemList',
        pull: true,
        put: false,
        sort: false
      },
      group2: {
        name: 'itemList',
        pull: false,
        put: true,
        sort: false
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    // TODO: 返回首页
		goHome() {
      this.pause()
			this.$router.go(-1)
		},

		play() {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start() {
      this.status = 3
      // this.timing()
      this.play()
    },

    startProcess() {
      for (let i = 0; i < 7; i++) {
        this.dragItemRight.push([{
          name: i + 1,
          startIndex: i,
        }])
      }
    },

    dragStart(item) {
      this.choose = this.dragItemRight[item - 1][0].name
    },

    goback() {
      if (this.isShowCorrect) return
      this.number = 0
      this.choose = 0
      this.current = 0
      this.dragItemLeft = [[], [], [], [], [], [], []]
      this.dragItemRight = []
    },

    reset() {
      if (this.isShowCorrect) return
      this.succesNum = 0
      this.errorNum = 0
      this.choose = 0
      this.dragItemLeft = [[], [], [], [], [], [], []]
      this.dragItemRight = []
      this.startProcess()
    },

    dragAdd(index) {
      if (this.choose !== index) {
        if (this.dragItemLeft[index - 1].length > 1) {
          const item = this.dragItemLeft[index - 1].filter(item => item.name !== index)[0]
          this.dragItemLeft[index - 1] = this.dragItemLeft[index - 1].filter(item => item.name === index)
          this.dragItemRight[item.startIndex].push(item)
        } else {
          const item = this.dragItemLeft[index - 1].pop()
          this.dragItemRight[item.startIndex].push(item)
        }
        this.errorNum++
      } else {
        this.succesNum++
      }

      const list = this.dragItemLeft.filter(item => item.length)
      if (list.length >= 7) {
        this.isShowCorrect = true
        this.timer = setTimeout(() => {
          this.isShowCorrect = false
          this.submit()
        }, 2200)
      }
    },

    chooseItem(item) {
      this.startProcess()
      this.current = item
      this.number = 1
    },

    submit() {
      this.pause()
      this.isStop = true
      this.store = 100 - 5 * this.errorNum > 0 ? 100 - 5 * this.errorNum : 0
			this.infos[0].value = this.second
			this.infos[1].value = this.succesNum
			this.infos[2].value = this.errorNum
			this.status = 4
			this.params = {
				id: this.info.id,
				grade: '',
				time: this.second,
				totalPoints: this.store
			}
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
			this.choose = 0
      this.current = 0
      this.dragItemLeft = [[], [], [], [], [], [], []]
      this.dragItemRight = []
			this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game92-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game126/bg.png");
  } 

  .game-synopsis {
    position: relative;
    width: 1823px;
    height: 644px;
    margin-top: 40px;
    margin-left: 15px;

    .img1 {
      position: absolute;
      top: 0;
      left: 0;
      width: 1823px;
    }

    .img2 {
      position: absolute;
      top: 18px;
      right: 283px;
      width: 187px;
    }
  }

  .setting-page {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .footer {
      position: absolute;
      bottom: 40px;
      width: 734px;
      display: inline-flex;
      justify-content: space-between;

      .img {
        width: 268px;
        height: 115px;
        cursor: pointer;
      }
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    .top {
			position: absolute;
			top: 40px;
			left: 105px;
			width: 175px;
			display: flex;
			justify-content: space-between;
      z-index: 1;

			.top-icon {
				width: 80px;
				cursor: pointer;
			}
		}

    .content {
      position: relative;
      width: 1920px;
      height: 1080px;
      display: flex;
      justify-content: center;
      align-items: center;

      .content-1 {
        width: 1795px;
        height: 835px;
        margin-top: 65px;
        margin-right: 20px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        
        .content-item {
          position: relative;
          width: 466px;
          height: 242px;
          margin-left: -23px;
          padding-top: 10px;
          padding-right: 60px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;

          .item-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 466px;
          }

          .item-img {
            position: relative;
          }

          .item-img-1, .item-img-2, .item-img-3 {
            width: 102px;
          }

          .item-img-4 {
            width: 122px;
          }

          .item-img-5 {
            width: 174px;
          }

          .item-img-6 {
            width: 154px;
          }

          .item-img-7 {
            width: 130px;
          }

          .item-img-8 {
            width: 155px;
          }

          .item-img-9 {
            width: 159px;
          }

          .item-img-10 {
            width: 97px;
          }

          .item-img-11 {
            width: 73px;
          }

          .item-img-12 {
            width: 151px;
          }

          .item-num {
            position: absolute;
            bottom: 73px;
            right: 50px;
            height: 69px;
          }

          .item-num-right {
            right: 25px;
          }
        }
      }

      .content-2 {
        position: relative;
        width: 1849px;
        height: 916px;
        margin-bottom: 54px;
        margin-left: 17px;
        padding: 220px 235px 56px 255px;
        display: flex;

        .content-bg {
          position: absolute;
          top: 84px;
          left: 0;
          width: 1849px;
        }

        .content-btn1 {
          position: absolute;
          top: 0;
          right: 50px;
          width: 110px;
          cursor: pointer;
        }

        .content-btn2 {
          position: absolute;
          top: 122px;
          right: 50px;
          width: 110px;
          cursor: pointer;
        }

        .content-left {
          position: relative;
          width: 955px;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;

          .left-item {
            position: relative;

            .left-item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
            }

            .left-main {
              position: absolute;

              img {
                width: calc(100% - 3px);
              }
            }
          }

          .left-item-1 {
            width: 337px;
            height: 580px;

            .left-main-1 {
              position: absolute;
              top: 2px;
              left: 155px;
              width: 112px;
              height: 113px;
            }

            .left-main-2 {
              position: absolute;
              top: 125px;
              left: 94px;
              width: 80px;
              height: 159px;
            }

            .left-main-3 {
              position: absolute;
              top: 120px;
              left: 176px;
              width: 159px;
              height: 160px;
            }

            .left-main-4 {
              position: absolute;
              top: 240px;
              left: 66px;
              width: 159px;
              height: 159px;
            }

            .left-main-5 {
              position: absolute;
              top: 373px;
              left: 2px;
              width: 79px;
              height: 80px;
            }

            .left-main-6 {
              position: absolute;
              top: 468px;
              left: 170px;
              width: 56px;
              height: 112px;
            }

            .left-main-7 {
              position: absolute;
              top: 342px;
              left: 229px;
              width: 80px;
              height: 159px;
            }
          }

          .left-item-2 {
            width: 334px;
            height: 328px;

            .left-main-1 {
              position: absolute;
              top: 45px;
              left: 1.5px;
              width: 80px;
              height: 80px;
            }

            .left-main-2 {
              position: absolute;
              top: 130px;
              left: 2px;
              width: 80px;
              height: 81px;
            }

            .left-main-3 {
              position: absolute;
              top: 215px;
              left: 2px;
              width: 80px;
              height: 80px;
            }

            .left-main-4 {
              position: absolute;
              top: 44px;
              left: 88px;
              width: 80px;
              height: 159px;
            }

            .left-main-5 {
              position: absolute;
              top: 131px;
              left: 88px;
              width: 80px;
              height: 159px;
            }

            .left-main-6 {
              position: absolute;
              top: 3px;
              left: 173px;
              width: 160px;
              height: 159px;
            }

            .left-main-7 {
              position: absolute;
              top: 169px;
              left: 173px;
              width: 160px;
              height: 159px;
            }
          }

          .left-item-3 {
            width: 330px;
            height: 429px;

            .left-main-1 {
              position: absolute;
              top: 2px;
              left: 23px;
              width: 57px;
              height: 112px;
            }

            .left-main-2 {
              position: absolute;
              top: 2px;
              left: 85px;
              width: 57px;
              height: 112px;
            }

            .left-main-3 {
              position: absolute;
              top: 66px;
              left: 28px;
              width: 112px;
              height: 113px;
            }

            .left-main-4 {
              position: absolute;
              top: 185px;
              left: 2px;
              width: 159px;
              height: 80px;
            }

            .left-main-5 {
              position: absolute;
              top: 266px;
              left: 2px;
              width: 159px;
              height: 159px;
            }

            .left-main-6 {
              position: absolute;
              top: 270px;
              right: 163px;
              width: 159px;
              height: 159px;
            }

            .left-main-7 {
              position: absolute;
              bottom: 1px;
              right: 1px;
              width: 159px;
              height: 80px;
            }
          }

          .left-item-4 {
            width: 397px;
            height: 231px;

            .left-main-1 {
              position: absolute;
              top: 2px;
              left: 2px;
              width: 159px;
              height: 80px;
            }

            .left-main-2 {
              position: absolute;
              top: 2px;
              left: 86px;
              width: 225px;
              height: 113px;
            }

            .left-main-3 {
              position: absolute;
              top: 5px;
              left: 235px;
              width: 79px;
              height: 80px;
            }

            .left-main-4 {
              position: absolute;
              top: 2px;
              right: 1px;
              width: 80px;
              height: 159px;
            }

            .left-main-5 {
              position: absolute;
              top: 85px;
              left: 2px;
              width: 80px;
              height: 80px;
            }

            .left-main-6 {
              position: absolute;
              top: 119px;
              left: 86px;
              width: 225px;
              height: 112px;
            }

            .left-main-7 {
              position: absolute;
              top: 86px;
              right: 2px;
              width: 80px;
              height: 80px;
            }
          }

          .left-item-5 {
            width: 567px;
            height: 344px;

            .left-main-1 {
              position: absolute;
              top: 3px;
              left: 2px;
              width: 57px;
              height: 112px;
            }

            .left-main-2 {
              position: absolute;
              top: 3px;
              left: 66px;
              width: 56px;
              height: 112px;
            }

            .left-main-3 {
              position: absolute;
              top: 67px;
              right: 449px;
              width: 112px;
              height: 113px;
            }

            .left-main-4 {
              position: absolute;
              top: 184px;
              left: 60px;
              width: 160px;
              height: 159px;
            }

            .left-main-5 {
              position: absolute;
              top: 184px;
              left: 152px;
              width: 159px;
              height: 80px;
            }

            .left-main-6 {
              position: absolute;
              top: 185px;
              left: 244px;
              width: 160px;
              height: 159px;
            }

            .left-main-7 {
              position: absolute;
              top: 104px;
              right: -1px;
              width: 159px;
              height: 80px;
            }
          }

          .left-item-6 {
            width: 499px;
            height: 384px;

            .left-main-1 {
              position: absolute;
              top: 3px;
              left: 223px;
              width: 113px;
              height: 113px;
            }

            .left-main-2 {
              position: absolute;
              top: 106px;
              left: 2px;
              width: 80px;
              height: 80px;
            }

            .left-main-3 {
              position: absolute;
              top: 109px;
              left: 185px;
              width: 112px;
              height: 113px;
            }

            .left-main-4 {
              position: absolute;
              top: 142px;
              left: 85px;
              width: 160px;
              height: 159px;
            }

            .left-main-5 {
              position: absolute;
              bottom: 0;
              left: 86px;
              width: 80px;
              height: 79px;
            }

            .left-main-6 {
              position: absolute;
              bottom: 0;
              right: 161px;
              width: 159px;
              height: 159px;
            }

            .left-main-7 {
              position: absolute;
              bottom: 80px;
              right: -1px;
              width: 159px;
              height: 80px;
            }
          }

          .left-item-7 {
            width: 423px;
            height: 352px;

            .left-main-1 {
              position: absolute;
              top: 3px;
              left: 3px;
              width: 113px;
              height: 112px;
            }

            .left-main-2 {
              position: absolute;
              top: 59px;
              left: 145px;
              width: 113px;
              height: 56px;
            }

            .left-main-3 {
              position: absolute;
              top: 125px;
              left: 59px;
              width: 112px;
              height: 225px;
            }

            .left-main-4 {
              position: absolute;
              top: 119px;
              left: 63px;
              width: 159px;
              height: 80px;
            }

            .left-main-5 {
              position: absolute;
              bottom: 61px;
              right: 74px;
              width: 225px;
              height: 113px;
            }

            .left-main-6 {
              position: absolute;
              bottom: 1px;
              left: 62px;
              width: 169px;
              height: 57px;
            }

            .left-main-7 {
              position: absolute;
              bottom: 0;
              right: 0;
              width: 112px;
              height: 57px;
            }
          }

          .left-item-8 {
            width: 505px;
            height: 469px;

            .left-main-1 {
              position: absolute;
              top: 3px;
              right: 189px;
              width: 113px;
              height: 113px;
            }

            .left-main-2 {
              position: absolute;
              top: 119px;
              right: 133px;
              width: 225px;
              height: 114px;
            }

            .left-main-3 {
              position: absolute;
              top: 236px;
              right: 132px;
              width: 225px;
              height: 113px;
            }

            .left-main-4 {
              position: absolute;
              bottom: 60px;
              left: 86px;
              width: 169px;
              height: 57px;
            }

            .left-main-5 {
              position: absolute;
              bottom: 37px;
              right: 81px;
              width: 159px;
              height: 80px;
            }

            .left-main-6 {
              position: absolute;
              top: 288px;
              right: 0;
              width: 112px;
              height: 57px;
            }

            .left-main-7 {
              position: absolute;
              bottom: 0;
              left: 2px;
              width: 112px;
              height: 57px;
            }
          }

          .left-item-9 {
            width: 518px;
            height: 297px;

            .left-main-1 {
              position: absolute;
              top: 3px;
              left: 174px;
              width: 112px;
              height: 113px;
            }

            .left-main-2 {
              position: absolute;
              top: 118px;
              left: 174px;
              width: 112px;
              height: 57px;
            }

            .left-main-3 {
              position: absolute;
              bottom: 0;
              left: 2px;
              width: 159px;
              height: 81px;
            }

            .left-main-4 {
              position: absolute;
              bottom: 0;
              left: 91px;
              width: 79px;
              height: 80px;
            }

            .left-main-5 {
              position: absolute;
              top: 181px;
              left: 173px;
              width: 112px;
              height: 113px;
            }

            .left-main-6 {
              position: absolute;
              bottom: 0;
              right: 116px;
              width: 225px;
              height: 114px;
            }

            .left-main-7 {
              position: absolute;
              top: 180px;
              right: 0;
              width: 225px;
              height: 113px;
            }
          }

          .left-item-10 {
            width: 317px;
            height: 290px;

            .left-main-1 {
              position: absolute;
              top: 3px;
              left: 83px;
              width: 80px;
              height: 80px;
            }

            .left-main-2 {
              position: absolute;
              top: 89px;
              left: 3px;
              width: 159px;
              height: 80px;
            }

            .left-main-3 {
              position: absolute;
              top: 56px;
              right: -1px;
              width: 225px;
              height: 113px;
            }

            .left-main-4 {
              position: absolute;
              bottom: 0;
              left: 35px;
              width: 112px;
              height: 113px;
            }

            .left-main-5 {
              position: absolute;
              bottom: 0;
              left: 45px;
              width: 225px;
              height: 113px;
            }

            .left-main-6 {
              position: absolute;
              right: 40px;
              bottom: 56px;
              width: 113px;
              height: 57px;
            }

            .left-main-7 {
              position: absolute;
              bottom: -2px;
              right: 36px;
              width: 57px;
              height: 112px;
            }
          }

          .left-item-11 {
            width: 238px;
            height: 242px;

            .left-main-1 {
              position: absolute;
              top: 2px;
              left: 4px;
              width: 225px;
              height: 112px;
            }

            .left-main-2 {
              position: absolute;
              top: 6px;
              left: 2px;
              width: 113px;
              height: 225px;
            }

            .left-main-3 {
              position: absolute;
              bottom: 0;
              left: 2px;
              width: 112px;
              height: 57px;
            }

            .left-main-4 {
              position: absolute;
              top: 125px;
              left: 63px;
              width: 113px;
              height: 113px;
            }

            .left-main-5 {
              position: absolute;
              top: 70px;
              right: 58px;
              width: 57px;
              height: 112px;
            }

            .left-main-6 {
              position: absolute;
              top: 7px;
              right: -1px;
              width: 57px;
              height: 169px;
            }

            .left-main-7 {
              position: absolute;
              bottom: 1px;
              right: 0;
              width: 113px;
              height: 112px;
            }
          }

          .left-item-12 {
            width: 496px;
            height: 296px;

            .left-main-1 {
              position: absolute;
              top: 3.5px;
              left: 3.5px;
              width: 79px;
              height: 79px;
            }

            .left-main-2 {
              position: absolute;
              top: 48px;
              left: 89px;
              width: 80px;
              height: 80px;
            }

            .left-main-3 {
              position: absolute;
              top: 3px;
              left: 175px;
              width: 80px;
              height: 79px;
            }

            .left-main-4 {
              position: absolute;
              bottom: 0;
              left: 88px;
              width: 160px;
              height: 160px;
            }

            .left-main-5 {
              position: absolute;
              bottom: 80px;
              left: 181px;
              width: 159px;
              height: 80px;
            }

            .left-main-6 {
              position: absolute;
              bottom: 0;
              right: 65px;
              width: 159px;
              height: 160px;
            }

            .left-main-7 {
              position: absolute;
              bottom: 79px;
              right: 0;
              width: 57px;
              height: 169px;
            }
          }
        }

        .content-right {
          flex: 1;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;

          .right-item {
            position: relative;

            .right-img {
              position: absolute;
              cursor: pointer;

              img {
                width: 100%
              }
            }
          }

          .right-item-1 {
            width: 250px;
            height: 365px;

            .right-img-1 {
              bottom: 14px;
              left: 87px;
              width: 112px;
            }

            .right-img-2 {
              top: 68px;
              right: 0;
              width: 80px;
            }

            .right-img-3 {
              bottom: 0;
              left: 0;
              width: 159px;
            }

            .right-img-4 {
              top: 32px;
              left: 0;
              width: 159px;
            }

            .right-img-5 {
              top: 32px;
              right: 0;
              width: 79px;
            }

            .right-img-6 {
              bottom: 70px;
              right: 0;
              width: 56px;
            }

            .right-img-7 {
              top: 0;
              left: 0;
              width: 80px;
            }
          }

          .right-item-2 {
            width: 265px;
            height: 383px;

            .right-img-1 {
              bottom: 0;
              left: 72px;
              width: 80px;
            }

            .right-img-2 {
              bottom: 102px;
              right: 0;
              width: 80px;
            }

            .right-img-3 {
              top: 99px;
              right: 0;
              width: 80px;
            }

            .right-img-4 {
              top: 217px;
              left: 94px;
              width: 80px;
            }

            .right-img-5 {
              top: 0;
              left: 63px;
              width: 80px;
            }

            .right-img-6 {
              top: 34px;
              left: 0;
              width: 160px;
            }

            .right-img-7 {
              top: 203px;
              left: 0;
              width: 160px;
            }
          }

          .right-item-3 {
            width: 226px;
            height: 435px;

            .right-img-1 {
              top: 144px;
              left: 0;
              width: 57px;
            }

            .right-img-2 {
              bottom: 59px;
              right: 0;
              width: 57px;
            }

            .right-img-3 {
              top: 95px;
              left: 33px;
              width: 112px;
            }

            .right-img-4 {
              top: 0;
              left: 36px;
              width: 159px;
            }

            .right-img-5 {
              left: 0;
              bottom: 0;
              width: 159px;
            }

            .right-img-6 {
              bottom: 180px;
              right: 0;
              width: 159px;
            }

            .right-img-7 {
              bottom: 0;
              left: 28px;
              width: 159px;
            }
          }

          .right-item-4 {
            width: 225px;
            height: 390px;

            .right-img-1 {
              top: 125px;
              left: 30px;
              width: 159px;
            }

            .right-img-2 {
              top: 0;
              left: 0;
              width: 225px;
            }

            .right-img-3 {
              right: 24px;
              top: 38px;
              width: 79px;
            }

            .right-img-4 {
              bottom: 10px;
              right: 0;
              width: 80px;
            }

            .right-img-5 {
              bottom: 83px;
              left: 0;
              width: 80px;
            }

            .right-img-6 {
              left: 0;
              bottom: 0;
              width: 225px;
            }

            .right-img-7 {
              top: 40px;
              left: 14px;
              width: 80px;
            }
          }

          .right-item-5 {
            width: 263px;
            height: 383px;

            .right-img-1 {
              bottom: 26px;
              right: 0;
              width: 57px;
            }

            .right-img-2 {
              top: 89px;
              right: 33px;
              width: 56px;
            }

            .right-img-3 {
              bottom: 30px;
              left: 0;
              width: 112px;
            }

            .right-img-4 {
              top: 0;
              left: 26px;
              width: 160px;
            }

            .right-img-5 {
              left: 50px;
              bottom: 0;
              width: 159px;
            }

            .right-img-6 {
              bottom: 48px;
              left: 31px;
              width: 160px;
            }

            .right-img-7 {
              top: 88px;
              left: 41px;
              width: 159px;
            }
          }

          .right-item-6 {
            width: 249px;
            height: 418px;

            .right-img-1 {
              top: 151px;
              left: 26px;
              width: 113px;
            }

            .right-img-2 {
              top: 30px;
              left: 0;
              width: 80px;
            }

            .right-img-3 {
              top: 0;
              right: 27px;
              width: 112px;
            }

            .right-img-4 {
              left: 21px;
              bottom: 0;
              width: 160px;
            }

            .right-img-5 {
              top: 128px;
              left: 0;
              width: 80px;
            }

            .right-img-6 {
              bottom: 131px;
              right: 0;
              width: 159px;
            }

            .right-img-7 {
              bottom: 48px;
              right: 10px;
              width: 159px;
            }
          }

          .right-item-7 {
            width: 244px;
            height: 389px;

            .right-img-1 {
              top: 209px;
              right: 0;
              width: 113px;
            }

            .right-img-2 {
              top: 0;
              right: 0;
              width: 113px;
            }

            .right-img-3 {
              bottom: 0;
              left: 0;
              width: 112px;
            }

            .right-img-4 {
              top: 155px;
              left: 34px;
              width: 159px;
            }

            .right-img-5 {
              top: 14px;
              right: 10px;
              width: 225px;
            }

            .right-img-6 {
              bottom: 0;
              left: 33px;
              width: 169px;
            }

            .right-img-7 {
              top: 0;
              left: 0;
              width: 112px;
            }
          }

          .right-item-8 {
            width: 281px;
            height: 420px;

            .right-img-1 {
              bottom: 12px;
              left: 0;
              width: 113px;
            }

            .right-img-2 {
              bottom: 82px;
              left: 16px;
              width: 225px;
            }

            .right-img-3 {
              top: 0;
              left: 23px;
              width: 225px;
            }

            .right-img-4 {
              top: 156px;
              right: 0;
              width: 169px;
            }

            .right-img-5 {
              top: 135px;
              left: 0;
              width: 159px;
            }

            .right-img-6 {
              bottom: 89px;
              right: 19px;
              width: 112px;
            }

            .right-img-7 {
              bottom: 0;
              right: 29px;
              width: 112px;
            }
          }

          .right-item-9 {
            width: 284px;
            height: 365px;

            .right-img-1 {
              top: 90px;
              right: 0;
              width: 112px;
            }

            .right-img-2 {
              top: 39px;
              left: 0;
              width: 112px;
            }

            .right-img-3 {
              top: 0;
              left: 45px;
              width: 159px;
            }

            .right-img-4 {
              top: 0;
              right: 50px;
              width: 79px;
            }

            .right-img-5 {
              top: 108px;
              left: 30px;
              width: 112px;
            }

            .right-img-6 {
              bottom: 125px;
              left: 28px;
              width: 225px;
            }

            .right-img-7 {
              bottom: 0;
              left: 28px;
              width: 225px;
            }
          }

          .right-item-10 {
            width: 310px;
            height: 385px;

            .right-img-1 {
              bottom: 34px;
              right: 33px;
              width: 80px;
            }

            .right-img-2 {
              bottom: 174px;
              left: 0;
              width: 159px;
            }

            .right-img-3 {
              bottom: 130px;
              right: 27px;
              width: 225px;
            }

            .right-img-4 {
              bottom: 0;
              left: 72px;
              width: 112px;
            }

            .right-img-5 {
              top: 0;
              left: 49px;
              width: 225px;
            }

            .right-img-6 {
              top: 14px;
              right: 0;
              width: 113px;
            }

            .right-img-7 {
              top: 107px;
              right: 30px;
              width: 57px;
            }
          }

          .right-item-11 {
            width: 255px;
            height: 411px;

            .right-img-1 {
              bottom: 0;
              right: 0;
              width: 225px;
            }

            .right-img-2 {
              top: 0;
              left: 33px;
              width: 113px;
            }

            .right-img-3 {
              bottom: 0;
              left: 0;
              width: 112px;
            }

            .right-img-4 {
              top: 0;
              left: 104px;
              width: 113px;
            }

            .right-img-5 {
              top: 72px;
              right: 21px;
              width: 57px;
            }

            .right-img-6 {
              left: 105px;
              bottom: 117px;
              width: 57px;
            }

            .right-img-7 {
              bottom: 116px;
              right: 0;
              width: 113px;
            }
          }

          .right-item-12 {
            width: 257px;
            height: 363px;

            .right-img-1 {
              top: 167px;
              right: 0;
              width: 79px;
            }

            .right-img-2 {
              bottom: 0;
              right: 0;
              width: 80px;
            }

            .right-img-3 {
              top: 15px;
              left: 0;
              width: 80px;
            }

            .right-img-4 {
              bottom: 0;
              left: 28px;
              width: 160px;
            }

            .right-img-5 {
              top: 104px;
              left: 0;
              width: 159px;
            }

            .right-img-6 {
              top: 24px;
              left: 31px;
              width: 159px;
            }

            .right-img-7 {
              top: 0;
              right: 0;
              width: 57px;
            }
          }
        }
      }
    }

    .sucess {
      position: absolute;
      bottom: -762px;
      width: 774px;
      height: 762px;
      transition: transform 2s ease-in;
      z-index: 100;

      .img {
        width: 774px;
        height: 762px;
      }
    }

    .translate-top {
      transform: translateY(-2442px);
    }
  }

  .black-bg {
    background: rgba(0, 0, 0, 0.33);
  }
}
</style>
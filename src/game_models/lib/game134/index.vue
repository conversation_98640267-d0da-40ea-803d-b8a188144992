<template>
  <div class="game134-page">
    <div class="page-bg"></div>
    <settingPage title="比例定位" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、本训练提高您的数字推理能力。</p>
        <p class="synopsis-content">2、请点击比例尺上代表左边数字的圆点。</p>
        <p class="synopsis-content">3、随着您的能力提高，题目难度将住建增加，努力吧。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <div class="content-left">
          <div class="title">
            <img class="title-bg" src="/static/game_assets/game134/title_bg.png" />
            <span class="title-text">{{current}}</span>
          </div>

          <div class="answer">
            <img class="answer-bg" src="/static/game_assets/game134/bar.png" />
            <img :class="['answer-point', 'answer-point-' + Number(item / 10)]" v-for="item in answerList" :key="item + 'item'" src="/static/game_assets/game134/point.png" @click="chooseItem(item)" />
          </div>
        </div>

        <!-- <div class="content-right"></div> -->
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 一共10道题
 
export default {
  name: 'game134',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isShowCorrect: false,
      isShowError: false,
      show: false,
      answerList: [],
      current: 0,

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
      this.startProcess()
      // this.timing()
    },

    startProcess() {
      const list = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
      this.current = api.getRandomArray(list, 1)[0]
      const arr = list.filter(item => item !== this.current)
      this.answerList = api.getRandomArray(arr, 3).concat(this.current)
      this.answerList = api.shuffle(this.answerList)
      this.number++
    },

    chooseItem(item) {
      if (this.isShowCorrect || this.isShowError) return
      if (item === this.current) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }

      setTimeout(() => {
        this.isShowCorrect = false
        this.isShowError = false

        if (this.number < 10) {
          this.startProcess()
          return
        } else {
          this.submit()
        }
      }, 800)
    },

    stop() {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isStop = true
      this.store = this.succesNum * 10
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.current = 0
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game134-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game133/bg.png");
  }

  .game-synopsis {
    width: 707px;
    height: 444px;
    margin: 34px;
    padding: 33px 30px;
    background: #FFFEF3;
    border: 2px solid #014747;
    border-radius: 36px;

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #5E381F;
    }

    .synopsis-content {
      padding-top: 18px;
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 400;
      color: #5E381F;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1000px;
      height: 815px;
      // margin-right: 395px;
      margin-bottom: 140px;
      display: flex;
      justify-content: space-between;

      .content-left {
        display: inline-flex;
        justify-content: space-between;
        width: 100%;

        .title {
          position: relative;
          padding-top: 313px;
          width: 209px;
          height: 209px;

          .title-bg {
            position: absolute;
            top: 313px;
            left: 0;
            width: 209px;
          }

          .title-text {
            display: block;
            position: relative;
            font-size: 113px;
            line-height: 209px;
            text-align: center;
            font-weight: 600;
            color: #2135B1;
            font-family: PingFangSC-Semibold, PingFang SC;
          }
        }

        .answer {
          position: relative;
          width: 82px;
          height: 100%;

          .answer-bg {
            position: absolute;
            top: 0;
            left: 14px;
            width: 50px;
          }

          .answer-point {
            position: absolute;
            width: 82px;
            left: 0;
            cursor: pointer;
          }

          .answer-point-0 {
            bottom: -41px; 
          }

          .answer-point-1 {
            bottom: 41.5px; 
          }

          .answer-point-2 {
            bottom: 122px; 
          }

          .answer-point-3 {
            bottom: 203.5px; 
          }

          .answer-point-4 {
            bottom: 285px; 
          }

          .answer-point-5 {
            bottom: 366.5px; 
          }

          .answer-point-6 {
            bottom: 448px; 
          }

          .answer-point-7 {
            bottom: 529.5px; 
          }

          .answer-point-8 {
            bottom: 611px; 
          }

          .answer-point-9 {
            bottom: 692.5px; 
          }

          .answer-point-10 {
            bottom: 774px; 
          }
        }
      }

      .content-right {
        margin-top: 122px;
        width: 626px;
        height: 626px;
        border-radius: 20px;
        background: #fff;
      }
    }

    .footer {
      position: absolute;
      bottom: 50px;
      display: flex;
      justify-content: space-between;
      width: 1790px;

      .img1 {
        width: 270px;
        height: 115px;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px;
      margin-bottom: 40px;
      z-index: 99;
    }
  }
}
</style>
<template>
  <div class="game99-page">
    <div class="page-bg"></div>
    <img class="bg-icon" src="/static/game_assets/game99/icon.png">
		<settingPage title="视觉广度-方式一" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、注意的广度又称注意的范围，是指在同一时间内一个人能看清楚地把握注意对象的数量。</p>
        <p class="synopsis-content">2、本组训练将有助于扩大您的注意范围，进而有助于提高学习和工作的效率。</p>
        <p class="synopsis-content">3、训练方法:显示屏上将快速出现一些相同的图形，请注意看，然后说出呈现目标的个数。</p>
        <p class="synopsis-content">4、训练等级:依据注意的数量和目标呈现的时间长短分为三个等级。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <template v-if="isShowKey">
        <div class="title">
          <img class="title-bg" src="/static/game_assets/game99/title_bg.png" />
          <div class="title-text">
            <span>您刚才看到了几个"</span>
            <img :src="`/static/game_assets/game99/item_${chooseItem}.png`" />
            <span>"图形？</span>
          </div>
        </div>

        <div class="content">
          <img class="content-left" src="/static/game_assets/game99/left1.png" />
          <img class="content-right" src="/static/game_assets/game99/right1.png" />

          <div class="content-key">
            <img class="key-bg" src="/static/game_assets/game99/btn_bg.png" />

            <div class="content-input">
              <img class="input-bg" src="/static/game_assets/game99/input.png" />
              <span class="btn-number">{{ choose }}</span>
            </div>

            <div class="content-delete" @click="choose = ''">
              <img class="delete-bg" src="/static/game_assets/game99/delete.png" />
              <span class="btn-text">X</span>
            </div>

            <div class="content-btn" v-for="item in 10" :key="item + 'btn'" @click="select(item % 10)">
              <img class="btn-bg" src="/static/game_assets/game99/small_btn.png" />
              <span class="btn-text">{{item % 10}}</span>
            </div>

            <div class="content-confirm" @click="confirm">
              <img class="confirm-bg" src="/static/game_assets/game99/big_btn.png" />
              <span class="btn-text">确定</span>
            </div>
          </div>
        </div>

        <div class="footer">
          <div class="left-group">
            <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
            <img class="img" v-if="isShowCorrect || isShowError" src="/static/game_assets/common/continue.png" @click="goon">
          </div>
          <img class="img" v-if="isShowCorrect || isShowError" src="/static/game_assets/common/reset.png" @click="reset">
        </div>

        <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
        <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
      </template>

      <template v-else>
        <div class="content-position">
          <img :class="['position-img', 'img-' + index]" v-for="(item, index) in answer" :key="index + 'img'" :src="`/static/game_assets/game99/item_${chooseItem}.png`">
        </div>
      </template>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 游戏分为3个等级，每轮游戏4个回合
// 1级：3-7 1s
// 2级：4-8 0.5s
// 3级：5-9 0.5s

export default {
  name: 'game99',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowKey: false,
      isShowCorrect: false,
      isShowError: false,
      chooseItem: 0,
      answer: 0,
      choose: '',

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.startProcess()
      this.status = 3
      // this.timing()
    },

    startProcess() {
      this.isShowKey = false
      const all = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
      
      const start = this.level + 2
      const end = start + 4
      this.answer = api.randomNum(start, end) // 答案个数
      this.chooseItem = api.randomNum(1, 10) // 答案图形
      
      setTimeout(() => {
        this.isShowKey = true
        this.number++
      }, (6 - parseInt(this.level / 2)) * 500)
    },

    select(item) {
      if (this.choose.length >= 2) return
      this.choose = this.choose + (item === 10 ? 0 : item).toString()
    },

    confirm() {
      if (Number(this.choose) === this.answer) {
        this.isShowCorrect = true
      } else {
        this.isShowError = true
      }
    },

    stop() {
      if (!this.isShowKey) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isStop = true
      this.store = this.succesNum * 25
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again() {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.isStop = false
      this.answer = 0
      this.choose = ''
      this.chooseItem = 0
      this.start()
      this.timing()
    },

    goon() {
      this.isShowCorrect = false
      this.isShowError = false

      if (Number(this.choose) === this.answer) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      if (this.number < 4) {
        this.answer = 0
        this.choose = ''
        this.startProcess()
      } else {
        this.submit()
      }
    },

    reset() {
      this.isShowCorrect = false
      this.isShowError = false

      this.answer = 0
      this.choose = ''
      this.number--
      this.startProcess()
    }
  }
}
</script>

<style lang="scss" scoped>
.game99-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game99/bg.png");    
  } 

  .bg-icon {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 394px;
  }

  .game-synopsis {
    width: 1576px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

		.title {
      position: absolute;
      top: 0;
      width: 1054px;
      height: 144px;
      z-index: 1;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1054px;
        height: 144px;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 24px 0 40px 0;
        font-size: 36px;
        line-height: 79px;
        text-align: center;
        color: #1B1D2D;
        user-select: none;

        img {
          width: 79px;
          height: 79px;
        }
      }
    }

    .content {
      position: relative;
      width: 1650px;
      height: 755px;
      margin-bottom: 43px;
      margin-left: 12px;
      padding: 0 256px 0 417px;

      .content-left {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 579px;
      }

      .content-right {
        position: absolute;
        right: 0;
        bottom: 14px;
        width: 504px;
      }

      .content-key {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        align-items: center;
        padding: 117px 336px 39px 228px;
        width: 977px;
        height: 755px;

        .key-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 977px;
        }

        .content-input {
          position: relative;
          width: 336px;
          height: 124px;

          .input-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 336px;
          }

          .btn-number {
            position: relative;
            display: block;
            font-size: 60px;
            line-height: 124px;
            font-weight: 600;
            color: #3D4166;
            text-align: center;
            user-select: none;
          }
        }

        .content-delete {
          position: relative;
          width: 61px;
          height: 118px;
          padding: 11px 0;
          cursor: pointer;

          .delete-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 61px;
          }
        }

        .content-btn {
          position: relative;
          width: 124px;
          height: 95px;
          cursor: pointer;

          .btn-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 124px;
            height: 95px;
          }
        }

        .content-confirm {
          position: relative;
          width: 270px;
          height: 95px;
          cursor: pointer;

          .confirm-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 270px;
            height: 95px;
          }
        }

        .btn-text {
          position: relative;
          display: block;
          font-size: 60px;
          line-height: 95px;
          font-weight: 600;
          color: #3D4166;
          text-align: center;
          user-select: none;
        }
      }
		}

    .footer {
      position: absolute;
      bottom: 20px;
      display: flex;
      justify-content: space-between;
      width: 1670px;

      .img {
        height: 115px;
        cursor: pointer;
      }

      .left-group {
        width: 581px;
        display: flex;
        justify-content: space-between;
      }
    }

    .content-position {
      position: relative;
      width: 100%;
      height: 900px;

      .position-img {
        position: absolute;
        width: 105px;
        height: 105px;
      }

      .img-1 {
        top: 400px;
        left: 100px;
      }

      .img-2 {
        top: 800px;
        left: 1400px;
      }

      .img-3 {
        top: 150px;
        left: 400px;
      }

      .img-4 {
        top: 80px;
        left: 1000px;
      }

      .img-5 {
        top: 10px;
        left: 1500px;
      }

      .img-6 {
        top: 500px;
        left: 600px;
      }

      .img-7 {
        top: 750px;
        left: 300px;
      }

      .img-8 {
        top: 600px;
        left: 1700px;
      }

      .img-9 {
        top: 800px;
        left: 680px;
      }

      .img-10 {
        top: 450px;
        left: 850px;
      }

      .img-11 {
        top: 320px;
        left: 1270px;
      }

      .img-0 {
        top: 280px;
        left: 1700px;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px;
      margin-bottom: 40px;
      z-index: 99;
    }
  }
}
</style>
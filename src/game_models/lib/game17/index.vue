<template>
  <div class="game17-page">
    <div class="page-bg"></div>
    <settingPage title="不同图片" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、查找不同训练的目的是培养被训练者的观察力和提高对所关注信息的选择能力。</p>
        <p class="synopsis-content">2、训练内容丰富，比对的两组图片几乎一样。但有几处不同，请您尽快把它们找出来。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="title">
        <div class="left">总个数： 3</div>
        <div class="right">
          <template v-for="item in 5">
            <img v-if="answer.length < item" class="img" :key="item" :src="`/static/game_assets/game17/btn_${item}.png`">
            <img v-else class="img" :key="item" :src="`/static/game_assets/game17/btn_choose_${item}.png`">
          </template>
        </div>
      </div>

      <div class="content">
        <template v-for="item in imgList">
          <div class="item" v-if="item === questionNum" :key="item + '-1'">
            <img class="img" />
            <div :ref="`ring-${questionNum}-${item}`" :class="`ring-${questionNum}-${item}`" v-for="item in 5" :key="item" @click="chooseItem(item)"></div>
          </div>

          <div :class="['item', 'item-' + questionNum]" v-if="item === questionNum" :key="item + '-2'">
            <img class="img" />
            <div :ref="`ring-${questionNum}-${item}`" :class="`ring-${questionNum}-${item}`" v-for="item in 5" :key="item" @click="chooseItem(item)"></div>
          </div>
        </template>
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        <img class="img3" src="/static/game_assets/common/submit.png" @click="submit">
      </div>
    </div>

    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'

export default {
  name: 'game17',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      imgList: [1, 2, 3],
      questionNum: 0,
      answer: [],

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.status = 3
      // this.timing()
      this.imgList = api.shuffle(this.imgList)
      this.startProcess()
    },

    startProcess() {
      this.questionNum = this.imgList[this.number]
    },

    chooseItem(item) {
      const list = this.answer.filter(it => it === item)
      if (list.length) return
      this.answer.push(item) 
      const refs = this.$refs[`ring-${this.questionNum}-${item}`]
      refs.forEach(ref => {
        ref.style.opacity = 1
      })
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    submit() {
      this.succesNum = this.succesNum + this.answer.length

      this.number++
      if (this.number >= 3) {
        this.isStop = true
        this.errorNum = 15 - this.succesNum
        this.store = parseInt(100 / (this.succesNum + this.errorNum) * this.succesNum)
        this.infos[0].value = this.second
        this.infos[1].value = this.succesNum
        this.infos[2].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: '',
          time: this.second,
          totalPoints: this.store
        }
      } else {
        this.answer = []
        this.startProcess()
      }
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    again() {
      this.number = 0
      this.answer = []
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.questionNum = 0
      this.isStop = false
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game17-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game12/bg.png");
  }

  .game-synopsis {
    width: 787px;
    height: 484px;
    margin: 34px;
    padding: 33px 30px;
    background: #fff;
    border: 2px solid #7BBD41;
    border-radius: 36px;

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #5E381F;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 18px;
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 400;
      color: #5E381F;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .title {
      display: flex;
      justify-content: space-between;
      position: relative;
      padding: 40px 155px 0 190px;

      .left {
        padding: 15px 35px 15px 20px;
        border: 1px solid #37B982;
        border-radius: 38px;

        font-size: 32px;
        line-height: 45px;
        color: #37B982;
        user-select: none;
      }

      .right {
        display: inline-flex;
        justify-content: space-between;
        width: 338px;
        height: 76px;
        padding: 7px 0;

        .img {
          width: 62px;
          height: 62px;
        }
      }
    }

    .content {
      width: 1100px;
      height: 708px;
      padding-top: 44px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;

      .item {
        position: relative;
        width: 512px;
        height: 664px;

        .img {
          width: 512px;
          height: 664px;
        }

        .ring-1-1 {
          position: absolute;
          top: 21px;
          right: 25px;
          width: 45px;
          height: 45px;
          border: 6px solid #F5FF39;
          border-radius: 50%;
          opacity: 0;
        }

        .ring-1-2 {
          position: absolute;
          top: 75px;
          right: 28px;
          width: 45px;
          height: 45px;
          border: 6px solid #F5FF39;
          border-radius: 50%;
          opacity: 0;
        }

        .ring-1-3 {
          position: absolute;
          top: 460px;
          right: 70px;
          width: 104px;
          height: 104px;
          border: 6px solid #F5FF39;
          border-radius: 50%;
          opacity: 0;
        }

        .ring-1-4 {
          position: absolute;
          top: 189px;
          right: 12px;
          width: 76px;
          height: 76px;
          border: 6px solid #F5FF39;
          border-radius: 50%;
          opacity: 0;
        }

        .ring-1-5 {
          position: absolute;
          top: 537px;
          right: 223px;
          width: 55px;
          height: 55px;
          border: 6px solid #F5FF39;
          border-radius: 50%;
          opacity: 0;
        }
      }

      .img {
        width: 366px;
        height: 371px;
        background: #fff;
        border-radius: 37px;
        cursor: pointer;
      }

      .number {
        width: 366px;
        height: 348px;
        background: #fff;
        border-radius: 37px;
      }
    }

    .footer {
      position: relative;
      display: flex;
      justify-content: space-between;
      width: 1509px;
      padding-bottom: 47px;
      margin: 0 auto;

      .img1 {
        width: 270px;
        height: 115px;
        cursor: pointer;
      }

      .img3 {
        width: 267px;
        height: 115px;
        cursor: pointer;
      }
    }
  }
}
</style>
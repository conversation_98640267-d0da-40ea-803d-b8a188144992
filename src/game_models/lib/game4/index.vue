<template>
  <div class="game4-page">
    <div id="page-bg" :class="status > 1 ? 'page-bg2': 'page-bg1'"></div>
    <SettingPage @next="status = 1" v-if="status == 0" :title="gameData.title" :gameData="gameData"
      :info="gameData.info" :formData="formData" :musicUrl="'/static/game_assets/game1/mp3/project_mp3.mp3'" />
    <div class="page-content page-content2" v-else-if="status == 1">
      <div class="num">
        <div class="img"></div>
      </div>
    </div>
    <div class="page-content page-content3" v-else-if="status == 2">
      <div class="info_container">
        <div class="info">
          <div class="item time">
            <img src="/static/game_assets/game4/images/time.png" />
            <div class="label">{{getTime(time)}}</div>
          </div>
          <div class="item time">
            <img src="/static/game_assets/game4/images/points.png" />
            <div class="label">{{number}}</div>
          </div>
          <div class="music" :class="formData.sound ? 'type1':'type2'" @click="changeSoundStatus"></div>
          <audio ref="music" muted controls="controls" autoplay="autoplay" loop="loop" style="display:none">
            <source src="/static/game_assets/game1/mp3/project_mp3.mp3" type="audio/mpeg" />
          </audio>
        </div>
      </div>
      <div class="answer">
        <template v-for="(item,index) of question">
          <div :key="index" :style="{left:`${item.left}px`,right:`${item.right}px`, marginTop:`${item.top}px`}"
            :class="'item ' + (answer == index?'current ':'') + (stop ?'stop':'')" @click="clickAnswer(item,index)">
            <img class="pic" :src="item.icon" />
          </div>
        </template>
      </div>
      <div class="actions">
        <img src="/static/game_assets/game4/images/stop.png" class="stop-btn" alt="" @click="goStop" v-show="!stop">
        <div class="other-btn">
          <img src="/static/game_assets/game4/images/continue.png" class="btn" alt="" @click="goContinue" v-show="stop">
          <img src="/static/game_assets/game4/images/replay.png" class="btn" alt="" @click="reset">
        </div>
      </div>
    </div>
    <ResultPage v-else-if="status == 3" @reset="reset" @nextPage="nextPage">
      <div class="_result">
        <div class="_result-points">
          <span class="_result-points-num">{{number}}</span>
          <span class="_result-points-unit">分</span>
        </div>
      </div>
    </ResultPage>
  </div>
</template>

<script>
import gameData from "./data/data.json"
import ResultPage from "../../resultPage.vue"
import SettingPage from "../../settingPage.vue"
import Action from "./action.vue"
export default {
  name: 'App',
  props: ['info'],
  components: { ResultPage, SettingPage },
  mixins: [Action],
  data () {
    let formData = {}
    for (const item of gameData.info) {
      formData[item.key] = item.default
      if (item.key == 'difficulty') {
        formData.gridCol = item.col
      }
    }
    console.log('-=-=-=-=-=', formData)
    return {
      number: 0,
      status: 0,
      gameData,
      formData,
      answer: null,
      time: 0,
      question: [],
      questionAnswer: null,
      stop: false,
      sound: false
    }
  },
  watch: {
    status (newVal, oldVal) {
      if (this.status == 1) {
        setTimeout(() => {
          this.time = 60
          this.currentDifficulty = this.gameData.gameInfo
          this.setQuestion(this.formData.difficulty)
          this.status = 2
          let fun = () => {
            if (!this.stop) {

              if (this.time == 0) {
                this.submit()
              } else {
                this.time--;
              }
              setTimeout(() => {
                fun()
              }, 1000);
            }
          }
          setTimeout(() => {
            fun()
          }, 1000);
        }, 3000);
      } else if (this.status == 3) {

      }
    }
  },
  methods: {
    changeSoundStatus () {
      if (this.formData.sound) {
        this.$refs.music.pause()
        this.formData.sound = false
      } else {
        this.formData.sound = true
        this.$refs.music.play()
      }
    },
    reset () {
      this.stop = false;
      this.answer = null;
      this.status = 0;
    },
    submit () {
      this.stop = true;
      // if (this.answer + 1 == this.questionAnswer) {
      //   this.number = 100
      // } else {
      //   this.number = 0
      // }
      this.save({ id: this.info.id, grade: "1", time: 60 - this.time, totalPoints: this.number })
      this.status = 3;
    },
    goStop () {
      this.stop = true
    },
    goContinue () {
      this.stop = false
      let fun = () => {
        if (!this.stop) {
          if (this.time == 0) {
            this.submit()
          } else {
            this.time--;
          }
          setTimeout(() => {
            fun()
          }, 1000);
        }
      }
      setTimeout(() => {
        fun()
      }, 1000);
    },
    clickAnswer (item, index) {
      console.log(item, index)
      this.answer = index
      if (item.type == 1) {
        this.number++
      }
    },
    setQuestion () {
      const { question, answer } = this.currentDifficulty
      const { difficulty } = this.formData
      this.questionAnswer = answer[difficulty]
      this.question = question[difficulty]
      for (let item of this.question) {
        let left = Math.random() * 1300
        let right = Math.random() * (window.innerHeight * 0.7)
        let top = Math.random() * (window.innerHeight * (-0.7))
        item.left = left
        item.right = right
        item.top = top
      }
    },
    getTime (time) {
      let t = (time / 60 + '').split('.')
      let s = "00"
      if (t[1]) {
        s = (('0.' + t[1] - 0) * 60).toFixed(0)
      }
      if (s - 0 < 10 && s != "00") {
        s = "0" + s
      }
      return `${t[0]}:${s}`
    },
  },

}
</script>

<style lang="scss">
@import './index.scss';
</style>

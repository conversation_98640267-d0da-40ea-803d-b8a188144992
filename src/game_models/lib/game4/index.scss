.game4-page {
  @keyframes num {
    0% {
      background-image: url('/static/game_assets/game1/images/num3.png');
    }
    34% {
      background-image: url('/static/game_assets/game1/images/num3.png');
    }
    66% {
      background-image: url('/static/game_assets/game1/images/num2.png');
    }
    100% {
      background-image: url('/static/game_assets/game1/images/num1.png');
    }
  }
  width: 100%;
  height: 100%;
  position: relative;
  user-select: none;
  cursor: url('/static/game_assets/game1/images/cursor.ico'), auto;
  #page-bg {
    position: absolute;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    &.page-bg1 {
      background-image: url('/static/game_assets/game1/images/bg1.png');
    }
    &.page-bg2 {
      background-image: url('/static/game_assets/game1/images/bg2.png');
    }
  }
  .page-content {
    position: absolute;
    z-index: 2;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;

    &.page-content4 {
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      .content {
        width: calc(100% - 758px);
        height: 683px;
        background-image: url('/static/game_assets/game4/images/result.png');
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        ._result {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          &-points {
            display: flex;
            align-items: flex-end;
            &-num {
              font-size: 170px;
              font-family: PingFang SC;
              font-weight: 600;
              color: #414043;
              background: linear-gradient(0deg, #ff7b00 0%, #e82f2f 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
            &-unit {
              font-size: 48px;
              font-family: PingFang SC;
              font-weight: 400;
              color: #414043;
              background: linear-gradient(0deg, #ff7b00 0%, #e82f2f 100%);
              line-height: 170px;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }
        .points {
          display: flex;
          align-items: flex-end;
          margin: 94px 0;
          .value {
            font-size: 170px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            line-height: 150px;
            color: #ffffff;
            background-image: linear-gradient(124deg, #ff7b00 0%, #e82f2f 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .unit {
            font-size: 48px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            line-height: 67px;
            background-image: linear-gradient(121deg, #ffc500 0%, #ff5353 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
        .time {
          font-size: 37px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #5e381f;
          line-height: 52px;
        }
      }
      .actions {
        display: flex;
        justify-content: center;
      }
    }

    &.page-content3 {
      display: flex;
      flex-direction: column;
      align-items: center;
      overflow: hidden;
      .answer {
        display: flex;
        flex-wrap: wrap;
        width: 1300px;
        max-height: 70%;
        .item {
          width: 400px;
          height: 234px;
          padding: 11px;
          box-sizing: border-box;
          animation: line 20s linear infinite;
          position: fixed;
          animation-play-state: running;

          .pic {
            max-width: 100%;
            max-height: 100%;
            animation: rotate 20s linear infinite;
            animation-play-state: running;
          }
          @keyframes rotate {
            0% {
              bottom: 100%;
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(1440deg);
              bottom: 0;
            }
          }
        }
        .stop {
          animation-play-state: paused;
          .pic {
            animation-play-state: paused;
          }
        }
        .current {
          display: none;
        }

        @keyframes line {
          0% {
            bottom: 100%;
          }
          100% {
            bottom: -30%;
          }
        }
      }
      .info_container {
        display: flex;
        width: 100%;
        justify-content: center;
        align-items: center;
        position: relative;
        padding-top: 47px;
        margin-bottom: 15px;
        height: 150px;
        z-index: 10;
        .num {
          font-size: 42px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #000000;
        }
        .info {
          display: flex;
          position: absolute;
          right: 0;
          .item {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 231px;
            height: 76px;
            border-radius: 38px;
            border: 1px solid #37b982;
            margin-right: 52px;
            img {
              width: 58px;
              height: 58px;
            }
            .label {
              width: 162px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 32px;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: #000000;
            }
          }
          .music {
            width: 82px;
            height: 82px;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            margin-right: 78px;
            &.type2 {
              background-image: url('/static/game_assets/game1/images/action2.png');
            }
            &.type1 {
              background-image: url('/static/game_assets/game1/images/action1.png');
            }
          }
        }
      }
    }
    &.page-content2 {
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      .num {
        width: 726px;
        height: 726px;
        background-image: url('/static/game_assets/game1/images/bg4.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        .img {
          width: 312px;
          height: 312px;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          animation-name: num;
          animation-duration: 3s;
          animation-timing-function: linear;
        }
      }
    }
    &.page-content1 {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .title {
        font-size: 50px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #5e381f;
        position: absolute;
        top: 78px;
      }
      .row {
        display: flex;
        align-items: center;
        margin-bottom: 50px;
        position: relative;
        .mask {
          width: 100%;
          height: 100%;
          position: absolute;
          left: 0;
          top: 0;
        }
        .label {
          font-size: 50px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #5e381f;
        }
        .content {
          display: flex;
          .item {
            display: flex;
            align-items: center;
            margin-right: 25px;
            .ck_name {
              font-size: 50px;
              font-family: PingFangSC-Semibold, PingFang SC;
              font-weight: 600;
              color: #5e381f;
            }
            .ck {
              width: 41px;
              height: 41px;
              display: flex;
              align-items: center;
              justify-content: center;
              background-image: url('/static/game_assets/game1/images/icon3.png');
              background-size: 100% 100%;
              background-repeat: no-repeat;
              margin-right: 27px;
              img {
                width: 44px;
                height: 44px;
                display: none;
              }
            }
            &.current {
              .ck {
                img {
                  display: block;
                }
              }
            }
          }
        }
      }
    }

    .actions {
      width: 100%;
      position: fixed;
      bottom: 64px;
      display: flex;
      padding: 0 206px;
      justify-content: space-between;
      .btn {
        width: 268px;
        height: 115px;
        margin-right: 28px;
        &:last-child {
          margin-right: 0;
        }
      }
      .stop-btn {
        width: 268px;
        height: 115px;
      }

      .other-btn {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        .btn {
          width: 268px;
          height: 115px;
          margin-right: 28px;
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }
}


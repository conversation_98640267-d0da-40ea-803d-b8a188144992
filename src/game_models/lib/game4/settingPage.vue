<template>
  <div class="page-content page-content1">
    <audio v-if="musicUrl" ref="music" muted controls="controls" autoplay="autoplay" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
      <div class="title">{{title}}</div>
      <div id="info">
        <div class="row" v-for="(item,index) of info" :key="index">
          <div class="mask" v-show="item.mask && !info.level"></div>
          <div class="label">{{item.label}}：</div>
          <div class="content">
            <div :class="`item ${(isCurrent(it.val,item.key) ? 'current' : '')}`" v-for="(it,ind) of gameData[item.key]"
              :key="ind" @click="selectItem(item,it)">
              <div class="ck">
                <img src="/static/game_assets/game1/images/icon4.png" alt="">
              </div>
              <div class="ck_name">{{it.label}}</div>
            </div>`
          </div>
        </div>
      </div>
      <div class="actions">
        <img src="/static/game_assets/game1/images/btn1.png" class="btn" alt="" @click="()=>{window.history.back(-1)}">
        <img src="/static/game_assets/game1/images/btn2.png" class="btn" alt="" @click="next">
      </div>
    </div>
</template>

<script>
export default {
  props:['title','info','formData','musicUrl','gameData'],
  watch:{
    'formData.sound' (val) {
      this.setSoundStatus()
    },
  },
  mounted(){
    this.setSoundStatus()
  },
  methods:{
    next(){
      this.$emit('next')
    },
    setSoundStatus () {
      if(this.musicUrl){
        if (this.formData.sound) {
          this.$refs.music.play()
        } else {
          this.$refs.music.pause()
        }
      }
    },
    selectItem (item, it) {
      const { type, key } = item
      switch (type) {
        case "radio":
          if (this.formData[key] != it.val) {
            this.formData[key] = it.val
          }
          if (key == 'difficulty') {
            this.formData.gridCol = it.col
          }
          break;
        case "switch":
          this.formData[key] = !this.formData[key];
          break;
      }
    },
    isCurrent (val, key) {
      if (typeof this.formData[key] == 'object' && this.formData[key].length) {
        return this.formData[key].indexOf(val) != -1
      } else {
        return this.formData[key] == val
      }
    }
  }
}
</script>

<style>

</style>
<template>
  <div class="game154-page">
    <div class="page-bg "></div>
    <settingPage @start="isStart" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">训练说明</p>
        <div class="synopsis-content">
          <p>1、请训练者读出显示的数字，由训练者/治疗师判定并选择对应选项;</p>
          <p>2、训练结束会出现成绩单</p>
          <p>2、本训练难度为A(简单);</p>
        </div>
      </div>
    </settingPage>
    <div class="page-content" v-if="status == 3">
      <div class="top">
        <div class="info">
          <div class="contanier">
            <div class="info-item">
              题目分数：{{number}}
            </div>
            <div class="info-item">
              题目数量：{{unit}}
            </div>
            <div class="info-item time">
              训练用时：{{getTime(second)}}
            </div>
          </div>
          <div class="desc" @mouseover="mouseover" @mouseleave="mouseleave">?</div>
        </div>
      </div>
      <div class="container">
        <audio ref="music" muted controls="controls" :autoplay="false" :loop="false" style="display:none">
          <source :src="voice" type="audio/mpeg" />
        </audio>
        <div class="voice" ref="voice" @click="changeMusic">
          <img class="voice-icon" src="/static/game_assets/game144/talk.png" alt="">
          {{question.desc}}
        </div>
        <div ref="synopsis" class="game-synopsis" style="display:none">
          <p class="synopsis-title">训练说明</p>
          <div class="synopsis-content">
            <p>1、请训练者读出显示的数字，由训练者/治疗师判定并选择对应选项;</p>
            <p>2、训练结束会出现成绩单</p>
            <p>2、本训练难度为A(简单);</p>
          </div>
        </div>
      </div>
      <div class="botton">
        <div class="right btn" @click="isRight">正确</div>
        <div class="error btn" @click="isError">错误</div>
      </div>
      <div class="check_answer" v-if="isSelect">
        <img class="check_answer_icon" src="/static/game_assets/game78/right.png" v-if="isAnswer == 0" alt=""></img>
        <img class="check_answer_icon" src="/static/game_assets/game78/false.png" v-if="isAnswer == 1" alt=""></img>
      </div>
    </div>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'

export default {
  name: 'game154',
  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },
  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data () {
    return {
      voice: "/static/game_assets/game153/audio1.mp3",
      sound: false,
      formData: [{ 'voice': "/static/game_assets/game154/audio1.mp3", desc: '按顺序从71数到32' }, { 'voice': "/static/game_assets/game154/audio2.mp3", desc: '按顺序从59数到97' }, { 'voice': "/static/game_assets/game154/audio3.mp3", desc: '按顺序从12数到55' }, { 'voice': "/static/game_assets/game154/audio4.mp3", desc: '按顺序从93数到35' }],
      status: 1,
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ],
      total: 0,
      question: {},
      unit: 0,
      currentClick: -1,
      isSelect: false, // 已经选择时间
      isAnswer: false, // 答案是否正确错误
      number: 0, //得分
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      params: {},
      isStop: false,
      show: false,
      minuteScale: "",
      hourScale: "",
      currentClick: -1,
      isSelect: false, // 已经选择时间
      isAnswer: -1, // 答案是否正确错误
      showBg: true,
      select: ""
    }
  },
  mounted() {
    this.timing()
  },
  methods: {
    changeMusic () {
      this.voice = this.question.voice
      this.$refs.music.load()
      if (this.sound) {
        this.$refs.music.pause()
        this.sound = false
      } else {
        this.sound = true
        this.$refs.music.play()
      }
    },
    mouseover () {
      this.$refs.voice.style = "display:none"
      this.$refs.synopsis.style = "display:block"
    },
    mouseleave () {
      this.$refs.synopsis.style = "display:none"
      this.$refs.voice.style = "display:flex"

    },
    timing () { // 计时
      let fun = () => {
        if (!this.isStop) {
          this.second++;
          setTimeout(() => {
            fun()
          }, 1000);
        }
      }
      setTimeout(() => {
        fun()
      }, 1000);
    },
    start () { // 开始
      this.level = Number(this.info.level) || 1
      this.startProcess()
      this.status = 3
      // this.timing()
    },
    isStart () { //处理开始页面背景
      this.status = 2
    },
    stop () { // 停止
      this.isStop = true
      this.show = true
    },
    cancel () {  // 继续游戏, 继续计时
      this.isStop = false
      this.timing()
    },
    startProcess () { //处理题目
      let unit = this.unit
      this.question = this.formData[unit]
      this.total = this.formData.length
    },
    isRight () {
      this.isSelect = true
      let unitPoint = Math.floor(100 / this.total)
      this.isAnswer = 0
      this.number += unitPoint
      this.succesNum++
      this.next()
    },
    isError () {
      this.isSelect = true
      this.isAnswer = 1
      this.errorNum++
      this.next()
    },
    getTime (seconds) {
      let hour = Math.floor(seconds / 3600) >= 10 ? Math.floor(seconds / 3600) : '0' + Math.floor(seconds / 3600);
      seconds -= 3600 * hour;
      let min = Math.floor(seconds / 60) >= 10 ? Math.floor(seconds / 60) : '0' + Math.floor(seconds / 60);
      seconds -= 60 * min;
      let sec = seconds >= 10 ? seconds : '0' + seconds;
      return hour + ':' + min + ':' + sec;
    },
    next () { // 完成 下一题
      this.$refs.music.pause()
      this.sound = false
      setTimeout(() => {
        this.isAnswer = -1
        if (this.unit < this.total - 1) {
          this.unit++
          this.isSelect = false
          this.startProcess()
        } else {
          this.submit()
        }
      }, 500)

    },
    submit () {  // 提交
      if (this.succesNum == this.total) {
        this.number = 100
      }
      this.isStop = true
      this.store = this.number
      this.infos[0].value = this.level
      this.infos[1].value = api.getTime(this.second)
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },
    again () { //重新开始
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.unit = 0
      this.question = ""
      this.isStop = false
      this.isSelect = false
      this.start()
      this.timing()
    },
  }
}
</script>

<style lang="scss" scoped>
.setting-page {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.game154-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game149/bg.png');
  }
  .page-bg1 {
    width: 1576px;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game144/bg2.png');
  }
  .game-synopsis {
    width: 1235px;
    height: 600px;
    display: flex;
    flex-direction: column;
    padding: 0 64px;
    background-image: url('/static/game_assets/game144/bg2.png');
    background-size: 100% 100%;
    margin-top: 141px;

    .synopsis-title {
      width: 100%;
      height: 160px;
      text-align: center;
      font-size: 3rem;
      line-height: 3rem;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: bold;
      color: #a83a01;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0;
    }
    .synopsis-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      p {
        font-size: 2rem;
        line-height: 3rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #a83a01;
      }
    }
  }
  .page-content {
    width: 100%;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    padding: 0 41px 93px;

    .top {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      padding-top: 36px;
      .info {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        .contanier {
          width: 780px;

          height: 89px;
          background: rgba(0, 0, 0, 0.58);
          border-radius: 45px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 33px;
          .info-item {
            min-width: 175px;
            font-size: 2rem;
            line-height: 3rem;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            white-space: nowrap;
          }
          .time {
            min-width: 280px;
            width: 280px;
          }
        }
        .desc {
          width: 126px;
          height: 88px;
          font-size: 4rem;
          line-height: 3rem;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #a83a01;
          display: flex;
          justify-content: center;
          align-items: center;
          background-image: url('/static/game_assets/game144/desc.png');
          background-size: 100% 100%;
          background-repeat: no-repeat;
          margin-left: 40px;
        }
      }
    }
    .container {
      width: 100%;
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      .voice {
        height: 105px;
        background: #57a8bb;
        border-radius: 53px;
        padding: 0 42px 0 12px;
        display: flex;
        align-items: center;
        &-icon {
          width: 91px;
          height: 91px;
          margin-right: 23px;
        }
        font-size: 3rem;
        line-height: 3rem;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
      }
    }
    .botton {
      height: 100px;
      display: flex;
      align-items: center;
      font-size: 2rem;
      line-height: 3rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #a83a01;
      .right {
        background-image: url('/static/game_assets/game144/right.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 46px;
      }
      .error {
        background-image: url('/static/game_assets/game144/error.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-left: 46px;
      }
      .btn {
        width: 294px;
        height: 83px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .check_answer {
      width: 100%;
      height: 100%;
      position: fixed;
      z-index: 999;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba(0, 0, 0, 0.3);
      // top: 40%;
      &_icon {
        width: 460px;
        height: 460px;
      }
    }
  }
}
</style>

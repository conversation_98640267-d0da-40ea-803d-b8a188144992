<template>
  <div class="game189-page">
    <div class="page-bg"></div>
    <settingPage title="应用数学" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、该训练提高用户的数学应用能力和解决问题的能力。</p>
        <p class="synopsis-content">2、用户遵医嘱选择训练方式，然后点击“开始”进行训练。</p>

        <p class="synopsis-title">训练方式</p>
        <div class="synopsis-choose">
          <div class="item">
            <div class="item-icon" @click="setChoose(1)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 1" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">加法</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(2)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 2" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">减法</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(3)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 3" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">乘法</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(4)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 4" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">除法</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(5)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 5" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">混合</span>
          </div>
        </div>
      </div>
    </settingPage>

    <div class="game-content" v-if="status === 3">
      <div class="title">应用数学</div>

      <div class="content">
        <div class="content-top">{{currect.text}}</div>

        <div class="content-bottom">
          <div class="bottom-item" v-for="item in currect.answerList" :key="item + 'item'" @click="chooseItem(item)">
            <img class="num-bg" src="/static/game_assets/game136/btn_bg_1.png" />
            <img v-if="answer === item" class="num-bg" src="/static/game_assets/game136/btn_bg_3.png" />
            <span :class="['num', answer === item && 'choose-num']">{{item}}</span>
          </div>
        </div>
      </div>

      <div class="footer">
        <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
        <img v-show="isShowCorrect || isShowError" class="img" src="/static/game_assets/common/next.png" @click="toNext" />
        <img v-show="isShowCorrect || isShowError" class="img" src="/static/game_assets/common/reset.png" @click="reset" />
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'
// 5道题
 
export default {
  name: 'game189',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      level: 1,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,

      type: 1, // 训练模式
      answer: null,
      questions: [],
      currect: {
        text: 0,
        answer: 0,
        answerList: []
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
      // this.timing()
      if (this.type === 1) {
        this.questions = api.getRandomArray(data.addList, 5)
      } else if (this.type === 2) {
        this.questions = api.getRandomArray(data.reduceList, 5)
      } else if (this.type === 3) {
        this.questions = api.getRandomArray(data.tackList, 5)
      } else if (this.type === 4) {
        this.questions = api.getRandomArray(data.divideList, 5)
      } else {
        const type = api.randomNum(1, 4)
        this.questions = api.getRandomArray(data.addList, 1)
        this.questions.push(api.getRandomArray(data.reduceList, 1)[0])
        this.questions.push(api.getRandomArray(data.tackList, 1)[0])
        this.questions.push(api.getRandomArray(data.divideList, 1)[0])
        if (type === 1) {
          const list = data.addList.filter(item => item.text !== this.questions[0].text)
          this.questions.push(api.getRandomArray(list, 1)[0])
        } else if (type === 2) {
          const list = data.reduceList.filter(item => item.text !== this.questions[1].text)
          this.questions.push(api.getRandomArray(list, 1)[0])
        } else if (type === 3) {
          const list = data.tackList.filter(item => item.text !== this.questions[2].text)
          this.questions.push(api.getRandomArray(list, 1)[0])
        } else {
          const list = data.divideList.filter(item => item.text !== this.questions[3].text)
          this.questions.push(api.getRandomArray(list, 1)[0])
        }
        this.questions = api.shuffle(this.questions)
      }
      this.startProcess()
    },

    startProcess() {
      this.currect.text = this.questions[this.number].text
      this.currect.answer = this.questions[this.number].answer
      this.currect.answerList = []
      const index = api.randomNum(0, 3)
      for (let i = 0; i < 4; i++) {
        if (i === index) {
          this.currect.answerList.push(this.currect.answer)
        } else {
          this.currect.answer - index > 0 ? this.currect.answerList.push(this.currect.answer - index + i) : this.currect.answerList.push(this.currect.answer + index + i)
        }
      }
    },

    setChoose(item) {
      this.type = item
    },

    chooseItem(item) {
      if (this.isShowCorrect || this.isShowError) return

      this.answer = item
      if (this.answer === this.currect.answer) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }
    },

    toNext() {
      this.number++
      this.answer = null
      this.isShowCorrect = false
      this.isShowError = false
      if (this.number >= 5) {
        this.submit()
      } else {
        this.startProcess()
      }
    },

    reset() {
      if (this.isShowCorrect) {
        this.succesNum--
      } else {
        this.errorNum--
      }
      this.answer = null
      this.isShowCorrect = false
      this.isShowError = false
    },

    stop() {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isStop = true
      this.store = this.succesNum * 20
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answer = null
      this.questions = []
      this.currect = {
        text: 0,
        answer: 0,
        answerList: []
      }
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game189-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
     background-image: url("/static/game_assets/game189/bg.png");
  }

  .game-synopsis {
    width: 860px;
    height: 600px;
    margin: 34px;
    padding: 33px 30px;
    background: #FFFEF3;
    border-radius: 36px;

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #5E381F;
      padding-bottom: 18px;
    }

    .synopsis-content {
      padding-bottom: 18px;
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 400;
      color: #5E381F;
    }

    .synopsis-choose {
      display: flex;
      flex-wrap: wrap;
      width: 550px;

      .item {
        position: relative;
        display: inline-flex;
        align-items: center;
        padding-right: 25px;
        padding-bottom: 16px;

        .item-icon {
          position: relative;
          width: 41px;
          height: 35px;
          display: inline-flex;
          justify-content: center;
          cursor: pointer;

          .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 41px;
          }

          .icon1 {
            position: absolute;
            width: 41px;
          }
        }

        .item-text {
          display: inline-block;
          padding-left: 13px;
          padding-top: 10px;
          font-size: 36px;
          line-height: 50px;
          font-weight: 600;
          color: #5E381F;
        }
      }
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .title {
      position: absolute;
      top: 37px;
      font-size: 50px;
      line-height: 70px;
      font-weight: 600;
      color: #5E381F;
    }

    .content {
      position: relative;
      width: 1638px;
      height: 500px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .content-top {
        position: relative;
        font-size: 42px;
        line-height: 59px;
        text-align: center;
        color: #5E381F;
      }

      .content-bottom {
        width: 100%;
        padding: 0 85px;
        display: flex;
        justify-content: space-between;

        .bottom-item {
          position: relative;
          width: 178px;
          height: 178px;
          padding-bottom: 18px;
          cursor: pointer;

          .num-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 178px;
          }

          .num {
            position: relative;
            display: block;
            font-size: 78px;
            line-height: 160px;
            text-align: center;
            font-family: Impact;
            color: #F48B4D;
          }

          .choose-num {
            color: #fff;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 47px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      width: 1864px;

      .img {
        height: 115px;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px;
      margin-bottom: 40px;
      z-index: 99;
    }
  }
}
</style>
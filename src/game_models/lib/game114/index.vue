<template>
  <div class="game114-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPage @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">本训练用于提高训练者的各种食物认知。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="left-icon">
        <img class="icon" src="/static/game_assets/game114/icon.png" />

        <div class="left-content" v-if="answerStatus === 2">
          <img class="img-bg" src="/static/game_assets/game114/content_bg.png" />
          <img class="img" :src="`/static/game_assets/game114/item_${answer}.png`" />
        </div>
      </div>

      <div class="content1" v-if="answerStatus === 1">
        <img class="content-bg" src="/static/game_assets/game8/content_bg.png" />
        <img class="left-btn" @click="toPrevious" src="/static/game_assets/game107/left.png" />
        <img class="right-btn" @click="toNext" src="/static/game_assets/game107/right.png" />
        
        <img class="content-img" :src="`/static/game_assets/game114/item_${questions[number].index}.png`" />
        <p class="content-text">{{questions[number].name}}</p>
      </div>

      <div class="content2" v-if="answerStatus === 2">
        <div :class="['content-item', choose === item.index && 'choose-item']" v-for="item in questions" :key="item.index + 'img'" @click="chooseItem(item.index)">
          <img class="item-img" :src="`/static/game_assets/game114/item_${item.index}.png`" />
        </div>
      </div>
      
      <div class="btn-group">
        <div class="btn" @click="stop">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>

        <div class="btn" v-if="answerStatus === 1 || answerStatus === 2 && choose" @click="startGame">
          <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
          <span class="text">{{answerStatus === 1 ? '开始训练' : '提交'}}</span>
        </div>
      </div>

      <div class="judge" v-if="isShowCorrect || isShowError">
        <img class="img" :src="`/static/game_assets/game8/${isShowCorrect ? 'success' : 'error'}.png`">
      </div>
    </div>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'
// 每轮游戏3个回合

export default {
  name: 'game114',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data() {
    return {
      musicUrl: '',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      show: false,
      status: 1,

      isPlayTitle: false,
      isPlay: false,
      isShowCorrect: false,
      isShowError: false,
      answerStatus: 1, // 1--学习 2--测试
      questions: [
        {
          name: '薯条',
          index: 1
        },
        {
          name: '巧克力',
          index: 2
        },
        {
          name: '鸡肉',
          index: 3
        },
        {
          name: '面条',
          index: 4
        },
        {
          name: '牛奶',
          index: 5
        }
      ],
      answer: 0,
      index: 0, 
      choose: 0,     
      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play(url) {
      if (url) {
        this.musicUrl = url
        this.$refs.music.load()
      }
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnded() {
      this.isPlay = false
      if (this.isPlayTitle) {
        this.isPlayTitle = false
        this.play(`/static/game_assets/audio/game114/item_${this.answer}.mp3`)
      }
    },

    start() {
      this.status = 3 
      this.play(`/static/game_assets/audio/game114/item_${this.questions[this.number].index}.mp3`)     
    },

    toPrevious() {
      if (this.number > 0) {
        this.number--
        this.play(`/static/game_assets/audio/game114/item_${this.questions[this.number].index}.mp3`)
      } else {
        this.number = 4
        this.play(`/static/game_assets/audio/game114/item_${this.questions[this.number].index}.mp3`)
      }
    },

    toNext() {
      if (this.number >= 4) {
        this.number = 0
        this.play(`/static/game_assets/audio/game114/item_${this.questions[this.number].index}.mp3`)
      } else {
        this.number++
        this.play(`/static/game_assets/audio/game114/item_${this.questions[this.number].index}.mp3`)
      }
    },

    startGame() {
      if (this.isShowCorrect || this.isShowError || this.isPlayTitle) return

      if (this.answerStatus === 1) {
        this.answerStatus = 2
        this.startProcess()
        this.play('/static/game_assets/audio/game114/title.mp3')
        this.isPlayTitle = true
        // this.timing()
      } else {
        if (this.choose === this.answer) {
          this.succesNum++
          this.isShowCorrect = true
        } else {
          this.errorNum++
          this.isShowError = true
        }

        setTimeout(() => {
          this.isShowCorrect = false
          this.isShowError = false

          if (this.answer >= 5) {
            this.submit()
          } else {
            this.choose = 0
            this.startProcess()
            this.play(`/static/game_assets/audio/game114/item_${this.answer}.mp3`)
          }
        }, 800)
      }
    },

    // 开始流程
    async startProcess() {
      this.questions = api.shuffle(this.questions)
      this.answer++
    },

    chooseItem(item) {
      if (this.isPlayTitle) return
      this.choose = item
    },

    stop() {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
      if (this.isPlay) this.pause()
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.play()
      if (this.answerStatus === 1) return
      this.timing()
    },

    submit() {
      this.pause()
      this.isStop = true
      this.store = 20 * this.succesNum
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again() {
      this.isStop = false
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.status = 3
      this.choose = 0
      this.answer = 0
      this.answerStatus = 1
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game114-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game114/bg.png");
  }

  .game-synopsis {

    .synopsis-content {
      padding-top: 20px;
      margin: 0;
      font-size: 30px;
      line-height: 42px;
      font-weight: 400;
      color: #A83A01;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .left-icon {
      position: absolute;
      bottom: 125px;
      left: 40px;
      width: 328px;
      height: 366px;

      .icon {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 158px;
      }

      .left-content {
        position: absolute;
        top: 0;
        right: 0;
        width: 161px;
        height: 178px;

        .img-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 161px;
        }

        .img {
          position: absolute;
          top: 15px;
          right: 17px;
          width: 128px;
        }
      }
    }

    .content1 {
      position: relative;
      width: 1253px;
      height: 708px;
      margin-bottom: 82px;
      padding: 98px 467px 0 430px;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1253px;
      }

      .left-btn {
        position: absolute;
        top: 300px;
        left: 30px;
        width: 64px;
        cursor: pointer;
      }

      .right-btn {
        position: absolute;
        top: 300px;
        right: 48px;
        width: 64px;
        cursor: pointer;
      }

      .content-img {
        position: relative;
        width: 356px;
      }

      .content-text {
        position: relative;
        padding: 52px;
        font-size: 74px;
        line-height: 97px;
        text-align: center;
        color: #A83A01;
      }
    }

    .content2 {
      position: relative;
      width: 1182px;
      height: 788px;
      margin-left: 128px;
      margin-bottom: 96px;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;

      .content-item {
        position: relative;
        margin: 34px;
        width: 326px;
        height: 326px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 5px solid #EA9A22;
        background: #FFFCB0;
        cursor: pointer;

        .item-img {
          width: 214px;
        }
      }

      .choose-item {
        border-color: #FF2E2E;
        background: #B0FFCC;
      }
    }

    .btn-group {
      position: absolute;
      bottom: 82px;
      width: 1450px;
      height: 84px;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px;
        height: 84px;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px;
          height: 84px;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px 0 21px 0;
          font-size: 38px;
          line-height: 53px;
          text-align: center;
          color: #A83A01;
        }
      }
    }

    .judge {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding-top: 10px;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      justify-content: center;
      align-items: center;

      .img {
        width: 460px;
        height: 460px;
      }
    }
  }
}
</style>
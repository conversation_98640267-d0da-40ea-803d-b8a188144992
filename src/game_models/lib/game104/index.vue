<template>
  <div class="game104-page">
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <div class="page-bg"></div>
		<settingPage title="认识职业" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、通过本训练的教学，提高训练者的各种职业认知;</p>
        <p class="synopsis-content">2、一共七部分，请根据提示进行操作;</p>
        <p class="synopsis-content">3、本题学习内容为卡通形象。</p>
      </div>
    </settingPage>
    <div :class="['game-content', (number === 0 || number === 1 || number === 5 || number === 7) && 'black-bg']" v-if="status === 3">
      <div class="top">
        <img v-if="isPlay" class="top-icon" src="/static/game_assets/common/play.png" />
				<img v-else class="top-icon" src="/static/game_assets/common/pause.png" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>

      <div class="content">
        <img class="left-btn" v-if="number > 0" @click="toPrevious" src="/static/game_assets/game104/left_btn.png" />
        <img class="right-btn" v-if="number < 7" @click="toNext" src="/static/game_assets/game104/right_btn.png" />
        
        <!-- 学习环节+训练1 -->
        <div class="content-1" v-if="number === 0 || number === 1">
          <div class="content-title">
            <template v-if="number === 1">
              <img class="title-icon" src="/static/game_assets/game104/img7.png" />
              <img class="title-bg" src="/static/game_assets/game104/text_bg.png" />
              <span class="title-text">{{current.name}}</span>
            </template>
          </div>

          <div class="content-main">
            <div :class="['content-item', itemClass(1, index)]" v-for="(item, index) in question" :key="index + 'item'" @click="chooseItem(item.index)">
              <img class="item-img" :src="`/static/game_assets/game104/item_${item.index}.png`" />
              
              <template v-if="number === 1">
                <img class="item-icon" src="/static/game_assets/game104/img6.png" />
                <img class="icon-bg" src="/static/game_assets/game104/icon_bg.png" />
              </template>

              <img v-if="number === 1 && choose === item.index && isShowCorrect" class="icon" src="/static/game_assets/game104/icon.png" />
            </div>
          </div>
        </div>
        
        <!-- 训练2 -->
        <div class="content-2" v-if="number === 2">
          <div class="content-left">
            <img class="left-bg" src="/static/game_assets/game104/content_bg3.png" />
            <div class="left-content">
              <div class="left-item" v-for="item in 9" :key="item + 'item'">
                <draggable 
                  class="left-item-wapper" 
                   v-model="dragItemTop[item-1]"
                  :group="group1" 
                  @start="dragStart1(item)"
                >
                  <img v-for="it in dragItemTop[item-1]" :key="it.name + 'img'" :class="['left-img', 'left-img-' + it.name]" :src="`/static/game_assets/game104/item_${current.index}_${it.name}.png`" />
                </draggable>
              </div>
            </div>
          </div>

          <div class="content-right">
            <img class="right-img" :src="`/static/game_assets/game104/item_${current.index}.png`" />
            <img class="right-bg" src="/static/game_assets/game104/content_bg4.png" />

            <div class="right-content">
              <img class="right-content-bg" src="/static/game_assets/game104/content_bg2.png" />
              <draggable 
                :class="['right-item', 'right-item-' + item]" 
                :style="{'z-index': choose === item ? 99 : 0}" 
                v-for="item in 9" 
                :key="item + 'item'" 
                v-model="dragItemBottom[item-1]" 
                :group="group2" 
                @add="dragAdd(item)"
              >
                <img :class="['right-img', 'right-img-' + item]" :src="`/static/game_assets/game104/puzzle_${item}.png`" />
                <img v-for="it in dragItemBottom[item-1]" :key="it.name + 'img'" :class="['left-img', 'left-img-' + it.name]" :src="`/static/game_assets/game104/item_${current.index}_${it.name}.png`" />
              </draggable>
            </div>
          </div>
        </div>
      
        <!-- 训练3 -->
        <div class="content-3" v-if="number === 3">
          <img class="content-bg" src="/static/game_assets/game104/content_bg1.png" />

          <div class="content-left">
            <div class="left-item" v-for="item in question" :key="item.index + 'item'">
              <div class="item-left">
                <img class="item-bg" src="/static/game_assets/game104/text_bg.png" />
                <span class="item-text">{{item.name}}</span>
              </div>

              <img class="item-right" src="/static/game_assets/game104/img5.png">
            </div>
          </div>

          <div class="content-middle">
            <draggable
              class="middle-item"
              v-for="(item, index) in question"
              :key="item.index + 'item2'"
              v-model="dragItemLeft[index]" 
              :group="group2"
              @add="dragAdd2(index)"
            >
              <img class="item-icon" src="/static/game_assets/game104/img6.png" />
              <img v-for="it in dragItemLeft[index]" :key="it.name + 'img1'" class="right-img" :src="`/static/game_assets/game104/item_${it.name}.png`" />
            </draggable>
          </div>

          <div class="content-right">
            <div class="right-item" v-for="item in 3" :key="item + 'item3'">
              <draggable 
                class="right-item-wapper" 
                :group="group1" 
                v-model="dragItemRight[item-1]"
                @start="dragStart2(item)"
              >
                <img v-for="it in dragItemRight[item-1]" :key="it.name + 'img2'" class="right-img" :src="`/static/game_assets/game104/item_${it.name}.png`" />
              </draggable>
            </div>
          </div>
        </div>

        <!-- 训练4 -->
        <div class="content-4" v-if="number === 4">
          <img class="content-bg" src="/static/game_assets/game104/content_bg1.png" />

          <div class="content-top">
            <img class="top-icon1" src="/static/game_assets/game104/img4.png" />
            <img class="top-icon2" src="/static/game_assets/game104/img3.png" />
            <img class="top-img" src="/static/game_assets/game104/img2.png" />
          </div>

          <div class="content-bottom">
            <div class="bottom-item" v-for="item in question" :key="item.index + 'item4'" @click="chooseItem(item.index)">
              <img class="item-img" :src="`/static/game_assets/game104/item_${item.index}.png`" />

              <img class="icon-bg" src="/static/game_assets/game104/icon_bg.png" />
              <img v-if="choose === item.index && isShowCorrect" class="icon" src="/static/game_assets/game104/icon.png" />
            </div>
          </div>
        </div>

        <!-- 训练5 + 训练7 -->
        <div class="content-5" v-if="number === 5 || number === 7">
          <div class="content-item" v-for="(item, index) in question" :key="index + 'item5'" @click="openItem(item, index)">
            <div :class="['item', itemClass(2, item, index)]">
              <img class="bg" src="/static/game_assets/game104/item_bg1.png" />
              <img class="icon1" src="/static/game_assets/game104/img6.png" />
            </div>
            <div :class="['item', itemClass(2, item, index)]">
              <img v-if="number === 5" class="main1" :src="`/static/game_assets/game104/item_${item.index}.png`" />

              <template v-if="number === 7">
                <img class="bg" src="/static/game_assets/game104/item_bg1.png" />
                <img class="main2" src="/static/game_assets/game104/img7.png" />
              </template>
            </div>
          </div>
        </div>

        <!-- 训练6 -->
        <div class="content-6" v-if="number === 6">
          <img class="content-bg" src="/static/game_assets/game104/content_bg1.png" />

          <div class="content-top">
            <img class="top-icon1" src="/static/game_assets/game104/img4.png" />
            <img class="top-icon2" src="/static/game_assets/game104/img7.png" />
            <div class="top-title">
              <img class="title-bg" src="/static/game_assets/game104/text_bg.png" />
              <span class="title-text">{{current.name}}</span>
            </div>
            <img class="top-img" src="/static/game_assets/game104/img1.png" />
          </div>

          <div class="content-bottom">
            <div class="bottom-item" v-for="item in question" :key="item.index + 'item'" @click="chooseItem(item.index)">
              <img class="item-img" :src="`/static/game_assets/game104/item_${item.index}.png`" />

              <img class="icon-bg" src="/static/game_assets/game104/icon_bg.png" />
              <img v-if="choose === item.index && isShowCorrect" class="icon" src="/static/game_assets/game104/icon.png" />
            </div>
          </div>
        </div>
      </div>

      <div :class="['sucess', isShowCorrect ? 'translate-top' : '']" :style="{'opacity': isShowCorrect ? 1 : 0}">
        <img class="img" src="/static/game_assets/game29/hot_balloon.png">
      </div>
    </div>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import api from '../../utils/common.js'

export default {
  name: 'game104',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    draggable
  },

  data() {
    return {
      musicUrl: '',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,
      isShowCorrect: false,
      isCanOpen: true,

      timer: null,
      index: 0,
			question: [],
      current: {},
      choose: 0,
      answer: [],
      dragItemTop: [],
      dragItemBottom: [[], [], [], [], [], [], [], [], []],
      dragItemLeft: [[], [], []],
      dragItemRight: [],
      group1: {
        name: 'itemList',
        pull: true,
        put: false,
        sort: false
      },
      group2: {
        name: 'itemList',
        pull: false,
        put: true,
        sort: false
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    itemClass(type, item, index) {
      if (type === 1 && this.number === 0) {
        if (this.index > item) {
          return 'rotate-item'
        }
      } else {
        const list1 = this.answer.filter(it => it.index === item.index)
        const list2 = this.answer.filter(it => it.sort === index)
        if (list1.length && list2.length) {
          return 'flip-item'
        }
      }
    },

    // TODO: 返回首页
		goHome() {
      this.pause()
			this.$router.go(-1)
		},

		play(url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },
  
    handleEnded() {
      this.isPlay = false
      this.index++
      setTimeout(() => {
        if (this.number) return

        if (this.index < 6) {
          this.play(`/static/game_assets/audio/game104/audio_${this.question[this.index].index}.mp3`)
        } else {
          this.number++
          this.startProcess()
        }
      }, 1200)
    },

    start() {
      this.status = 3
      this.startProcess()
      // this.timing()
    },

    startProcess() {
      const list = [
        {
          name: '教师',
          index: 1
        },
        {
          name: '医生',
          index: 2
        },
        {
          name: '工人',
          index: 3
        },
        {
          name: '厨师',
          index: 4
        },
        {
          name: '园丁',
          index: 5
        },
        {
          name: '科学家',
          index: 6
        }
      ]

      this.index = 0
      if (this.number === 1 || this.number === 2) {
        this.current = api.getRandomArray(list, 1)[0]
      }
      if (this.number === 0 || this.number === 1) {
        this.question = api.shuffle(list)
      }
      if (this.number === 0) {
        this.play(`/static/game_assets/audio/game104/audio_${this.question[this.index].index}.mp3`)
      }
      if (this.number === 2) {
        let li = [1, 2, 3, 4, 5, 6, 7, 8, 9]
        li = api.shuffle(li)

        for (let i = 0; i < 9; i++) {
          const item = li[i]
          this.dragItemTop.push([{
            name: item,
            startIndex: i,
          }])
        }
      }
      if (this.number === 3 || this.number === 4 || this.number === 5 || this.number === 6 || this.number === 7) {
        this.question = api.getRandomArray(list, 3)
      }
      if (this.number === 3) {
        const li = api.shuffle([0, 1, 2])
        for (let i = 0; i < 3; i++) {
          const item = this.question[li[i]]
          this.dragItemRight.push([{
            name: item.index,
            index: li[i],
            startIndex: i,
          }])
        }
      }
      if (this.number === 4 || this.number === 6) {
        this.current = api.getRandomArray(this.question, 1)[0]
      }
      if (this.number === 5 || this.number === 7) {
        this.question = this.question.concat(this.question)
        this.question = api.shuffle(this.question)
      }
      if (this.number === 1 || this.number === 2 || this.number === 3 || this.number === 5  || this.number === 7) {
        this.play(`/static/game_assets/audio/game104/title_${this.number}.mp3`)
      }
      if (this.number === 4 || this.number === 6) {
        this.play(`/static/game_assets/audio/game104/audio_${this.current.index}.mp3`)
      }
    },

    toPrevious() {
      if (this.isShowCorrect) return
      clearTimeout(this.timer)
      this.choose = 0
      this.answer = []
      this.dragItemTop = []
      this.dragItemBottom = [[], [], [], [], [], [], [], [], []]
      this.dragItemLeft = [[], [], []]
      this.dragItemRight = []
      this.isShowCorrect = false
      this.number--
      this.startProcess()
    },

    toNext() {
      if (this.isShowCorrect) return
      clearTimeout(this.timer)
      this.choose = 0
      this.answer = []
      this.isShowCorrect = false
      this.number++
      if (this.number === 1 || this.number === 4 || this.number === 6) {
        this.errorNum++
      }
      if (this.number === 2) {
        const list = this.dragItemBottom.filter(item => item.length)
        this.errorNum = this.errorNum + 9 - list.length
      }
      if (this.number === 3) {
        const list = this.dragItemLeft.filter(item => item.length)
        this.errorNum = this.errorNum + 3 - list.length
      }
      if (this.number === 5) {
        const choose = this.answer.filter(it => it.sort === index)
        this.errorNum = this.errorNum + 3 - parseInt(choose.length / 2)
      }
      this.startProcess()
    },

    dragStart1(item) {
      this.choose = this.dragItemTop[item - 1][0].name
    },

    dragStart2(item) {
      this.choose = this.dragItemRight[item - 1][0].index
    },

    dragAdd(index) {
      if (this.choose !== index) {
        if (this.dragItemBottom[index - 1].length > 1) {
          const item = this.dragItemBottom[index - 1].filter(item => item.name !== index)[0]
          this.dragItemBottom[index - 1] = this.dragItemBottom[index - 1].filter(item => item.name === index)
          this.dragItemTop[item.startIndex].push(item)
        } else {
          const item = this.dragItemBottom[index - 1].pop()
          this.dragItemTop[item.startIndex].push(item)
        }
        this.errorNum++
        this.play('/static/game_assets/audio/error_audio.mp3')
      } else {
        this.succesNum++
      }

      const list = this.dragItemBottom.filter(item => item.length)
      if (list.length >= 9) this.goNext()
    },

    dragAdd2(index) {
      if (this.choose !== index) {
        if (this.dragItemLeft[index].length > 1) {
          const item = this.dragItemLeft[index].filter(item => item.index !== index)[0]
          this.dragItemLeft[index] = this.dragItemLeft[index].filter(item => item.index === index)
          this.dragItemRight[item.startIndex].push(item)
        } else {
          const item = this.dragItemLeft[index].pop()
          this.dragItemRight[item.startIndex].push(item)
        }
        this.errorNum++
        this.play('/static/game_assets/audio/error_audio.mp3')
      } else {
        this.succesNum++
      }

      const list = this.dragItemLeft.filter(item => item.length)
      if (list.length >= 3) this.goNext()
    },

    chooseItem(item) {
      if (!this.number) return

			this.choose = item
      if (this.choose === this.current.index) {
        this.succesNum++
        this.goNext()
      } else {
        this.errorNum++
        this.play('/static/game_assets/audio/error_audio.mp3')
      }
    },

    openItem(item, index) {
      const choose = this.answer.filter(it => it.sort === index)
      if (!this.isCanOpen || choose.length) return

      this.answer.push({
        ...item,
        sort: index
      })
      if (this.number === 7) this.play(`/static/game_assets/audio/game104/audio_${item.index}.mp3`)
      if (this.answer.length && !(this.answer.length % 2)) {
        const it = this.answer[this.answer.length - 2]
        if (it.index !== item.index) {
          this.errorNum++
          this.isCanOpen = false
          setTimeout(() => {
            this.answer.pop()
            this.answer.pop()
            this.isCanOpen = true

            if (this.answer.length >= 6) this.goNext()
          }, 500)
        } else {
          this.succesNum++
          if (this.answer.length >= 6) this.goNext()
        }
      }
    },

    goNext() {
      this.isShowCorrect = true
      this.timer = setTimeout(() => {
        this.timer = null
        this.choose = 0
        this.answer = []
        this.isShowCorrect = false
        this.number++

        if (this.number > 7) {
          this.submit()
        } else {
          this.startProcess()
        }
      }, 2200)
    },

    submit() {
      this.isStop = true
      this.store = parseInt(100 / (this.succesNum + this.errorNum) * this.succesNum)
			this.infos[0].value = this.second
			this.infos[1].value = this.succesNum
			this.infos[2].value = this.errorNum
			this.status = 4
			this.params = {
				id: this.info.id,
				grade: '',
				time: this.second,
				totalPoints: this.store
			}
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
			this.choose = 0
      this.answer = []
      this.dragItemTop = []
      this.dragItemBottom = [[], [], [], [], [], [], [], [], []]
      this.dragItemLeft = [[], [], []]
      this.dragItemRight = []
			this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game104-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game104/bg.png");
  } 

  .game-synopsis {
    width: 1576px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    .top {
			position: absolute;
			top: 40px;
			left: 105px;
			width: 175px;
			display: flex;
			justify-content: space-between;

			.top-icon {
				width: 80px;
				cursor: pointer;
			}
		}

    .content {
      position: relative;
      width: 1920px;
      height: 1080px;
      display: flex;
      justify-content: center;
      align-items: center;

      .left-btn {
        position: absolute;
        top: 525px;
        left: 80px;
        width: 131px;
        cursor: pointer;
      }

      .right-btn {
        position: absolute;
        top: 525px;
        right: 80px;
        width: 131px;
        cursor: pointer;
      }

      .content-1 {
        position: relative;
        width: 1047px;
        height: 843px;
        margin-bottom: 47px;
        
        .content-title {
          position: relative;
          width: 380px;
          height: 122px;
          margin-left: 308px;

          .title-icon {
            position: absolute;
            top: 0;
            left: 0;
            width: 114px;
            z-index: 1;
          }

          .title-bg {
            position: absolute;
            top: 0;
            right: 0;
            width: 314px;
          }

          .title-text {
            position: relative;
            display: block;
            padding: 29px 0 44px 68px;
            font-size: 47px;
            line-height: 47px;
            text-align: center;
            font-weight: 500;
            color: #fff;
          }
        }

        .content-main {
          height: 721px;
          padding-top: 30px;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;

          .rotate-item {
            transform:rotate(1080deg) scale(0);
          }

          .content-item {
            position: relative;
            width: 334px;
            height: 335px;
            transition: All 1s ease-in-out;
            cursor: pointer;

            .item-img {
              position: absolute;
              top: 0;
              left: 0;
              width: 334px;
            }

            .item-icon {
              position: absolute;
              top: 93px;
              left: 118px;
              width: 98px;
            }

            .icon-bg {
              position: absolute;
              right: 16px;
              bottom: 12px;
              width: 78px;
            }

            .icon {
              position: absolute;
              right: 34px;
              bottom: 38px;
              width: 44px;
            }
          }
        }
      }

      .content-2 {
        position: relative;
        width: 1251px;
        height: 891px;
        margin-top: 47px;
        margin-left: 15px;

        .content-left {
          position: absolute;
          left: 0;
          top: 0;
          width: 976px;
          height: 891px;

          .left-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 976px;
          }

          .left-content {
            position: relative;
            padding: 80px 250px 185px 85px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-content: space-between;

            .left-item {
              width: 200px;
              height: 200px;
              display: inline-flex;
              justify-content: center;
              align-items: center;

              .left-item-wapper {
                .left-img {
                  cursor: pointer;
                }

                .left-img-1 {
                  width: 160px;
                  height: 122px;
                }

                .left-img-2 {
                  width: 121px;
                  height: 161px;
                }

                .left-img-3 {
                  width: 159px;
                  height: 122px;
                }

                .left-img-4 {
                  width: 122px;
                  height: 200px;
                }

                .left-img-5 {
                  width: 199px;
                  height: 122px;
                }

                .left-img-6 {
                  width: 121px;
                  height: 200px;
                }

                .left-img-7 {
                  width: 160px;
                  height: 121px;
                }

                .left-img-8 {
                  width: 122px;
                  height: 160px;
                }

                .left-img-9 {
                  width: 160px;
                  height: 121px;
                }
              }
            }
          }
        }

        .content-right {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 519px;
          height: 818px;
          padding: 286px 50px 122px 59px;

          .right-img {
            position: absolute;
            top: 0;
            right: 50px;
            width: 210px;
            z-index: 1;
          }

          .right-bg {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 519px;
          }

          .right-content {
            position: relative;

            .right-content-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 410px;
            }

            .right-item {
              position: absolute;
              display: flex;
              justify-content: center;
              align-items: center;

              .right-img {
                position: absolute;
                top: 0;
                left: 0;
              }

              .left-img {
                position: relative;
                z-index: 2;
              }

              .right-img-1, .right-img-7, .right-img-9 {
                width: 195px;
              }

              .right-img-2, .right-img-6 {
                width: 156px;
              }

              .right-img-3 {
                width: 194px;
              }

              .right-img-4, .right-img-8 {
                width: 157px;
              }

              .right-img-5 {
                width: 234px;
              }

              .left-img-1, .left-img-7, .left-img-9 {
                width: 160px;
              }

              .left-img-2, .left-img-6 {
                width: 121px;
              }

              .left-img-3 {
                width: 159px;
              }

              .left-img-4, .left-img-8 {
                width: 122px;
              }

              .left-img-5 {
                width: 199px;
              }
            }

            .right-item-1 {
              top: 0;
              left: 0;
              width: 195px;
              height: 157px;
            }

            .right-item-2 {
              top: 0;
              left: 122px;
              width: 156px;
              height: 196px;
            }

            .right-item-3 {
              top: 0;
              left: 206px;
              width: 194px;
              height: 157px;
            }

            .right-item-4 {
              top: 83px;
              left: 0;
              width: 157px;
              height: 235px;
            }

            .right-item-5 {
              top: 122px;
              left: 83px;
              width: 234px;
              height: 157px;
            }

            .right-item-6 {
              top: 84px;
              left: 244px;
              width: 156px;
              height: 235px;
            }

            .right-item-7 {
              top: 245px;
              left: 0;
              width: 195px;
              height: 156px;
            }

            .right-item-8 {
              top: 206px;
              left: 121px;
              width: 157px;
              height: 195px;
            }

            .right-item-9 {
              top: 245px;
              left: 206px;
              width: 195px;
              height: 156px;
            }
          }
        }
      }

      .content-3 {
        position: relative;
        width: 1446px;
        height: 899px;
        margin-top: 95px;
        padding: 117px 272px 122px 252px;
        display: flex;
        justify-content: space-between;

        .content-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 1446px;
        }

        .content-left {
          display: flex;
          flex-direction: column;
          width: 416px;
          justify-content: space-between;

          .left-item {
            display: inline-flex;
            justify-content: space-between;
            align-items: center;
            height: 209px;

            .item-left {
              position: relative;
              width: 314px;
              height: 118px;

              .item-bg {
                position: absolute;
                top: 0;
                left: 0;
                width: 314px;
              }

              .item-text {
                position: relative;
                display: block;
                padding: 27px 0 44px 0;
                font-size: 47px;
                line-height: 47px;
                font-weight: 500;
                text-align: center;
                color: #fff;
              }
            }

            .item-right {
              position: relative;
              width: 62px;
              height: 43px;
            }
          }
        }

        .content-middle {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          width: 208px;

          .middle-item {
            position: relative;
            width: 208px;
            height: 209px;
            background: #C9C9C9;
            border-radius: 48px;

            .item-icon {
              position: absolute;
              top: 32px;
              left: 53px;
              width: 98px;
            }
          }
        }

        .content-right {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          width: 208px;
          height: 100%;

          .right-item {
            position: relative;
            width: 208px;
            height: 209px;

            .right-item-wapper {
              width: 208px;
              height: 209px;

              .right-img {
                width: 208px;
                height: 209px;
                cursor: pointer;
              }
            }
          } 
        }

        .right-img {
          position: relative;
          width: 208px;
          height: 209px;
        }
      }

      .content-4 {
        position: relative;
        width: 1446px;
        height: 970px;
        margin-top: 20px;
        padding: 480px 206px 0 193px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .content-bg {
          position: absolute;
          top: 71px;
          left: 0;
          width: 1446px;
        }

        .content-top {
          position: absolute;
          top: 0;
          width: 740px;
          height: 567px;
          margin-right: 25px;

          .top-icon1 {
            position: absolute;
            top: 0;
            left: 253px;
            width: 172px;
            z-index: 2;
          }

          .top-icon2 {
            position: absolute;
            top: 178px;
            left: 201px;
            width: 355px;
            z-index: 1;
          }

          .top-img {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 740px;
            z-index: 0;
          }
        }

        .content-bottom {
          position: relative;
          width: 100%;
          display: flex;
          justify-content: space-between;
          z-index: 1;

          .bottom-item {
            position: relative;
            width: 334px;
            height: 335px;
            cursor: pointer;

            .item-img {
              width: 334px;
              height: 335px;
            }

            .icon-bg {
              position: absolute;
              right: 22px;
              bottom: 14px;
              width: 78px;
            }

            .icon {
              position: absolute;
              right: 38px;
              bottom: 42px;
              width: 44px;
            }
          }
        }
      }

      .content-5 {
        position: relative;
        width: 1083px;
        height: 724px;
        margin-top: 100px;
        display: flex;
        flex-wrap: wrap;

        .content-item > div:first-child {
          z-index: 1;
          backface-visibility: hidden;
        }

        .flip-item {
          transform: rotateY(180deg);
        }

        .content-item {
          position: relative;
          width: 361px;
          height: 362px;
          cursor: pointer;

          .item {
            position: absolute;
            top: 0;
            left: 0;
            width: 361px;
            height: 362px;
            transition: all .5s;
            display: flex;
            justify-content: center;
            align-items: center;

            .bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 361px;
              height: 362px;
            }

            .icon1 {
              position: relative;
              width: 98px;
            }

            .main1 {
              position: relative;
              width: 334px;
            }

            .main2 {
              position: relative;
              width: 182px;
              margin-left: 20px;
            }
          }
        }
      }

      .content-6 {
        position: relative;
        width: 1446px;
        height: 970px;
        margin-top: 20px;
        padding: 0 206px 0 193px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .content-bg {
          position: absolute;
          top: 71px;
          left: 0;
          width: 1446px;
        }

        .content-top {
          position: relative;
          width: 702px;
          height: 446px;
          margin-right: 22px;

          .top-icon1 {
            position: absolute;
            top: 0;
            left: 253px;
            width: 172px;
            z-index: 2;
          }

          .top-icon2 {
            position: absolute;
            top: 178px;
            left: 126px;
            width: 114px;
            z-index: 1;
          }

          .top-title {
            position: absolute;
            top: 181px;
            left: 194px;
            width: 314px;
            height: 118px;

            .title-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 314px;
            }

            .title-text {
              position: relative;
              display: block;
              padding: 27px 0 44px 0;
              font-size: 47px;
              line-height: 47px;
              text-align: center;
              font-weight: 500;
              color: #fff;
            }
          }

          .top-img {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 702px;
          }
        }

        .content-bottom {
          width: 100%;
          padding-top: 34px;
          display: flex;
          justify-content: space-between;

          .bottom-item {
            position: relative;
            width: 334px;
            height: 335px;
            cursor: pointer;

            .item-img {
              width: 334px;
              height: 335px;
            }

            .icon-bg {
              position: absolute;
              right: 22px;
              bottom: 14px;
              width: 78px;
            }

            .icon {
              position: absolute;
              right: 38px;
              bottom: 42px;
              width: 44px;
            }
          }
        }
      }
    }

    .sucess {
      position: absolute;
      bottom: -762px;
      width: 774px;
      height: 762px;
      transition: transform 2s ease-in;
      z-index: 100;

      .img {
        width: 774px;
        height: 762px;
      }
    }

    .translate-top {
      transform: translateY(-2442px);
    }
  }

  .black-bg {
    background: rgba(0, 0, 0, 0.33);
  }
}
</style>
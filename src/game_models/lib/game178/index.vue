<template>
  <div class="game178-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPage title="数形状" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、请根据提示配图，在屏幕正下方数字键盘中输入多少个形状；</p>
        <p class="synopsis-content">2、训练结束后会出现成绩单；</p>
        <p class="synopsis-content">3、本训练难度为A（简单）。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />
        
        <div class="top-text">有多少个{{currect.text}}？</div>
      </div>

      <div class="content">
        <div class="content-top">
          <img class="top-img" v-for="item in currect.answer" :key="item + 'img1'" :src="`/static/game_assets/game178/item_${currect.imgIndex}.png`" />
        </div>

        <div class="content-bottom">
          <div class="left">
            <img v-for="item in 10" :key="item" class="btn" :src="`/static/game_assets/game35/btn_${item}.png`" @click="chooseItem(item)" />
          </div>
          <div class="right">
            <div class="input">
              <img class="img" src="/static/game_assets/game35/input.png" />
              <img class="clear" src="/static/game_assets/game35/clear.png" @click="answer = ''" />
              <span class="number">{{ answer }}</span>
            </div>
            <img class="img" src="/static/game_assets/game35/confirm.png" @click="submit" />
        </div>
        </div>
      </div>

      <div class="footer">
        <div class="footer-right">
          <p class="item">题目分数：{{store}}</p>
          <p class="item">题目数量：{{number}}</p>
          <p class="item">用时：{{time}}</p>
        </div>
      </div>
    </div>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import api from '../../utils/common.js'
 
export default {
  name: 'game178',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    draggable
  },

  data() {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      level: 1,
      status: 1,
      isPlay: false,
      isStop: false,

      answer: '',
      currect: {
        text: '',
        imgIndex: 0,
        answer: 0
      },

      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time() {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play() {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.startProcess()
      this.status = 3
      // this.timing()
      this.play()
    },

    startProcess() {
      const list = [
        {
          index: 1,
          text: '五边形'
        },
        {
          index: 2,
          text: '六边形'
        },
        {
          index: 3,
          text: '长方形'
        },
        {
          index: 4,
          text: '圆形'
        },
        {
          index: 5,
          text: '三角形'
        },
        {
          index: 6,
          text: '正方形'
        },
        {
          index: 7,
          text: '梯形'
        },
        {
          index: 8,
          text: '五角星'
        }
      ]
      this.currect.answer = api.randomNum(1, 10)
      this.currect.imgIndex = api.getRandomArray(list, 1)[0].index
      this.currect.text = list[this.currect.imgIndex - 1].text
      this.number++
    },

    chooseItem(item) {
      if (this.answer.length >= 2) return
      this.answer = this.answer + (item === 10 ? 0 : item).toString()
    },

    submit() {
      if (!this.answer) return
      if (Number(this.answer) === this.currect.answer) {
        this.succesNum++
      } else {
        this.errorNum++
      }
      this.store = 10 * this.succesNum

      if (this.number < 10) {
        this.answer = ''
        this.startProcess()
      } else {
        this.pause()
        this.isStop = true
        this.infos[0].value = this.level
        this.infos[1].value = this.second
        this.infos[2].value = this.succesNum
        this.infos[3].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: this.level,
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.isStop = false
      this.store = 0
      this.answer = ''
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game178-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game176/bg.png");
  }

  .game-synopsis {
    width: 707px;
    height: 444px;
    margin: 34px;
    padding: 33px 30px;
    background: #fff;
    border: 2px solid #58AD49;
    border-radius: 36px;

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #5E381F;
    }

    .synopsis-content {
      padding-top: 18px;
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 400;
      color: #5E381F;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
			position: absolute;
			top: 20px;
      left: 0;
      width: 100%;
      padding: 0 54px;

			.top-icon {
        position: absolute;
        top: 0;
        left: 50px;
				width: 80px;
				cursor: pointer;
        z-index: 1;
			}

      .top-text {
        position: relative;
        font-size: 50px;
        line-height: 82px;
        text-align: center;
        font-weight: 600;
        color: #0B4F5D;
      }
		}

    .content {
      position: relative;
      width: 1434px;
      height: 638px;
      margin-bottom: 40px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;

      .content-top {
        width: 725px;
        height: 290px;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;

        .top-img {
          width: 120px;
          height: 120px;
          margin: 10px;
        }
      }

      .content-bottom {
        width: 1434px;
        height: 309px;
        display: flex;

        .left {
          display: flex;
          flex-wrap: wrap;
          align-content: space-between;
          width: 1083px;
          height: 309px;

          img {
            width: 180px;
            height: 127px;
            margin: 0 18px;
            cursor: pointer;
          }
        }

        .right {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: flex-end;
          width: 350px;
          height: 309px;
          padding-right: 10px;

          .input {
            position: relative;
            width: 313px;
            height: 129px;

            .img {
              position: absolute;
              top: 0;
              left: 0;
              width: 313px;
              height: 127px;
            }

            .clear {
              position: absolute;
              top: 29px;
              right: 24px;
              width: 57px;
              height: 58px;
              cursor: pointer;
            }

            .number {
              position: absolute;
              top: 20px;
              left: 80px;
              width: 145px;
              height: 74px;
              font-size: 52px;
              line-height: 74px;
              text-align: center;
              font-family: Impact;
              color: #fff;
            }
          }

          .img {
            width: 313px;
            height: 127px;
            cursor: pointer;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 20px;
      display: flex;
      justify-content: flex-end;
      align-items: flex-end;
      width: 1620px;

      .img {
        height: 115px;
        cursor: pointer;
      }

      .footer-right {
        width: 690px;
        display: inline-flex;
        justify-content: space-between;

        .item {
          margin: 0;
          width: 210px;
          height: 76px;
          border-radius: 4px;
          border: 1px solid #53872A; 
          background: #B7EBA4;

          font-size: 24px;
          text-align: center;
          line-height: 74px;
          color: #53872A;
        }
      }
    }
  }
}
</style>
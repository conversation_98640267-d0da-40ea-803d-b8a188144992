<template>
  <div class="game77-page">
    <div class="page-bg"></div>
		<settingPage title="超市商品" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">请您仔细浏览所提供的超市图片，根据提出的任务来答题。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="title">
        <img class="title-bg" src="/static/game_assets/game61/title_bg.png" />
        <span class="title-text">图中有几种{{current.question.topic}}？</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game77/content_bg.png" />
        <img class="left-icon" src="/static/game_assets/game77/left.png" />
        <img class="right-icon" src="/static/game_assets/game77/right.png" />

        <div :class="['content-item', choose.includes(item.name) && 'choose-item', isJudge && current.question.answer.includes(item.name) && !choose.includes(item.name) && 'correct-item']" v-for="item in current.answerList" :key="item.name+ 'item'" @click="chooseItem(item.name)">
          <img class="img" :src="item.img" />
          <p class="text">{{item.name}}</p>
          <img class="icon" v-if="isJudge && current.question.answer.includes(item) && choose.includes(item)" src="/static/game_assets/common/correct.png">
          <img class="icon" v-if="isJudge && !current.question.answer.includes(item) && choose.includes(item)" src="/static/game_assets/common/error.png">
        </div>
      </div>

      <div class="footer">
        <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
        <div class="group-btn">
          <img class="img" v-if="choose.length && !isJudge" src="/static/game_assets/common/confirm.png" @click="confirm" />

          <template v-if="isJudge">
            <img class="img" src="/static/game_assets/common/continue.png" @click="goOn" />
            <!-- <img class="img right-btn" src="/static/game_assets/common/reset.png" @click="reset"> -->
          </template>
        </div>        
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'

export default {
  name: 'game77',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
			show: false,
      isJudge: false,
      current: {},
			questionList: [],
			choose: [],

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.status = 3
      this.questionList = api.shuffle(data.data)
      this.startProcess()
      // this.timing()
    },

    startProcess() {
			this.current = this.questionList[this.number]
      this.current.answerList = api.shuffle(this.current.answerList)
      this.number++
    },

    chooseItem(item) {
      if (this.isJudge) return
      if (this.choose.includes(item)) {
        this.choose = this.choose.filter(it => it !== item)
        return
      }
			this.choose.push(item)
      this.choose = Array.from(new Set(this.choose))
    },

    goOn() {
      if (this.number >= 5) {
        this.submit()
      } else {
        this.isJudge = false
        this.choose = []
        this.startProcess()
      }
    },

    reset() {
      this.isJudge = false
      this.choose = []
      this.number = 0
      this.startProcess()
    },

    confirm() {
      const answer = this.current.question.answer
      this.choose.forEach(item => {
        if (answer.includes(item)) {
          this.succesNum++
        } else {
          this.errorNum++
        }
      })
      this.isJudge = true
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isStop = true
			this.store = 10 * this.succesNum + 10
			this.infos[0].value = this.second
			this.infos[1].value = this.succesNum
			this.infos[2].value = this.errorNum
			this.status = 4
			this.params = {
				id: this.info.id,
				grade: '',
				time: this.second,
				totalPoints: this.store
			}
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.isJudge = false
			this.current = {}
      this.choose = []
			this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game77-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game57/bg.png");    
  } 

  .game-synopsis {
    width: 1576px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

		.title {
      position: absolute;
      top: 0;
      width: 932px;
      height: 125px;
      z-index: 1;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px;
        height: 125px;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 31px 0 58px 0;
        font-size: 36px;
        line-height: 36px;
        text-align: center;
        color: #1B1D2D;
        user-select: none;
      }
    }

    .content {
      position: relative;
      width: 1920px;
      height: 988px;
      margin-top: 90px;
      padding: 144px 250px 266px 230px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between; 
      align-content: space-between;

      .content-bg {
        position: absolute;
        top: 0;
        left: 135px;
        width: 1655px;
        height: 916px;
      }

      .left-icon {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 603px;
      }

      .right-icon {
        position: absolute;
        right: 20px;
        bottom: 0;
        width: 684px;
      }

      .content-item {
        position: relative;
        width: 280px;
        height: 280px;
        padding: 19px 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        border: 3px solid #D2D2D2;
        border-radius: 45px;

        .img {
          width: 220px;
          height: 182px;
        }

        .text {
          padding-top: 10px;
          font-size: 32px;
          line-height: 32px;
          text-align: center;
          color: #1B1D2D;
          user-select: none;
        }

        .icon {
          position: absolute;
          right: 18px;
          bottom: 21px;
          width: 57px;
        }
      }

      .choose-item {
        border: 10px solid #5F6FB2;
      }

      .correct-item {
        border: 10px solid #F1CC6E;
      }
		}

    .footer {
      position: absolute;
      bottom: 75px;
      display: flex;
      justify-content: space-between;
      width: 1470px;
      padding-right: 38px;

      .img {
        height: 115px;
        cursor: pointer;
      }

      .right-btn {
        margin-left: 43px;
      }
    }
  }
}
</style>
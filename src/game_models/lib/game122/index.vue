<template>
  <div class="game122-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
		<settingPage title="认识形状" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、小智系列，通过学习并对比来掌掌握各种形状的一种认知，包括有圆形、三角形、正方形三种形状</p>
        <p class="synopsis-content">2、本训练难度为A(简单)。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <img class="top-img" src="/static/game_assets/game122/icon.png" />

      <div class="content">
        <img class="right-icon" src="/static/game_assets/game122/left_icon.png">

        <div class="content-left">
          <img class="left-bg" src="/static/game_assets/game122/img.png" />

          <img class="left-item1 right" src="/static/game_assets/game122/item_1.png" />

          <template v-if="number > 0">
            <img :class="['left-item' + item, number === item && 'flash-item']" v-for="item in 3" :key="item + 'img'" :src="`/static/game_assets/game122/item_${item}.png`" />
          </template>
          
          <draggable
            v-else
            :class="['left-item' + item]"
            v-for="(item, index) in 3"
            :key="item + 'left'"
            v-model="dragItemLeft[item - 1]" 
            :group="group2"
            @add="dragAdd(index)"
          >
            <img class="img" :src="`/static/game_assets/game122/item_${item}_2.png`" />
            <img v-for="it in dragItemLeft[item - 1]" :key="it.name + 'item1'" class="img-item" :src="`/static/game_assets/game122/item_${it.name}_1.png`" />
          </draggable>
        </div>

        <div class="content-right">
          <img class="right-bg" src="/static/game_assets/game122/content_bg.png" />

          <p class="right-title">{{number === 1 ? '圆形' : number === 2 ? '三角形' : '正方形'}}</p>
          
          <template v-if="number > 0">
            <img :class="['right-item', number === item && 'flash-item']" v-for="item in 3" :key="item + 'item'" :src="`/static/game_assets/game122/item_${item}_1.png`" />
          </template>
          
          <div class="right-item-wapper" v-else v-for="item in 3" :key="item + 'item'">
            <img class="item-bg" :src="`/static/game_assets/game122/item_${item}_3.png`" />
            <draggable 
              class="right-item" 
              :group="group1" 
              v-model="dragItemRight[item-1]"
              @start="dragStart(item)"
            >
              <img v-for="it in dragItemRight[item-1]" :key="it.name + 'img2'" class="right-img" :src="`/static/game_assets/game122/item_${it.name}_1.png`" />
            </draggable>
          </div>
        </div>
      </div>

      <div class="top">
        <img v-if="!isPlay" class="top-icon" src="/static/game_assets/common/play.png" />
				<img v-else class="top-icon" src="/static/game_assets/common/pause.png" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>

      <div class="footer">
        <img v-if="number === 0" class="img1" src="/static/game_assets/game81/left_btn.png" @click="clickLeft">
        <img v-if="number > 0" class="img2" src="/static/game_assets/game81/right_btn.png" @click="clickRight">
      </div>
    </div>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'

export default {
  name: 'game122',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    draggable
  },

  data() {
    return {
      musicUrl: '',
      number: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,

      choose: 0,
      dragItemLeft: [[], [], []],
      dragItemRight: [],
      group1: {
        name: 'itemList',
        pull: true,
        put: false,
        sort: false
      },
      group2: {
        name: 'itemList',
        pull: false,
        put: true,
        sort: false
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    // TODO: 返回首页
		goHome() {
      this.pause()
			this.$router.go(-1)
		},

		play(url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnded() {
      this.isPlay = false
      setTimeout(() => {
        if (!this.number) return

        if (this.number < 3) {
          this.number++
          this.startProcess()
        } else {
          this.number = 0
          this.goNext()
        }
      }, 500)
    },

    start() {
      this.status = 3
      this.startProcess()
      // this.timing()
      this.play()
    },

    startProcess() {
      this.play(`/static/game_assets/audio/game122/audio_${this.number}.mp3`)
    },

    goNext() {
      this.play(`/static/game_assets/audio/game122/title.mp3`)
      for (let i = 0; i < 3; i++) {
        this.dragItemRight.push([{
          name: i + 1,
          startIndex: i,
        }])
      }
    },

    dragStart(item) {
      this.choose = this.dragItemRight[item - 1][0].name
    },

    dragAdd(index) {
      if (this.choose !== Number(index + 1)) {
        if (this.dragItemLeft[index].length > 1) {
          const item = this.dragItemLeft[index].filter(item => item.name !== index)[0]
          this.dragItemLeft[index] = this.dragItemLeft[index].filter(item => item.name === index)
          this.dragItemRight[item.startIndex].push(item)
        } else {
          const item = this.dragItemLeft[index].pop()
          this.dragItemRight[item.startIndex].push(item)
        }
        this.errorNum++
        this.play(`/static/game_assets/audio/error_audio.mp3`)
      } else {
        this.succesNum++
      }

      const list = this.dragItemLeft.filter(item => item.length)
      if (list.length >= 3) {
        setTimeout(() => {
          this.submit()
        }, 800)
      }
    },

    clickLeft() {
      this.number = 1
      this.startProcess()
    },

    clickRight() {
      this.pause()
      this.number = 0
      this.goNext()
    },

    submit() {
      this.isStop = true
			this.store = 100 - 20 * this.errorNum > 0 ? 100 - 20 * this.errorNum : 100
			this.infos[0].value = this.second
			this.infos[1].value = this.succesNum
			this.infos[2].value = this.errorNum
			this.status = 4
			this.params = {
				id: this.info.id,
				grade: '',
				time: this.second,
				totalPoints: this.store
			}
    },

    again() {
      this.number = 1
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
			this.choose = 0,
      this.dragItemLeft = [[], [], []],
      this.dragItemRight = [],
			this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game122-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game122/bg.png");
  } 

  .game-synopsis {
    width: 1576px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top-img {
      position: absolute;
      top: 64px;
      right: 129px;
      width: 221px;
    }

    .top {
			position: absolute;
			top: 40px;
			left: 105px;
			width: 175px;
			display: flex;
			justify-content: space-between;

			.top-icon {
				width: 80px;
				cursor: pointer;
			}
		}

    .content {
      position: relative;
      width: 1478px;
      height: 646px;
      margin-top: 141px;
      margin-right: 36px;
      padding: 0 117px 17px 0;
      display: flex;
      justify-content: space-between;

      .right-icon {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 180px;
        z-index: 2;
      }

      .content-left {
        position: relative;
        width: 990px;

        .left-bg {
          position: absolute;
          left: 0;
          bottom: 64px;
          width: 990px;
        }

        .left-item1 {
          position: absolute;
          width: 149px;
          height: 150px;
          bottom: 0;
          left: 125px;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
          }

          .img-item {
            position: relative;
            width: 138px;
          }
        }

        .left {
          bottom: 0;
          left: 125px;
        }

        .right {
          bottom: 0;
          left: 716px;
        }

        .left-item2 {
          position: absolute;
          top: 128px;
          right: 284px;
          width: 97px;
          height: 87px;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
          }

          .img-item {
            position: relative;
            width: 90px;
          }
        }

        .left-item3 {
          position: absolute;
          top: 236px;
          right: 311px;
          width: 112px;
          height: 112px;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
          }

          .img-item {
            position: relative;
            width: 102px;
          }
        }

        .flash-item {
          animation: flash 1s linear infinite;
        }
      }

      .content-right {
        position: relative;
        width: 329px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .right-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 329px;
        }

        .right-title {
          position: relative;
          margin: 0;
          padding: 32px 0 43px 0;
          font-size: 50px;
          line-height: 50px;
          font-weight: 500;
          text-align: center;
          color: #FCFCFC;
        }
        
        .right-item-wapper {
          position: relative;
          width: 134px;
          height: 162px;

          .item-bg {
            position: absolute;
            top: 27px;
            left: 0;
            width: 134px;
          }
        }

        .right-item {
          position: relative;
          margin-top: 27px;
          width: 134px;
          height: 135px;
          cursor: pointer;

          .right-img {
            width: 100%;
          }
        }

        .flash-item {
          animation: flash 1s linear infinite;
        }
      }
		}

    .footer {
      position: absolute;
      bottom: 67px;
      right: 84px;
      width: 338px;
      height: 154px;
      z-index: 3;

      .img1 {
        position: absolute;
        top: 0;
        left: 0;
        height: 154px;
        cursor: pointer;
      }

      .img2 {
        position: absolute;
        top: 0;
        right: 0;
        height: 154px;
        cursor: pointer;
      }
    }
  }
}

@keyframes flash{
  0% {
    transform: scale(1);
  }
  80% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
</style>
<template>
  <div class="game6-page">
    <div class="page-bg" :class="status > 1 ? 'page-bg2': 'page-bg1'"></div>
    <settingPage title="推理识序" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">请在呈现的所有图片中找出其对应的动作顺序，并按顺序点击图片，如点击错误可以重新开始挑战</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="title">
        <p class="name">沉船</p>
        <div class="icon">
          <div class="icon-img">
            <img src="/static/game_assets/common/clock.png" />
            <span>{{ time }}</span>
          </div>
        </div>
      </div>

      <div class="content">
        <img class="img" v-for="item in correctAnswer" :key="item" :src="`/static/game_assets/game6/item_${num}_${item}.png`" @click="select(item)" />
        <template v-for="item in 4">
          <img class="number" :key="item + 'number'" v-if="answer[item - 1]" :src="`/static/game_assets/game6/item_${num}_${answer[item - 1]}.png`" />
          <img class="number" :key="item + 'number'" v-else :src="`/static/game_assets/game6/img_${item}.png`">
        </template>
      </div>

      <div class="footer">
        <div class="left">
          <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
          <img class="img2" src="/static/game_assets/common/continue.png" @click="goOn">
        </div>
        <div class="right">
          <img v-if="answer.length >= 4" class="img3" src="/static/game_assets/common/submit.png" @click="submit">
          <img class="img2" src="/static/game_assets/common/reset.png" @click="reset">
        </div>
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
 
export default {
  name: 'game6',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      answer: [],
      num: 1,
      correctAnswer: [],
      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time() {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.level = Number(this.info.level) || 1
      const list = [1, 2, 3, 4]
      this.num = api.randomNum(1, 3)
      this.correctAnswer = api.shuffle(list)
      this.status = 3
      // this.timing()
    },

    select(item) {
      if (this.isStop) return
      const list = this.answer.filter(it => it === item)
      if (list.length) return
      this.answer.push(item) 
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    goOn() {
      if (!this.isStop) return
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isStop = true
      this.answer.forEach((item, index) => {
        if (item === index + 1) {
          this.succesNum++
        } else {
          this.errorNum++
        }
        this.store = this.succesNum * 25
      })
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    reset() {
      this.answer = []
      this.second = 0
      if (this.isStop) {
        this.isStop = false
        this.timing()
      }
    },

    again() {
      this.answer = []
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game6-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background-image: url("/static/game_assets/game1/images/bg1.png");
  }
  .page-bg2 {
    background-image: url("/static/game_assets/game1/images/bg2.png");
  }

  .game-synopsis {
    width: 707px;
    height: 444px;
    margin: 34px;
    padding: 33px 30px;
    background: #fff;
    border: 2px solid #7BBD41;
    border-radius: 36px;

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #5E381F;
    }

    .synopsis-content {
      padding-top: 18px;
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 400;
      color: #5E381F;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .title {
      position: relative;
      padding: 47px 0;

      .name {
        margin: 0;
        font-size: 42px;
        line-height: 59px;
        text-align: center;
        font-weight: 500;
      }

      .icon {
        position: absolute;
        top: 42px;
        right: 210px;
        display: flex;

        .icon-img {
          width: 230px;
          height: 76px;
          padding: 4px 11px;
          border: 1px solid #37B982;
          border-radius: 38px;
          font-size: 0;

          img {
            width: 58px;
            height: 58px;
            vertical-align: middle;
          }

          span {
            display: inline-block;
            padding-left: 20px;
            font-size: 32px;
            line-height: 58px;
            font-weight: 500;
            vertical-align: middle;
          }
        }

        .icon-star {
          margin-left: 50px;
        }
      }
    }

    .content {
      position: relative;
      width: 1509px;
      height: 766px;
      padding: 16px 0;
      margin: 0 auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;

      .img {
        width: 366px;
        height: 371px;
        background: #fff;
        border-radius: 37px;
        cursor: pointer;
      }

      .number {
        width: 366px;
        height: 348px;
        background: #fff;
        border-radius: 37px;
      }
    }

    .footer {
      position: relative;
      display: flex;
      justify-content: space-between;
      width: 1509px;
      padding-bottom: 47px;
      margin: 0 auto;

      .left {
        width: 566px;
        display: inline-flex;
        justify-content: space-between;
      }

      .right {
        width: 564px;
        display: inline-flex;
        justify-content: flex-end;

      }

      .img1 {
        width: 270px;
        height: 115px;
        cursor: pointer;
      }

      .img2 {
        width: 268px;
        height: 115px;
        cursor: pointer;
      }

      .img3 {
        width: 267px;
        height: 115px;
        cursor: pointer;
        margin-right: 30px;
      }
    }
  }
}
</style>
<template>
  <div class="game81-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
		<settingPage title="星角正圆" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">认识三角形、正方形、圆形、星形等不同形状，同时认识对应形状的实物。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <img class="content-bg" src="/static/game_assets/game81/content_bg.png" />

        <div class="content-title">
          <img class="title-bg" src="/static/game_assets/game81/text_bg.png" />
          <span class="title-text">{{title}}</span>
        </div>

        <div class="content-more">
          <div class="content-item" v-for="item in 4" :key="item + 'item'">
            <img v-if="current !== item" class="item-img" :src="`/static/game_assets/game81/item_${item}.png`" />
            <img v-if="current === item" class="item-img flash-item" :src="`/static/game_assets/game81/item_${item}_1.png`" />
          </div>
        </div>

        <img class="content-main" :src="`/static/game_assets/game81/item_${current}_${item}.png`" />
      </div>

      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
				<img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>

      <div class="footer">
        <img v-if="number > 0" class="img1" src="/static/game_assets/game81/left_btn.png" @click="clickLeft">
        <img v-if="number < 3" class="img2" src="/static/game_assets/game81/right_btn.png" @click="clickRight">
      </div>
    </div>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import api from '../../utils/common.js'

export default {
  name: 'game81',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
  },

  data() {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,
      title: '',
      current: 0,
      questionList: [],
      item: 2,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    // TODO: 返回首页
		goHome() {
      this.pause()
			this.$router.go(-1)
		},

		play() {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start() {
      this.status = 3
      const list = [1, 2, 3, 4] // 1-三角 2-星 3-方形 4-圆
      this.questionList = api.shuffle(list)
      this.startProcess()
      // this.timing()
      this.play()
    },

    startProcess() {
      this.current = this.questionList[this.number]
      this.setTime()

      if (this.current === 1) {
        this.title = '三角形'
      } else if (this.current === 2) {
        this.title = '星形'
      } else if (this.current === 3) {
        this.title = '正方形'
      } else if (this.current === 4) {
        this.title = '圆形'
      }
    },

    setTime() {
      if (this.item >= 4) {
        this.item = 4 
        return
      }
      setTimeout(() => {
        this.item++
        this.setTime()
      }, 500)
    },

    clickLeft() {
      this.number--
      this.item = 2
      this.startProcess()
    },

    clickRight() {
      this.number++
      this.item = 2
      this.startProcess()
    },

    submit() {
      this.isStop = true
			this.store = 5 * this.succesNum
			this.infos[0].value = this.second
			this.infos[1].value = this.succesNum
			this.infos[2].value = this.errorNum
			this.status = 4
			this.params = {
				id: this.info.id,
				grade: '',
				time: this.second,
				totalPoints: this.store
			}
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
			this.current = 0
      this.item = 2
      this.title = ''
			this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game81-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game126/bg.png");
  } 

  .game-synopsis {
    width: 1576px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
			position: absolute;
			top: 40px;
			left: 105px;
			width: 175px;
			display: flex;
			justify-content: space-between;

			.top-icon {
				width: 80px;
				cursor: pointer;
			}
		}

    .content {
      position: relative;
      width: 1920px;
      height: 914px;
      margin-top: 27px;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1920px;
        height: 914px;
      }

      .content-title {
        position: absolute;
        top: 50px;
        left: 220px;
        width: 364px;
        height: 165px;

        .title-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 364px;
        }

        .title-text {
          display: block;
          position: relative;
          padding: 50px 0 63px 18px;
          font-size: 52px;
          line-height: 52px;
          font-weight: 600;
          text-align: center;
          color: #fff;
          user-select: none;
        }
      }

      .content-more {
        position: absolute;
        top: 382px;
        left: 371px;
        width: 361px;
        height: 356px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;

        .content-item {
          width: 168px;
          height: 168px;
          border: 3px solid #AACFAB;
          border-radius: 31px;
          display: flex;
          align-items: center;
          justify-content: center;

          .item-img {
            width: 138px;
          }

          .flash-item {
            animation: flash 1s linear infinite;
          }
        }
      }

      .content-main {
        position: absolute;
        top: 126px;
        right: 483px;
        width: 594px;
      }
		}

    .footer {
      position: absolute;
      bottom: 67px;
      right: 310px;
      width: 338px;
      height: 154px;

      .img1 {
        position: absolute;
        top: 0;
        left: 0;
        height: 154px;
        cursor: pointer;
      }

      .img2 {
        position: absolute;
        top: 0;
        right: 0;
        height: 154px;
        cursor: pointer;
      }
    }
  }
}

@keyframes flash{
  0% {
    width: 138px;
  }
  80% {
    width: 168px;
  }
  100% {
    width: 138px;
  }
}
</style>
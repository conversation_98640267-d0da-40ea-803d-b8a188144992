<template>
  <div class="game38-page">
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <div class="page-bg"></div>
    <settingPage @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <div class="synopsis-left">
          <p class="synopsis-content">1、此项训练着重加强您的记忆编码能力。</p>
          <p class="synopsis-content">2、给您呈现成对的词，有的词之间有联系，比如美丽-丑陋，有的词之间没有联系，比如眼睛-兔子，要求您注意词之间的联系，如果没有联系，请您用联想的方法把两个词联系起来，比如“兔子的红眼睛”，以此加强记忆。</p>
          <p class="synopsis-content">3、我给您听4对词，您要注意看，要求您记住那两个词之间是联系在一起的一对。比如看到“钟表-皮鞋”，就代表钟表和皮鞋连在一起的对词，等把4对词看完以后，比如看到钟表，您就填皮鞋。</p>
        </div>

        <div class="synopsis-right">
          <div class="synopsis-item">
            <p class="title">呈现时间</p>
            <p class="time">5秒</p>
          </div>

          <div class="synopsis-item">
            <p class="title">间隔时间</p>
            <p class="time">2秒</p>
          </div>
        </div>
      </div>
    </settingPage>

    <div class="game-content" v-if="status === 3">
      <div class="top">{{index}}</div>

      <div class="content">
        <img class="item-bg" src="/static/game_assets/game38/item_bg.png" />
        <a-input v-if="answerStatus === 'answer' || answerStatus === 'submit'" class="input-text" v-model="answer" />
        <img v-else class="item-bg" src="/static/game_assets/game38/item_bg.png" />
      </div>
      
      <div class="btn-group">
        <div class="btn" @click="stop">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>

        <div class="btn" @click="startGame" v-if="answerStatus === 'playWait' || answerStatus === 'submit'">
          <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
          <span class="text">{{answerStatus === 'playWait' ? '开始' : '确定'}}</span>
        </div>
      </div>
    </div>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'
import data from './data/data.json'
// 每轮游戏4个回合

export default {
  name: 'game38',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data() {
    return {
      musicUrl: '',
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      questions: [],
      answer: '',
      answerStatus: 'playWait', // play -- 题目 answer -- 回答 playWait -- 播放前等待 submit -- 提交
      index: 1,
      num: 1, // 两个音频的位置
      show: false,
      status: 1,
      isStop: false,
      isPlay: false,
      isContinue: false,
      timer: null,
      answerArr: [],
      indexArr: [],
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play(url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$nextTick(() => {
        this.$refs.music.play()
        this.isPlay = true
      })
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnd() {
      this.isPlay = false
      if (this.answerStatus !== 'play') return

      this.num++
      if (this.num === 2) {
        this.play(this.questions[this.index - 1].url2)
        this.num = 0
      } else if (this.num === 1) {
        this.timer = setTimeout(() => {
          this.timer = null
          this.next()
        }, 2000)
      }
    },

    next() {
      this.index++
      if (this.index <= this.questions.length) {
        this.play(this.questions[this.index - 1].url1)
      } else {
        this.index = 1
        this.answerStatus = 'answer'
        this.play(this.questions[this.index - 1].url1)
      }
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
    },

    startGame() {
      if (this.answerStatus === 'playWait') {
        this.answerStatus = 'play'
        this.startProcess()
      } else if (this.answerStatus === 'submit') {
        if (this.answer === this.answerArr[this.index - 1]) {
          this.succesNum++
        } else {
          this.errorNum++
        }
        
        this.number++
        this.index++
        this.answer = ''

        if (this.number < 4) {
          this.answerStatus = 'answer'
          this.play(this.questions[this.index - 1].url1)
        } else {
          this.store = this.succesNum * 25
          this.isStop = true
          this.infos[0].value = this.level
          this.infos[1].value = this.second
          this.infos[2].value = this.succesNum
          this.infos[3].value = this.errorNum
          this.status = 4
          this.params = {
            id: this.info.id,
            grade: this.level,
            time: this.second,
            totalPoints: this.store
          }
        }
      }
    },

    // 开始流程
    async startProcess() {
      this.questions = []
      this.answerArr = []
      const arr = api.getRandomArray(data.data, 8)
      for (let i = 0; i < 4; i++) {
        this.questions.push({
          url1: arr[2 * i].audio,
          url2: arr[2 * i + 1].audio
        })
        this.answerArr.push(arr[2 * i + 1].text)
      }

      this.index = 1
      this.play(this.questions[this.index - 1].url1)
    },

    stop() {
      if (this.isPlay) {
        this.pause()
        this.isContinue = true
      }
      if (this.timer) clearTimeout(this.timer)
      this.isStop = true
      this.show = true
    },

    reset() {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.answerStatus = 'playWait'
      this.index = 0
      this.indexArr = []
      this.answer = ''
    },

    // 继续游戏, 继续计时
    cancel() {
      if (this.isContinue) {
        this.play()
        this.isContinue = false
      } else if (this.answerStatus === 'play') {
        this.next()
      }
      if (this.answerStatus === 'answer' || this.answerStatus === 'submit') {
        this.isStop = false
        this.timing()
      }
    },

    again() {
      this.isStop = false
      this.status = 3
      this.reset()
      this.timing()
    }
  },

  watch: {
    answer(newValue) {
      if (newValue) {
        this.answerStatus = 'submit'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.game38-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game38/bg.png");
  }

  .game-synopsis {
    display: flex;

    .synopsis-left {
      width: 820px;

      .synopsis-content {
        padding-top: 20px;
        margin: 0;
        font-size: 30px;
        line-height: 42px;
        font-weight: 400;
        color: #A83A01;
        user-select: none;
      }
    }

    .synopsis-right {
      padding-left: 50px;
      .synopsis-item {
        .title {
          margin: 0;
          padding-bottom: 20px;
          font-size: 30px;
          line-height: 48px;
          font-weight: 500;
          color: #A83A01;
          user-select: none;
        }

        .time {
          margin-bottom: 40px;
          padding: 14px 48px;
          border-radius: 10px;
          background: #F8DD84;

          font-size: 48px;
          line-height: 60px;
          color: #A83A01;
          user-select: none;
        }
      }
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: 58px 0 82px 0;

    .top {
      width: 110px;
      height: 110px;
      background: #FFFCB0;
      border-radius: 50%;

      font-size: 74px;
      line-height: 110px;
      text-align: center;
      font-weight: 500;
      color: #AA4202;
      font-family: PingFangSC-Medium, PingFang SC;
      user-select: none;
    }

    .content {
      width: 865px;
      height: 400px;
      padding-bottom: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .item-bg {
        width: 340px;
        height: 340px;
      }

      .input-text {
        width: 340px;
        height: 140px;
        background: #EA9A22;
        border-radius: 16px;
        border: none;

        font-size: 77px;
        font-weight: 500;
        text-align: center;
        color: #fff;
        user-select: none;
      }
    }

    .btn-group {
      position: relative;
      width: 1072px;
      height: 166px;
      display: flex;
      justify-content: space-between;
      padding-bottom: 82px;
      margin: 0 auto;

      .btn {
        position: relative;
        width: 295px;
        height: 84px;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px;
          height: 84px;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px 0 21px 0;
          font-size: 38px;
          line-height: 53px;
          text-align: center;
          color: #A83A01;
          user-select: none;
        }
      }
    }
  }
}
</style>
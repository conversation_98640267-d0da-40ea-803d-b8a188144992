<template>
  <div class="game48-page">
    <audio ref="music" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <div class="page-bg"></div>
    <settingPage @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、当记忆收到损伤时，会记不住经常出现在您身边、帮助您的人的姓名或身份。发生这种情况时，请不要泄气，我们将帮助您记住他们的名字。</p>
        <p class="synopsis-content">2、本组训练为相貌一人名在认。训练中，除了要记住一个人的相貌外，还要记住她/他的名字。训练分为“学习”和“判断”两部分。播放完学习阶段的照片后，做配对选择判断。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <template v-if="answerStatus === 'hint'">
        <div class="content1">
          <img class="content-bg" src="/static/game_assets/game8/content_bg.png">
          <p class="content-title">注意喽～</p>
          <p class="content-text">请记住即将划出的照片和名字</p>
        </div>
      </template>

      <template v-if="answerStatus === 'play'">
        <div :class="['content2', isShowClass && 'opacity-content']">
          <div class="content-left">
            <img class="content-img" :src="current.img" />
          </div>
          <div class="content-right">
            <p class="content-text">姓名：{{current.name}}</p>
          </div>
        </div>
      </template>

      <template v-if="answerStatus === 'answer' || answerStatus === 'judge'">
        <div class="content3" @mousedown="handleMousedown" @mousemove="handleMousemove" @mouseup="handleMouseup">
          <canvas id="mycanvas" width="1300" height="750"></canvas>
          <div class="content-top">
            <div class="content-item" v-for="(item, index) in questions" :key="item.name + 'img'">
              <div class="item-top">
                <img class="item-img" :src="item.img" />
              </div>
              <div :class="['item-point', 'item-point' + index]" :ref="'img' + index"></div>
            </div>
          </div>
          
          <div class="content-bottom">
            <div class="content-item" v-for="(item, index) in answerList" :key="item.name + 'text'">
              <div class="item-point" :ref="'text' + index"></div>
              <div class="item-text">{{item.name}}</div>
            </div>
          </div>
        </div>
      </template>
      
      <div class="btn-group">
        <div class="btn" @click="stop" v-if="answerStatus !== 'play' && answerStatus !== 'hint'">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>

        <div class="btn" @click="startGame" v-if="answerStatus === 'answerWait' || (answerStatus === 'answer' && answer.length === questions.length * 2)">
          <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
          <span class="text">{{ (answerStatus === 'answerWait' && '开始判断') || (answerStatus === 'answer' && '确定') }}</span>
        </div>
      </div>

      <div class="judge" v-if="answerStatus === 'judge'">
        <img class="img" :src="`/static/game_assets/game8/${isCorrect ? 'success' : 'error'}.png`">
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from '../game46/data/data.json'
// 1级：3组图片
// 2级：4组图片
// 3级：5组图片

export default {
  name: 'game48',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      show: false,
      status: 1,
      isStop: false,
      isShowClass: false,
      questions: [],
      answerList: [],
      current: {},
      answerStatus: 'hint', // play -- 题目 answer -- 回答 answerWait -- 回答前等待 hint -- 提示 judge -- 判定
      isCorrect: false,
      answer: [],
      path: [],
      x1: 0,
      y1: 0,
      x2: 0,
      y2: 0,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
      this.questions = api.getRandomArray(data.data, this.level + 2)
      const list = JSON.parse(JSON.stringify(this.questions))
      this.answerList = api.shuffle(list)
      setTimeout(() => {
        this.answerStatus = 'play'
        this.startProcess()
      }, 800)
    },

    startGame() {
      if (this.answerStatus === 'answerWait') {
        this.answerStatus = 'answer'
        this.current = this.questions[this.number]
        this.current.answerList[0] = api.shuffle(this.current.answerList[0])
        this.number++
      } else if (this.answerStatus === 'answer') {
        this.answerStatus = 'judge'
        this.isCorrect = true
        for(let i = 0; i < this.answer.length; i = i + 2) {
          const item1 = this.questions[(this.answer[i] - 1) / 2].name
          const item2 = this.answer[i + 1]
          if (item1 === item2) {
            this.succesNum++
          } else {
            this.errorNum++
            this.isCorrect = false
          }
        }

        setTimeout(() => {
          this.answerStatus = 'answer'
          this.submit()
        }, 800)        
      }
    },

    // 开始流程
    async startProcess() {
      this.current = this.questions[0]
      await this.playAnimation(500)
      this.isShowClass = true
      await this.playAnimation(2000)
      this.isShowClass = false

      this.current = this.questions[1]
      await this.playAnimation(500)
      this.isShowClass = true
      await this.playAnimation(2000)
      this.isShowClass = false

      this.current = this.questions[2]
        await this.playAnimation(500)
        this.isShowClass = true
        await this.playAnimation(2000)
        this.isShowClass = false

      if (this.level >= 2) {
        this.current = this.questions[3]
        await this.playAnimation(500)
        this.isShowClass = true
        await this.playAnimation(2000)
        this.isShowClass = false
      }

      if (this.level >= 3) {
        this.current = this.questions[4]
        await this.playAnimation(500)
        this.isShowClass = true
        await this.playAnimation(2000)
        this.isShowClass = false
      }
      this.answerStatus = 'answerWait'
    },

    playAnimation(time) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve(true)
        }, time)
      })
    },

    drawLine(x1, y1, x2, y2) {
      const canvas = document.getElementById('mycanvas')
      const ctx = canvas.getContext('2d')
      ctx.save()
      ctx.beginPath() //不写每次都会重绘上次的线
      ctx.lineCap = "round"
      ctx.lineJoin = "round"

      ctx.moveTo(x1, y1)
      ctx.lineTo(x2, y2)
      ctx.closePath()
      ctx.strokeStyle = "#FF1616"
      ctx.lineWidth = 4
      ctx.stroke()
      ctx.restore()
    },

    isRange(x, y, type) {
      let finalX, finalY, index
      const ref1 = this.$refs['img0'][0]
      const item1x1 = parseInt(ref1.getBoundingClientRect().left)
      const item1y1 = parseInt(ref1.getBoundingClientRect().top)
      const item1x2 = parseInt(ref1.getBoundingClientRect().left) + parseInt(ref1.offsetHeight)
      const item1y2 = parseInt(ref1.getBoundingClientRect().top) + parseInt(ref1.offsetWidth)
      if (item1x1 < x && x < item1x2 && item1y1 < y && y < item1y2) {
        finalX = parseInt(ref1.getBoundingClientRect().left) + parseInt(ref1.offsetHeight) / 2
        finalY = parseInt(ref1.getBoundingClientRect().top) + parseInt(ref1.offsetWidth) / 2
        index = 1
      }
      const ref2 = this.$refs['text0'][0]
      const item2x1 = parseInt(ref2.getBoundingClientRect().left)
      const item2y1 = parseInt(ref2.getBoundingClientRect().top)
      const item2x2 = parseInt(ref2.getBoundingClientRect().left) + parseInt(ref2.offsetHeight)
      const item2y2 = parseInt(ref2.getBoundingClientRect().top) + parseInt(ref2.offsetWidth)
      if (item2x1 < x && x < item2x2 && item2y1 < y && y < item2y2) {
        finalX = parseInt(ref2.getBoundingClientRect().left) + parseInt(ref2.offsetHeight) / 2
        finalY = parseInt(ref2.getBoundingClientRect().top) + parseInt(ref2.offsetWidth) / 2
        index = 2
      }

      const ref3 = this.$refs['img1'][0]
      const item3x1 = parseInt(ref3.getBoundingClientRect().left)
      const item3y1 = parseInt(ref3.getBoundingClientRect().top)
      const item3x2 = parseInt(ref3.getBoundingClientRect().left) + parseInt(ref3.offsetHeight)
      const item3y2 = parseInt(ref3.getBoundingClientRect().top) + parseInt(ref3.offsetWidth)
      if (item3x1 < x && x < item3x2 && item3y1 < y && y < item3y2) {
        finalX = parseInt(ref3.getBoundingClientRect().left) + parseInt(ref3.offsetHeight) / 2
        finalY = parseInt(ref3.getBoundingClientRect().top) + parseInt(ref3.offsetWidth) / 2
        index = 3
      }
      const ref4 = this.$refs['text1'][0]
      const item4x1 = parseInt(ref4.getBoundingClientRect().left)
      const item4y1 = parseInt(ref4.getBoundingClientRect().top)
      const item4x2 = parseInt(ref4.getBoundingClientRect().left) + parseInt(ref4.offsetHeight)
      const item4y2 = parseInt(ref4.getBoundingClientRect().top) + parseInt(ref4.offsetWidth)
      if (item4x1 < x && x < item4x2 && item4y1 < y && y < item4y2) {
        finalX = parseInt(ref4.getBoundingClientRect().left) + parseInt(ref4.offsetHeight) / 2
        finalY = parseInt(ref4.getBoundingClientRect().top) + parseInt(ref4.offsetWidth) / 2
        index = 4
      }

      const ref5 = this.$refs['img2'][0]
      const item5x1 = parseInt(ref5.getBoundingClientRect().left)
      const item5y1 = parseInt(ref5.getBoundingClientRect().top)
      const item5x2 = parseInt(ref5.getBoundingClientRect().left) + parseInt(ref5.offsetHeight)
      const item5y2 = parseInt(ref5.getBoundingClientRect().top) + parseInt(ref5.offsetWidth)
      if (item5x1 < x && x < item5x2 && item5y1 < y && y < item5y2) {
        finalX = parseInt(ref5.getBoundingClientRect().left) + parseInt(ref5.offsetHeight) / 2
        finalY = parseInt(ref5.getBoundingClientRect().top) + parseInt(ref5.offsetWidth) / 2
        index = 5
      }
      const ref6 = this.$refs['text2'][0]
      const item6x1 = parseInt(ref6.getBoundingClientRect().left)
      const item6y1 = parseInt(ref6.getBoundingClientRect().top)
      const item6x2 = parseInt(ref6.getBoundingClientRect().left) + parseInt(ref6.offsetHeight)
      const item6y2 = parseInt(ref6.getBoundingClientRect().top) + parseInt(ref6.offsetWidth)
      if (item6x1 < x && x < item6x2 && item6y1 < y && y < item6y2) {
        finalX = parseInt(ref6.getBoundingClientRect().left) + parseInt(ref6.offsetHeight) / 2
        finalY = parseInt(ref6.getBoundingClientRect().top) + parseInt(ref6.offsetWidth) / 2
        index = 6
      }

      if (this.level >= 2) {
        const ref7 = this.$refs['img3'][0]
        const item7x1 = parseInt(ref7.getBoundingClientRect().left)
        const item7y1 = parseInt(ref7.getBoundingClientRect().top)
        const item7x2 = parseInt(ref7.getBoundingClientRect().left) + parseInt(ref7.offsetHeight)
        const item7y2 = parseInt(ref7.getBoundingClientRect().top) + parseInt(ref7.offsetWidth)
        if (item7x1 < x && x < item7x2 && item7y1 < y && y < item7y2) {
          finalX = parseInt(ref7.getBoundingClientRect().left) + parseInt(ref7.offsetHeight) / 2
          finalY = parseInt(ref7.getBoundingClientRect().top) + parseInt(ref7.offsetWidth) / 2
          index = 7
        }
        const ref8 = this.$refs['text3'][0]
        const item8x1 = parseInt(ref8.getBoundingClientRect().left)
        const item8y1 = parseInt(ref8.getBoundingClientRect().top)
        const item8x2 = parseInt(ref8.getBoundingClientRect().left) + parseInt(ref8.offsetHeight)
        const item8y2 = parseInt(ref8.getBoundingClientRect().top) + parseInt(ref8.offsetWidth)
        if (item8x1 < x && x < item8x2 && item8y1 < y && y < item8y2) {
          finalX = parseInt(ref8.getBoundingClientRect().left) + parseInt(ref8.offsetHeight) / 2
          finalY = parseInt(ref8.getBoundingClientRect().top) + parseInt(ref8.offsetWidth) / 2
          index = 8
        }
      }

      if (this.level >= 3) {
        const ref9 = this.$refs['img4'][0]
        const item9x1 = parseInt(ref9.getBoundingClientRect().left)
        const item9y1 = parseInt(ref9.getBoundingClientRect().top)
        const item9x2 = parseInt(ref9.getBoundingClientRect().left) + parseInt(ref9.offsetHeight)
        const item9y2 = parseInt(ref9.getBoundingClientRect().top) + parseInt(ref9.offsetWidth)
        if (item9x1 < x && x < item9x2 && item9y1 < y && y < item9y2) {
          finalX = parseInt(ref9.getBoundingClientRect().left) + parseInt(ref9.offsetHeight) / 2
          finalY = parseInt(ref9.getBoundingClientRect().top) + parseInt(ref9.offsetWidth) / 2
          index = 9
        }
        const ref10 = this.$refs['text4'][0]
        const item10x1 = parseInt(ref10.getBoundingClientRect().left)
        const item10y1 = parseInt(ref10.getBoundingClientRect().top)
        const item10x2 = parseInt(ref10.getBoundingClientRect().left) + parseInt(ref10.offsetHeight)
        const item10y2 = parseInt(ref10.getBoundingClientRect().top) + parseInt(ref10.offsetWidth)
        if (item10x1 < x && x < item10x2 && item10y1 < y && y < item10y2) {
          finalX = parseInt(ref10.getBoundingClientRect().left) + parseInt(ref10.offsetHeight) / 2
          finalY = parseInt(ref10.getBoundingClientRect().top) + parseInt(ref10.offsetWidth) / 2
          index = 10
        }
      }

      if (!finalX) {
        this.x1 = 0
        this.y1 = 0
        this.x2 = 0
        this.y2 = 0
        if (type === 2) this.answer.pop()
        return
      }

      if (type === 1) {
        if (index % 2) { // 上方
          if (this.answer.includes(index)) return
          this.x1 = finalX
          this.y1 = finalY
          this.answer.push(index)
        } else { // 下方
          const item = this.answerList[parseInt((index - 1) / 2)].name
          if (this.answer.includes(item)) return
          this.x1 = finalX
          this.y1 = finalY
          this.answer.push(item)
        }
      } else {
        if (!(this.answer.length % 2)) return
        const popItem = this.answer.pop()
        if ((index % 2) && (typeof popItem === 'string')) { // 上方
          if (this.answer.includes(index)) return
          this.x2 = finalX
          this.y2 = finalY
          this.answer.push(index)
          this.answer.push(popItem)
        } else if (!(index % 2) && (typeof popItem === 'number')) { // 下方
          const item = this.answerList[parseInt((index - 1) / 2)].name
          if (this.answer.includes(item)) return
          this.x2 = finalX
          this.y2 = finalY
          this.answer.push(popItem)
          this.answer.push(item)
        }
      }
    },   

    handleMousedown(e) {
      if (this.answerStatus !== 'answer') return
      this.isRange(Number(e.clientX), Number(e.clientY), 1)
    },

    handleMousemove(e) {
      if (!this.x1) return

      const x2 = e.clientX
      const y2 = e.clientY
      const canvas = document.getElementById('mycanvas')
      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, 1320, 700)
      this.drawLine(this.drawLine(this.x1, this.y1, x2, y2))
      this.path.forEach(item => {
        this.drawLine(this.drawLine(item.x1, item.y1, item.x2, item.y2))
      })
    },

    handleMouseup(e) {
      if (this.answerStatus !== 'answer') return
      this.isRange(Number(e.clientX), Number(e.clientY), 2)
      const canvas = document.getElementById('mycanvas')
      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, 1320, 700)
      if (this.x2) {
        this.index++
        this.path.push({
          x1: this.x1, 
          y1: this.y1, 
          x2: this.x2, 
          y2: this.y2
        })
      }

      this.path.forEach(item => {
        this.drawLine(this.drawLine(item.x1, item.y1, item.x2, item.y2))
      })
      this.x1 = 0
      this.y1 = 0
      this.x2 = 0
      this.y2 = 0
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    submit() {
      this.isStop = true
      if (this.level === 1) {
        this.store = this.succesNum > 1 ? 40 + 30 * (this.succesNum - 1) : this.succesNum ? 40 : 0
      } else if (this.level === 2) {
        this.store = this.succesNum * 25
      } else if (this.level === 3) {
        this.store = this.succesNum * 20
      }
      this.infos[0].value = this.level
			this.infos[1].value = this.second
			this.infos[2].value = this.succesNum
			this.infos[3].value = this.errorNum
			this.status = 4
			this.params = {
				id: this.info.id,
				grade: this.level,
				time: this.second,
				totalPoints: this.store
			}
    },

    // 继续游戏, 继续计时
    cancel() {
      if (this.answerStatus !== 'hint') return
      this.isStop = false
      this.timing()
    },

    again() {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.isStop = false
      this.status = 3
      this.answerStatus = 'hint'
      this.answer = []
      this.path = []
      this.current = {}
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game48-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game46/bg.png");
  }

  .game-synopsis {

    .synopsis-content {
      padding-top: 20px;
      margin: 0;
      font-size: 30px;
      line-height: 42px;
      font-weight: 400;
      color: #A83A01;
    }
  }

  .game-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content1 {
      position: relative;
      width: 1253px;
      height: 708px;
      margin-bottom: 80px;
      padding-top: 220px;
      padding-right: 13px;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1253px;
        height: 708px;
      }

      .content-title {
        position: relative;
        margin: 0;
        font-size: 60px;
        line-height: 84px;
        text-align: center;
        font-weight: bold;
        color: #A83A01;
      }

      .content-text {
        position: relative;
        margin: 0;
        padding-top: 40px;
        font-size: 48px;
        line-height: 67px;
        text-align: center;
        color: #A83A01;
      }
    }

    .content2 {
      width: 1180px;
      height: 610px;
      margin-bottom: 150px;
      background: #4EA787;
      padding: 12px;
      display: flex;
      justify-content: space-between;
      opacity: 0;

      .content-left {
        width: 438px;
        height: 585px;
        background: #fff;

        .content-img {
          width: 100%;
          height: 100%;
        }
      }

      .content-right {
        width: 705px;
        height: 585px;
        padding: 162px 0;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        background: #fff;

        .content-text {
          margin: 0;
          padding-top: 100px;
          font-size: 48px;
          line-height: 67px;
          text-align: center;
          font-weight: 500;
          color: #464F32;
        }
      }
    }

    .opacity-content {
      opacity: 1;
      transition: opacity 0.5s;
    }

    .content3 {
      position: relative;
      width: 100%;
      height: 100%;

      .content-top {
        position: absolute;
        top: 150px;
        left: 300px;
        width: 1320px;
        height: 417px;
        display: flex;
        justify-content: center;

        .content-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 20px;

          .item-top {
            width: 224px;
            height: 294px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #4EA787;

            .item-img {
              width: 212px;
              height: 282px;
              background: #fff;
            }
          }

          .item-point {
            width: 53px;
            height: 53px;
            margin-top: 30px;
            background: #FFFFFF;
            border: 2px solid #4EA787;
            border-radius: 50%;
            user-select: none;
            -webkit-user-drag: none;
            cursor: pointer;
          }
        }
      }

      .content-bottom {
        position: absolute;
        top: 627px;
        left: 300px;
        width: 1320px;
        height: 222px;
        display: flex;
        justify-content: center;

        .content-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 20px;

          .item-point {
            width: 53px;
            height: 53px;
            margin-bottom: 30px;
            background: #FFFFFF;
            border: 2px solid #4EA787;
            border-radius: 50%;
            user-select: none;
            -webkit-user-drag: none;
            cursor: pointer;
          }

          .item-text {
            width: 224px;
            height: 96px;
            background: #fff;
            border-radius: 58px;
            font-size: 34px;
            line-height: 96px;
            text-align: center;
            font-weight: 500;
            color: #163625;
          }
        }
      }
    }

    .btn-group {
      position: absolute;
      bottom: 82px;
      width: 1554px;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px;
        height: 84px;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px;
          height: 84px;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px 0 21px 0;
          font-size: 38px;
          line-height: 53px;
          text-align: center;
          color: #A83A01;
        }
      }
    }

    .judge {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding-top: 10px;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      justify-content: center;
      align-items: center;

      .img {
        width: 460px;
        height: 460px;
      }
    }
  }
}
</style>
<template>
  <div class="game19-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPage title="颜色转换" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、屏幕上显示汉字‘灰’、’粉’，颜色不同并随机排列。(逐字出现)练习者按照要求说出当前字。</p>
        <p class="synopsis-content">2、两种朗读方式:①按字义读，即字是’灰’读灰;字为‘粉’读粉。②按颜色读，即灰色字(无论是’粉‘还是’灰’)读灰;粉色字就读粉。</p>
        <p class="synopsis-content">3、计算机不定时发出‘敲钟’的声音，提示立即转换为另一朗读规则。</p>
        <p class="synopsis-content">4、要求: 尽可能准确迅速的读出当前字。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <img class="content-bg" src="/static/game_assets/game21/content_bg.png" />
        <img class="left-icon" src="/static/game_assets/game21/left_icon.png" />
        <img class="right-icon" src="/static/game_assets/game21/right_icon.png" />

        <div class="content-title">
          <img class="title-bg" src="/static/game_assets/game21/title_bg.png" />
          <p class="title-text">现在请根据字体的灰粉命名，并根据命名点击下方相应灰粉的按钮。</p>
        </div>

        <div class="content-main">
          <div class="content-item" v-for="(item, index) in questions" :key="index">
            <img v-if="chooseIndex === index" class="item-bg" src="/static/game_assets/game21/btn_choose_bg.png" />
            <img v-else class="item-bg" src="/static/game_assets/game21/btn_bg.png" />
            <span :class="['item-text', item.isPink ? 'text-pink' : '']">{{item.text}}</span>
          </div>
        </div>
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        <div class="btn-group">
          <div class="btn" @click="clickPink" @mousedown="isPinkClick = true" @mouseup="isPinkClick = false">
            <img v-if="isPinkClick" class="img" src="/static/game_assets/game21/pink_choose.png">
            <img v-else class="img" src="/static/game_assets/game21/pink.png">
          </div>

          <div class="btn" @click="clickGrey" @mousedown="isGreyClick = true" @mouseup="isGreyClick = false">
            <img v-if="isGreyClick" class="img" src="/static/game_assets/game21/grey_choose.png">
            <img v-else class="img" src="/static/game_assets/game21/grey.png">
          </div>
        </div>
      </div>
    </div>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'

export default {
  name: 'game19',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data() {
    return {
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,
      type: 1, // 1 -- 按字义读  0 -- 按颜色读
      chooseIndex: null,
      oldChooseIndex: null,
      questions: [],
      musicUrl: '/static/game_assets/audio/bell.mp3',
      isPinkClick: false,
      isGreyClick: false,
      timer: null,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    playAudio() {
      if (this.isStop) return
      
      this.$refs.music.play()
      this.isPlay = true
    },

    pauseAudio() {
      this.$refs.music.pause()
    },

    handleEnded() {
      this.isPlay = false

      if (this.type) {
        this.type = 0
      } else {
        this.type = 1
      }
    },

    start() {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess() {
      for (let i = 0; i < 40; i++) {
        const isPink = api.randomNum(0, 1) // 1 -- 粉色 0 -- 灰色
        const text = api.randomNum(0, 1) // 1 -- 粉 0 -- 灰
        this.questions.push({
          text: text ? '粉' : '灰',
          isPink
        })
      }

      this.setIndex()
      this.setTime()
    },

    setIndex() {
      if (this.chooseIndex !== null) this.oldChooseIndex = this.chooseIndex
      this.chooseIndex = api.randomNum(0, 39)
      if (this.chooseIndex === this.oldChooseIndex) this.chooseIndex = this.chooseIndex === 39 ? (this.chooseIndex - 1) : (this.chooseIndex + 1)
    },

    setTime() {
      if (this.isStop) return

      const time = api.randomNum(5, 8)
      this.timer = setTimeout(() => {
        this.playAudio()
        this.setTime()
      }, time * 1000)
    },

    clickPink() {
      const text = this.questions[this.chooseIndex].text
      const isPink = this.questions[this.chooseIndex].isPink
      if ((this.type && text === '粉') || (!this.type && isPink)) {
        this.succesNum++
      } else if ((this.type && text !== '粉') || (!this.type && !isPink)) {
        this.errorNum++
      }

      if ((this.succesNum + this.errorNum) >= 20) {
        this.submit()
      } else {
        this.setIndex()
      }
    },

    clickGrey() {
      const text = this.questions[this.chooseIndex].text
      const isPink = this.questions[this.chooseIndex].isPink
      if ((this.type && text === '灰') || (!this.type && !isPink)) {
        this.succesNum++
      } else if ((this.type && text !== '灰') || (!this.type && isPink)) {
        this.errorNum++
      }

      if ((this.succesNum + this.errorNum) >= 20) {
        this.submit()
      } else {
        this.setIndex()
      }
    },

    stop() {
      this.isStop = true
      this.show = true
      this.isPlay && this.pauseAudio()
      clearTimeout(this.timer)
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()

      if (this.isPlay) {
        this.$refs.music.play()
      } else {
        this.setTime()
      }
    },

    submit() {
      clearTimeout(this.timer)
      this.isStop = true
      this.store = 5 * this.succesNum
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again() {
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.isPlay = false
      this.questions = []
      this.type = 1
      this.chooseIndex = null
      this.oldChooseIndex = null
      this.timer = null
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game19-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/bg_1.png");
  }

  .game-synopsis {
    width: 1576px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1920px;
      height: 826px;
      padding-top: 49px;
      padding-left: 250px;
      padding-right: 270px;

      .content-bg {
        position: absolute;
        top: 0;
        left: 181px;
        width: 1524px;
        height: 807px;
      }

      .left-icon {
        position: absolute;
        top: 54px;
        left: 0;
        height: 680px;
        z-index: 1;
      }

      .right-icon {
        position: absolute;
        top: 108px;
        right: 0;
        width: 298px;
        height: 718px;
        z-index: 1;
      }

      .content-title {
        position: relative;
        padding-left: 23px;
        padding-bottom: 21px;
        width: 1354px;
        height: 141px;

        .title-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 1354px;
        }

        .title-text {
          position: relative;
          margin: 0;
          padding-left: 120px;
          font-size: 36px;
          line-height: 120px;
          color: #6F7685;
          user-select: none;
        }
      }

      .content-main {
        display: flex;
        flex-wrap: wrap;

        .content-item {
          position: relative;
          width: 169px;
          height: 169px;
          margin: -16px;

          .item-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 169px;
            height: 169px;
          }

          .item-text {
            position: relative;
            display: block;
            font-size: 57px;
            font-weight: 500;
            text-align: center;
            line-height: 169px;
            color: #6F7685;
            user-select: none;
          }

          .text-pink {
            color: #FF3B88;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 60px;
      display: flex;
      justify-content: space-between;
      width: 1469px;
      padding-right: 38px;

      .img1 {
        width: 270px;
        height: 115px;
        margin: 9px 0;
        cursor: pointer;
      }

      .btn-group {
        width: 588px;
        height: 133px;
        display: flex;
        justify-content: space-between;

        .img {
          width: 274px;
          height: 133px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
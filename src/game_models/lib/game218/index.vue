<template>
  <div class="game218-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source :src="audioUrl" type="audio/mpeg" />
    </audio>
    <settingPage title="风险管理" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">根据提示，进一步认识阿尔兹海默症，做好风险管理</p>
      </div>
    </settingPage>

    <div class="game-content" v-if="status === 3">
      <div class="btn-right">
        <div class="icon-img">
          <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
          <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />
          <span>语音读题</span>
        </div>
      </div>

      <div class="content">
        <div v-show="showQuestion">
          <img class="img-left" src="/static/game_assets/game218/icon_1.png">

          <div class="content-question">
            <p>{{ currect.question }}</p>
            <img class="btn" src="/static/game_assets/game218/btn_2.png"
                 @click="handleClick(1)" />
          </div>
        </div>

        <div v-show="!showQuestion">
          <img class="img-right" src="/static/game_assets/game218/icon_2.png">

          <div class="content-answer">
            <p v-for="item in currect.answerList">{{ item }}</p>
          </div>

          <img v-if="number < questionList.length" class="btn" src="/static/game_assets/game218/btn_1.png"
               @click="handleClick(2)" />
          <div v-else class="btn-group">
            <img class="btn" src="/static/game_assets/common/quit.png" @click="quit">
            <img class="btn" src="/static/game_assets/common/again.png" @click="again" />
          </div>
        </div>
      </div>
    </div>
    <!-- <bgMusic :status="status"></bgMusic> -->
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common'
import data from './data/data.json'

export default {
  name: 'game218',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      status: 1,
      isPlay: false,
      audioUrl: '',

      questionList: [],
      currect: {},
      showQuestion: true,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time() {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play() {
      this.audioUrl = this.showQuestion ? this.currect.questionAudio : this.currect.answerAudio
      this.$nextTick(() => {
        this.$refs.music.load()
        this.$refs.music.play()
        this.isPlay = true
      })
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnd() {
      this.isPlay = false
    },

    start() {
      this.status = 3
      // this.timing()

      this.questionList = data.data
      this.startProcess()
    },

    next() {
      this.show = false
      this.startProcess()
    },

    startProcess() {
      this.currect = this.questionList[this.number]
      this.number++
      this.play()
    },

    handleClick(type) {
      if (type === 1) {
        this.showQuestion = false
        this.play()
      } else {
        this.showQuestion = true
        this.startProcess()
      }
    },

    submit() {
      this.pause()
      this.isStop = true
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    quit() {
      this.pause()
      this.isStop = true
      this.$router.go(-1)
    },

    again() {
      this.showQuestion = true
      this.number = 0
      this.second = 0
      this.isStop = false
      this.start()
      this.timing()
    },
  }
}
</script>

<style lang="scss">
.game218-page {
  position: relative;
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game218/bg.png");
  }

  .setting-page {
    .title {
      color: #5E381F;
    }
  }

  .game-synopsis {
    width: 707px;
    height: 444px;
    margin: 34px;
    padding: 64px 48px;
    background: #FFFEF3;
    border-radius: 36px;
    border: 2px solid #7BBD41;

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #5E381F;
      padding-bottom: 24px;
    }

    .synopsis-content {
      padding-bottom: 18px;
      margin: 0;
      font-size: 32px;
      line-height: 50px;
      font-weight: 400;
      color: #5E381F;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .btn-right {
      position: absolute;
      top: 40px;
      right: 100px;
      height: 76px;
      display: flex;
      z-index: 99;
    }

    .icon-img {
      padding: 4px 15px;
      // border: 1px solid #37B982;
      border-radius: 38px;
      font-size: 0;

      img {
        width: 50px;
        height: 50px;
        vertical-align: middle;
        cursor: pointer;
      }

      span {
        display: inline-block;
        padding-left: 8px;
        font-size: 28px;
        line-height: 50px;
        vertical-align: middle;
        user-select: none;
      }
    }

    .top {
      position: absolute;
      top: 128px;
      left: 0;
      width: 100%;
      height: 82px;
      padding: 0 140px;

      .top-icon {
        position: absolute;
        top: 0;
        left: 140px;
        width: 82px;
        cursor: pointer;
      }

      .top-text {
        display: block;
        font-size: 40px;
        line-height: 56px;
        text-align: center;
        font-weight: 600;
        color: #0055A6;
      }
    }

    .content {
      position: relative;
      width: 1720px;
      height: 100%;
      margin-right: 20px;
      display: flex;
      justify-content: center;

      .img-left {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 530px;
      }

      .content-question {
        position: absolute;
        bottom: 275px;
        right: 210px;
        width: 931px;
        height: 524px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        padding: 130px 90px 54px 110px;
        background-repeat: no-repeat;
        background-size: cover;
        background-image: url("/static/game_assets/game218/text_bg_1.png");

        p {
          width: 100%;
          margin: 0;
          font-size: 56px;
          line-height: 78px;
          font-weight: 500;
          color: #5E381F;
        }

        .btn {
          width: 270px;
          cursor: pointer;
        }
      }

      .img-right {
        position: absolute;
        right: 0;
        bottom: 160px;
        width: 420px;
      }

      .content-answer {
        position: absolute;
        bottom: 221px;
        left: 320px;
        width: 933px;
        height: 780px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 50px 32px;
        background-repeat: no-repeat;
        background-size: cover;
        background-image: url("/static/game_assets/game218/text_bg_2.png");

        p {
          width: 100%;
          margin: 0;
          font-size: 26px;
          line-height: 40px;
          margin: 5px 0;
        }
      }

      .btn {
        position: absolute;
        bottom: 54px;
        width: 270px;
        cursor: pointer;
      }

      .btn-group {
        position: absolute;
        bottom: 54px;
        right: 500px;
        width: 660px;
        height: 115px;
        display: inline-flex;
        justify-content: space-between;

        .btn {
          position: relative;
          bottom: 0;
          width: 270px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
<template>
  <div class="game196-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPage title="合成数字" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、请根据提示要求，选择对应等式:</p>
        <p class="synopsis-content">2、训练结束会出现成绩单;</p>
        <p class="synopsis-content">3、本训练难度为A（简单）。</p>
      </div>
    </settingPage>

    <div class="game-content" v-if="status === 3">
      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>
      <div class="title">
        <img class="title-bg" src="/static/game_assets/game206/title_bg.png" />
        <span class="title-text">合成数字-A（简单）</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game196/content_bg.png" />
        <img class="left-icon" src="/static/game_assets/game196/img1.png" />
        <img class="right-icon" src="/static/game_assets/game196/img2.png" />
        <div class="content-title">{{`如何得到${currect.sum}`}}</div>

        <div class="content-main">
          <div class="content-item" v-for="(item, index) in currect.answerList" :key="index + 'item'" @click="chooseItem(item.index)">
            <img class="item-bg" src="/static/game_assets/game196/btn_bg_1.png" />
            <img class="item-bg" v-if="answer === item.index" src="/static/game_assets/game196/btn_bg_2.png" />
            <span class="item-text">{{item.text}}</span>
          </div>
        </div>

        <div class="content-img store">
          <img class="item-bg" src="/static/game_assets/game196/item_bg_1.png" />
          <span class="item-text">题目分数</span>
          <span class="item-num">{{store}}</span>
        </div>

        <div class="content-img number">
          <img class="item-bg" src="/static/game_assets/game196/item_bg_2.png" />
          <span class="item-text">题目数量</span>
          <span class="item-num">{{number}}</span>
        </div>

        <div class="content-img time">
          <img class="item-bg" src="/static/game_assets/game196/item_bg_1.png" />
          <span class="item-text">训练用时</span>
          <span class="item-time">{{time}}</span>
        </div>
      </div>

      <div class="footer">
        <img v-if="answer" class="img" src="/static/game_assets/common/confirm.png" @click="confirm" />
      </div>
    </div>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'

export default {
  name: 'game196',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data() {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      level: 1,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isPlay: false,
      show: false,

      answer: 0,
      currect: {
        sum: 0,
        answer: [],
        answerList: []
      },  
      

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time() {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    // TODO: 返回首页
		goHome() {
      this.pause()
			this.$router.go(-1)
		},

    play() {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start() {this.level = Number(this.info.level) || 1
      
      this.status = 3
      // this.timing()
      this.startProcess()
      this.play()
    },

    startProcess() {
      this.currect.answerList = []
      let list = []
      while (this.currect.answerList.length < 4) {
        const num1 = api.randomNum(1, 10)
        const num2 = api.randomNum(0, 10)
        if (!list.includes(num1 + num2)) {
          this.currect.answerList.push({
            text: `${num1} + ${num2}`,
            index: list.length + 1
          })
          list.push(num1 + num2)
        }
      }
      this.currect.sum = list[0]
      this.currect.answer = 1
      this.currect.answerList = api.shuffle(this.currect.answerList)
      this.number++
    },

    chooseItem(index) {
      this.answer = index
    },

    confirm() {
      if (this.currect.answer === this.answer) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      this.store = 10 * this.succesNum
      if (this.number >= 10) {
        this.submit()
      } else {
        this.answer = 0
        this.startProcess()
      }
    },

    stop() {
      this.pause()
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.pause()
      this.isStop = true
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answer = 0
      this.currect = {
        sum: 0,
        answer: 0,
        answerList: []
      },
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game196-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game126/bg.png");
  } 

  .game-synopsis {
    width: 1605px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 40px;
      left: 105px;
      width: 175px;
      display: flex;
      justify-content: space-between;

      .top-icon {
        width: 80px;
        cursor: pointer;
      }
    }

    .title {
      position: absolute;
      top: 0;
      width: 932px;
      height: 125px;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px;
        height: 125px;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 28px 0 56px 0;
        font-size: 36px;
        line-height: 36px;
        text-align: center;
        color: #1B1D2D;
      }
    }

    .content {
      position: relative;
      width: 1786px;
      height: 922px;
      margin-top: 158px;
      margin-left: 76px;
      padding: 160px 618px 326px 413px;

      .content-bg {
        position: absolute;
        top: 0;
        left: 18px;
        width: 1655px;
      }

      .left-icon {
        position: absolute;
        bottom: 87px;
        left: 0;
        width: 440px;
      }

      .right-icon {
        position: absolute;
        bottom: 74px;
        right: 0;
        width: 541px;
      }

      .content-title {
        position: relative;
        width: 100%;
        padding-bottom: 83px;
        font-size: 44px;
        line-height: 44px;
        text-align: center;
        font-weight: 500;
        color: #1B1D2D;
      }

      .content-main {
        position: relative;
        width: 100%;
        height: 310px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;

        .content-item {
          position: relative;
          width: 353px;
          height: 138px;
          cursor: pointer;

          .item-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 353px;
          }

          .item-text {
            position: relative;
            display: block;
            font-size: 77px;
            line-height: 138px;
            text-align: center;
            color: #1B1D2D;
          }
        }
      }

      .content-img {
        position: absolute;
        right: 123px;
        width: 369px;
        height: 219px;
        padding: 28px 42px;
        display: flex;

        .item-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 369px;
        }

        .item-text {
          position: relative;
          display: inline-block;
          width: 36px;
          font-size: 36px;
          line-height: 40px;
          text-align: center;
          font-weight: 500;
          color: #253000;
        }

        .item-num {
          position: relative;
          display: block;
          width: 100%;
          height: 100%;
          padding-left: 37px;
          font-size: 92px;
          line-height: 170px;
          text-align: center;
          color: #312B4F;
          font-family: Impact;
        }
        
        .item-time {
          position: relative;
          display: block;
          width: 100%;
          height: 100%;
          padding-left: 37px;
          font-size: 48px;
          line-height: 170px;
          text-align: center;
          color: #312B4F;
          font-family: Impact;
        }
      }

      .store {
        top: 63px;
      }

      .number {
        top: 290px;
      }

      .time {
        top: 518px;
      }
    }

    .footer {
      position: absolute;
      bottom: 72px;
      display: flex;
      justify-content: space-between;
      width: 1480px;

      .img {
        height: 115px;
        cursor: pointer;
      }
    }
  }
}
</style>
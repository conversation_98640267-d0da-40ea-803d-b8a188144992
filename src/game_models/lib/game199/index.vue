<template>
  <div class="game199-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <audio ref="music2" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <settingPage title="配图减法" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、请根据提示配图，进行减法计算，在屏幕下方键盘中选择相应的选项;</p>
        <p class="synopsis-content">2、训练结束会出现成绩单。</p>
      </div>
    </settingPage>

    <div class="game-content" v-if="status === 3">
      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>
      <div class="title">
        <img class="title-bg" src="/static/game_assets/game206/title_bg.png" />
        <span class="title-text">{{`${currect.number1} - ${currect.number2} = ?`}}</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game199/content_bg.png" />
        <img class="left-icon" src="/static/game_assets/game199/img.png" />
        <img class="right-icon" src="/static/game_assets/game196/img2.png" />

        <div class="content-top">
          <div class="top-item" v-for="item in currect.number1" :key="item + 'item'">
            <img class="item-img" :src="`/static/game_assets/game199/item_${currect.imgIndex}.png`" />
            <img v-if="item > (currect.number1 - currect.number2)" class="item-icon" src="/static/game_assets/game199/icon.png" />
          </div>
        </div>

        <div class="content-title">{{`${currect.number1} - ${currect.number2}`}}</div>

        <div class="content-bottom">
          <div class="bottom-left">
            <div class="left-item" v-for="item in 10" :key="item + 'btn'" @click="chooseItem(item)">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_1.png" />
              <span class="item-num">{{item % 10}}</span>
            </div>
          </div>

          <div class="bottom-right">
            <div class="right-item item1">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_2.png" />
              <span class="item-num">{{answer}}</span>
            </div>

            <div class="right-item item2" @click="answer = ''">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_3.png" />
              <span class="item-num">X</span>
            </div>

            <div class="right-item item3" @click="confirm">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_4.png" />
              <span class="item-num">确定</span>
            </div>
          </div>
        </div>

        <div class="content-img store">
          <img class="item-bg" src="/static/game_assets/game196/item_bg_1.png" />
          <span class="item-text">题目分数</span>
          <span class="item-num">{{store}}</span>
        </div>

        <div class="content-img number">
          <img class="item-bg" src="/static/game_assets/game196/item_bg_2.png" />
          <span class="item-text">题目数量</span>
          <span class="item-num">{{number}}</span>
        </div>

        <div class="content-img time">
          <img class="item-bg" src="/static/game_assets/game196/item_bg_1.png" />
          <span class="item-text">训练用时</span>
          <span class="item-time">{{time}}</span>
        </div>
      </div>
    </div>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import api from '../../utils/common.js'

export default {
  name: 'game199',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
  },

  data() {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      level: 1,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isPlay: false,
      show: false,

      answer: '',
      currect: {
        answer: 0,
        number1: 0,
        number2: 0,
        imgIndex: 0
      },  
      

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time() {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    // TODO: 返回首页
		goHome() {
      this.pause()
			this.$router.go(-1)
		},

    play() {
      this.$refs.music.play()
      this.isPlay = true
    },

    playError() {
      this.$refs.music2.play()
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
      // this.timing()
      this.startProcess()
      this.play()
    },

    startProcess() {
      const imgIndex = [1, 2, 3, 4, 5, 6, 7, 8]
      const num1 = api.randomNum(1, 10)
      const num2 = api.randomNum(0, 10)
      this.currect.imgIndex = api.getRandomArray(imgIndex, 1)[0]
      this.currect.number1 = Math.max(num1, num2)
      this.currect.number2 = Math.min(num1, num2)
      this.currect.answer = this.currect.number1 - this.currect.number2
      this.number++
    },

    chooseItem(item) {
      if (this.answer.length >= 2) return
      this.answer = this.answer + (item === 10 ? 0 : item).toString()
    },

    confirm() {
      if (this.answer === '') {
        this.playError()
        return
      }

      if (this.currect.answer === Number(this.answer)) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      this.store = 10 * this.succesNum
      if (this.number >= 10) {
        this.submit()
      } else {
        this.answer = ''
        this.startProcess()
      }
    },

    submit() {
      this.pause()
      this.isStop = true
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answer = ''
      this.currect = {
        answer: 0,
        number1: 0,
        number2: 0,
        imgIndex: 0
      },
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game199-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game126/bg.png");
  } 

  .game-synopsis {
    width: 1605px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 40px;
      left: 105px;
      width: 175px;
      display: flex;
      justify-content: space-between;

      .top-icon {
        width: 80px;
        cursor: pointer;
      }
    }

    .title {
      position: absolute;
      top: 0;
      width: 932px;
      height: 125px;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px;
        height: 125px;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 28px 0 56px 0;
        font-size: 36px;
        line-height: 36px;
        text-align: center;
        color: #1B1D2D;
      }
    }

    .content {
      position: relative;
      width: 1892px;
      height: 922px;
      margin-top: 154px;
      padding: 109px 469px 184px 160px;

      .content-bg {
        position: absolute;
        top: 0;
        left: 78px;
        width: 1479px;
      }

      .left-icon {
        position: absolute;
        bottom: 13px;
        left: 0;
        width: 362px;
      }

      .right-icon {
        position: absolute;
        bottom: 44px;
        right: 0;
        width: 541px;
      }

      .content-top {
        position: relative;
        width: 100%;
        display: flex;
        justify-content: center;

        .top-item {
          position: relative;
          width: 114px;
          height: 188px;

          .item-img {
            position: absolute;
            top: 0;
            left: 0;
            width: 114px;
          }

          .item-icon {
            position: absolute;
            top: 37px;
            left: 3px;
            width: 100px;
          }
        }
      }

      .content-title {
        position: relative;
        width: 100%;
        padding: 35px 0 45px 0;
        font-size: 98px;
        line-height: 98px;
        text-align: center;
        font-weight: 500;
        color: #1B1D2D;
      }

      .content-bottom {
        position: relative;
        width: 100%;
        padding: 0 124px 0 154px;
        display: flex;

        .bottom-left {
          width: 557px;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;
          margin-right: -10px;

          .left-item {
            position: relative;
            width: 135px;
            height: 136px;
            margin: -12px -15px;
            cursor: pointer;

            .item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 135px;
            }

            .item-num {
              position: relative;
              display: block;
              font-size: 48px;
              line-height: 136px;
              text-align: center;
              color: #312B4F;
            }
          }
        }

        .bottom-right {
          width: 450px;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;

          .right-item {
            position: relative;

            .item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%
            }

            .item-num {
              position: relative;
              display: block;
              font-size: 48px;
              line-height: 136px;
              text-align: center;
              color: #312B4F;
            }
          }

          .item1 {
            width: 291px;
            height: 136px;
            margin: -12px -12px -12px 0;
          }

          .item2 {
            width: 190px;
            height: 136px;
            margin: -12px;
            cursor: pointer;
          }

          .item3 {
            width: 462px;
            height: 136px;
            margin: -12px -12px -12px 0;
            cursor: pointer;
          }
        }
      }

      .content-img {
        position: absolute;
        right: 123px;
        width: 369px;
        height: 219px;
        padding: 28px 42px;
        display: flex;

        .item-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 369px;
        }

        .item-text {
          position: relative;
          display: inline-block;
          width: 36px;
          font-size: 36px;
          line-height: 40px;
          text-align: center;
          font-weight: 500;
          color: #253000;
        }

        .item-num {
          position: relative;
          display: block;
          width: 100%;
          height: 100%;
          padding-left: 37px;
          font-size: 92px;
          line-height: 170px;
          text-align: center;
          color: #312B4F;
          font-family: Impact;
        }
        
        .item-time {
          position: relative;
          display: block;
          width: 100%;
          height: 100%;
          padding-left: 37px;
          font-size: 48px;
          line-height: 170px;
          text-align: center;
          color: #312B4F;
          font-family: Impact;
        }
      }

      .store {
        top: 63px;
      }

      .number {
        top: 290px;
      }

      .time {
        top: 518px;
      }
    }

    .footer {
      position: absolute;
      bottom: 72px;
      display: flex;
      justify-content: space-between;
      width: 1480px;

      .img {
        height: 115px;
        cursor: pointer;
      }
    }
  }
}
</style>
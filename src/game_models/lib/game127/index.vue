<template>
  <div class="game127-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
		<settingPage title="学习颜色" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、小松系列，通过学习并对比来掌握对各种颜色的认知，包括有橙、紫、粉、深红、褐、灰六种颜色;</p>
        <p class="synopsis-content">2、本训练难度为B(普通)。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <img class="top-img" src="/static/game_assets/game122/icon.png" />

      <div class="content1" v-if="number === 1">
        <img v-if="!choose" class="content-top" src="/static/game_assets/game126/item_6.png" />
        <img v-else class="content-top" :src="`/static/game_assets/game127/item_${choose}.png`" />
        
        <div class="content-bottom">
          <div class="content-item" v-for="item in questions" :key="item.index + 'item'" @click="chooseItem(item.index)">
            <img class="item-img" :src="`/static/game_assets/game127/item_${item.index}_1.png`" />
            <span class="item-text">{{item.color}}</span>
          </div>
        </div>
      </div>

      <div class="content2" v-if="number === 2">
        <div class="content-top">
          <draggable 
            :class="['top-item', isShowCorrect && 'translate-top2']" 
            v-for="(item, index) in questions"
            :key="item + 'item1'"
            :group="group2" 
            v-model="dragItemTop[index]"
            @add="dragAdd(item, index)"
          >
            <img class="item-img" :src="`/static/game_assets/game127/item_${item}.png`" />
            <img class="item-icon" src="/static/game_assets/game126/item_bg.png" />
            <img v-for="it in dragItemTop[index]" :key="it.name + 'item2'" class="item-icon" :src="`/static/game_assets/game127/item_${it.name}_2.png`" />
            <img v-if="isShowCorrect" class="item-icon2" src="/static/game_assets/game126/icon.png" />
          </draggable>
        </div>

        <div class="content-bottom">
          <draggable 
            class="bottom-item" 
            v-for="item in 4"
            :key="item + 'item2'"
            :group="group1" 
            v-model="dragItemBottom[item-1]"
            @start="dragStart(item - 1)"
          >
            <img v-for="it in dragItemBottom[item-1]" :key="it.name + 'img2'" class="item-img2" :src="`/static/game_assets/game127/item_${it.name}_2.png`" />
          </draggable>
        </div>
      </div>

      <div class="top">
        <img v-if="!isPlay" @click="play('')" class="top-icon" src="/static/game_assets/common/play.png" />
				<img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>

      <div class="footer">
        <img v-if="number > 1" class="img1" src="/static/game_assets/game81/left_btn.png" @click="clickLeft">
        <img v-if="number < 2" class="img2" src="/static/game_assets/game81/right_btn.png" @click="clickRight">
      </div>

      <div v-if="number === 1" :class="['sucess', isShowCorrect ? 'translate-top' : '']">
        <img class="img" src="/static/game_assets/game29/hot_balloon.png">
      </div>
    </div>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import api from '../../utils/common.js'

export default {
  name: 'game127',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    draggable
  },

  data() {
    return {
      musicUrl: '',
      number: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,

      isShowCorrect: false,
      choose: 0,
      answer: 0,
      questions: [],
      dragItemTop: [[], [], [], []],
      dragItemBottom: [],
      group1: {
        name: 'itemList',
        pull: true,
        put: false,
        sort: false
      },
      group2: {
        name: 'itemList',
        pull: false,
        put: true,
        sort: false
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    // TODO: 返回首页
		goHome() {
      this.pause()
			this.$router.go(-1)
		},

		play(url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnd() {
      this.isPlay = false
    },

    start() {
      this.status = 3
      this.startProcess()
      // this.timing()
    },

    startProcess() {
      const list = [
        {
          index: 1,
          color: '橙色'
        },
        {
          index: 2,
          color: '紫色'
        },
        {
          index: 3,
          color: '粉色'
        },
        {
          index: 4,
          color: '深红色'
        },
        {
          index: 5,
          color: '褐色'
        },
        {
          index: 6,
          color: '灰色'
        }
      ]
      if (this.number === 1) {
        this.questions = api.getRandomArray(list, 4)
        this.answer = api.getRandomArray(this.questions, 1)[0].index
        this.play(`/static/game_assets/audio/game127/audio_${this.answer}.mp3`)
      } else {
        const arr = api.getRandomArray(list, 4)
        this.questions = api.shuffle(arr.map(item => item.index))
        for (let i = 0; i < 4; i++) {
          const it = arr[i]
          this.dragItemBottom.push([{
            name: it.index,
            startIndex: i,
          }])
        }
        this.play(`/static/game_assets/audio/game126/title.mp3`)
      }
    },

    chooseItem(index) {
      this.choose = index
      if (this.choose === this.answer) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      this.isShowCorrect = true
      setTimeout(() => {
        this.isShowCorrect = false
        this.choose = 0
        this.answer = 0
        this.questions = []
        this.number++
        this.startProcess()
      }, 2300)
    },

    dragStart(item) {
      this.choose = this.dragItemBottom[item][0].name
    },

    dragAdd(it, index) {
      if (this.dragItemTop[index].length > 1) {
        const item = this.dragItemTop[index].pop()
        this.dragItemBottom[item.startIndex].push(item)
        return
      }

      if (this.choose !== it) {
        this.errorNum++
      } else {
        this.succesNum++
      }

      const list = this.dragItemTop.filter(item => item.length)
      if (list.length >= 4) {
        this.isShowCorrect = true
        setTimeout(() => {
          this.isShowCorrect = false
          this.submit()
        }, 1800)
      }
    },

    clickLeft() {
      if (this.isShowCorrect) return
      this.pause()
      this.number--
      this.choose = 0
      this.answer = 0
      this.questions = []
      this.dragItemTop = [[], [], [], []]
      this.dragItemBottom = []
      this.startProcess()
    },

    clickRight() {
      if (this.isShowCorrect) return
      this.pause()
      this.number++
      this.choose = 0
      this.answer = 0
      this.questions = []
      this.dragItemTop = [[], [], [], []]
      this.dragItemBottom = []
      this.startProcess()
    },

    submit() {
      this.isStop = true
			this.store = 20 * this.succesNum > 100 ? 100 : 20 * this.succesNum
			this.infos[0].value = this.second
			this.infos[1].value = this.succesNum
			this.infos[2].value = this.errorNum
			this.status = 4
			this.params = {
				id: this.info.id,
				grade: '',
				time: this.second,
				totalPoints: this.store
			}
    },

    again() {
      this.number = 1
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
			this.choose = 0
      this.answer = 0
      this.questions = []
      this.dragItemTop = [[], [], [], []]
      this.dragItemBottom = []
			this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game127-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game126/bg.png");
  } 

  .game-synopsis {
    width: 1576px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top-img {
      position: absolute;
      top: 64px;
      right: 129px;
      width: 221px;
    }

    .top {
			position: absolute;
			top: 40px;
			left: 105px;
			width: 175px;
			display: flex;
			justify-content: space-between;

			.top-icon {
				width: 80px;
				cursor: pointer;
			}
		}

    .content1 {
      position: relative;
      width: 1460px;
      height: 751px;
      margin-top: 23px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;

      .content-top {
        width: 255px;
        height: 383px;
      }

      .content-bottom {
        width: 100%;
        height: 262px;
        display: flex;
        justify-content: space-between;

        .content-item {
          position: relative;
          width: 387px;
          cursor: pointer;

          .item-img {
            position: absolute;
            top: 0;
            width: 387px;
          }

          .item-text {
            display: block;
            width: 387px;
            height: 48px;
            position: absolute;
            bottom: 0;

            font-size: 48px;
            line-height: 48px;
            text-align: center;
            color: #1E1E1D;
          }
        }
      }
    }

    .content2 {
      position: relative;
      width: 1166px;
      height: 782px;
      margin-top: 82px;
      margin-right: 28px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .content-top {
        position: relative;
        width: 100%;
        height: 495px;
        display: flex;
        justify-content: space-between;

        .top-item {
          position: relative;
          width: 256px;
          height: 495px;
          transition: all 1.5s ease-in;

          .item-img {
            position: absolute;
            top: 0;
            left: 0;
            width: 256px;
          }

          .item-icon {
            position: absolute;
            left: 104px;
            bottom: 189px;
            width: 48px;
          }

          .item-icon2 {
            position: absolute;
            left: 86px;
            bottom: 0;
            width: 82px;
          }

          .item-img2 {
            position: absolute;
            left: 104px;
            bottom: 189px;
            width: 48px;
          }
        }
      }

      .content-bottom {
        height: 285px;
        padding: 0 353px;
        display: flex;
        justify-content: space-between;

        .bottom-item {
          width: 100px;
          cursor: pointer;

          .item-img2 {
            width: 100px;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 22px;
      width: 1772px;
      height: 141px;
      z-index: 3;

      .img1 {
        position: absolute;
        top: 0;
        left: 0;
        height: 141px;
        cursor: pointer;
      }

      .img2 {
        position: absolute;
        top: 0;
        right: 0;
        height: 141px;
        cursor: pointer;
      }
    }

    .sucess {
      position: absolute;
      bottom: -762px;
      width: 774px;
      height: 762px;
      transition: all 2s ease-in;

      .img {
        width: 774px;
        height: 762px;
      }
    }

    .translate-top {
      transform: translateY(-2442px);
    }

    .translate-top2 {
      transform: translateY(-1742px);
    }
  }
}

@keyframes flash{
  0% {
    transform: scale(1);
  }
  80% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
</style>
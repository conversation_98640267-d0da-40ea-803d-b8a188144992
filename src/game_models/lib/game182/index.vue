<template>
  <div class="game182-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <settingPage title="估计数量" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、本项目考察数量的估计。</p>
        <p class="synopsis-content">2、题型是填空题。</p>
        <p class="synopsis-content">3、训练对象：数量的估计缺失/困难的您。</p>
        <p class="synopsis-content">4、一组10题答对9-10题的您可以终止这一项目训练。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="top">{{time ? time : ''}}{{title}}</div>

      <div class="content1" v-show="answerStatus === 'topic'">
        <template v-for="(item, index) in 120">
          <div class="content-item" :key="item + 'img'">
            <img v-if="currect.indexList.includes(index)" :src="`/static/game_assets/game182/item_${currect.imgIndex}.png`" />
          </div>
        </template>
      </div>

      <div class="content2" v-show="answerStatus === 'answer' && !isShowCorrect && !isShowError">
        <div class="input">
          <img class="img" src="/static/game_assets/game35/input.png" />
          <img class="clear" src="/static/game_assets/game35/clear.png" @click="answer = ''" />
          <span class="number">{{ answer }}</span>
        </div>

        <div class="key">
          <img v-for="item in 10" :key="item + 'key'" class="btn" :src="`/static/game_assets/game182/btn_${item % 10}.png`" @click="chooseItem(item % 10)" />
          <img class="confirm" src="/static/game_assets/game182/confirm.png" @click="submit" />
        </div>
      </div>

      <div class="footer" v-show="answerStatus === 'answer'">
        <img class="img" src="/static/game_assets/common/stop.png" @click="stop" >
        <img v-if="isShowCorrect || isShowError" class="img" src="/static/game_assets/common/next.png" @click="toNext">
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 差距在15个之内正确
 
export default {
  name: 'game182',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isStop: true,
      isShowCorrect: false,
      isShowError: false,
      
      time: 5,
      title: '',
      answerStatus: 'topic',
      answer: '',
      currect: {
        text: '',
        imgIndex: 0,
        indexList: [],
        answer: 0
      },

      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  methods: {
    timing(flag = true) {
      if (flag && this.isStop) return
      if (!flag && !this.time) {
        this.answerStatus = 'answer'
        this.title = '估计图中有多少' + this.currect.text
        this.isStop = false
        this.timing()
        return
      }
      setTimeout(() => {
        if (flag) this.second++
        if (!flag) this.time--
        this.timing(flag)
      }, 1000)
    },

    play() {
      this.$refs.music.play()
    },

    start() {
      this.startProcess()
      this.status = 3
    },

    startProcess() {
      const list1 = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]
      const list2 = [0, 1, 2, 3, 4, 5, 6, 7]
      const num1 = api.randomNum(5, 15) // 有几列
      const num2 = api.randomNum(2, 8) // 每列有几只
      const liList = api.getRandomArray(list1, num1)

      this.currect.indexList = []
      this.currect.answer = num1 * num2
      this.currect.imgIndex = api.randomNum(1, 3)
      if (this.currect.imgIndex === 3) {
        this.currect.text = '片叶子'
      } else {
        this.currect.text = '只蝴蝶'
      }
      for (let i = 0; i < num1; i++) { 
        const liIndex = liList[i]
        const haList = api.getRandomArray(list2, num2)
        for (let j = 0; j < num2; j++) {
          this.currect.indexList.push(liIndex * 8 + haList[j])
        }
      }
      this.title = '秒内估计出一共有多少' + this.currect.text
      this.timing(false)
    },

    chooseItem(item) {
      if (this.answer.length >= 3) return
      this.answer = this.answer + (item === 10 ? 0 : item).toString()
    },

    toNext() {
      this.number++
      this.isShowCorrect = false
      this.isShowError = false
      if (this.number < 10) {
        this.time = 5
        this.answer = ''
        this.answerStatus = 'topic'
        this.startProcess()
      } else {
        this.isStop = true
        this.store = 10 * this.succesNum
        this.infos[0].value = this.second
        this.infos[1].value = this.succesNum
        this.infos[2].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: '',
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    submit() {
      if (!this.answer) {
        this.play()
        return
      }
      this.isStop = true
      if (Number(this.answer) >= this.currect.answer - 15 && Number(this.answer) <= this.currect.answer + 15) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }
    },

    stop() {
      if (!this.isStop) this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      if (this.answerStatus === 'answer' && !this.isShowCorrect && !this.isShowError) {
        this.isStop = false
        this.timing()
      }
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.isStop = false
      this.store = 0
      this.answer = ''
      this.time = 5
      this.answerStatus = 'topic'
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game182-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game182/bg.png");
  }

  .game-synopsis {
    width: 807px;
    height: 494px;
    margin: 34px;
    padding: 33px 30px;
    background: #fff;
    border: 2px solid #58AD49;
    border-radius: 36px;

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #5E381F;
    }

    .synopsis-content {
      padding-top: 18px;
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 400;
      color: #5E381F;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
			position: absolute;
			top: 40px;
      left: 0;
      width: 100%;
      font-size: 50px;
      line-height: 82px;
      text-align: center;
      font-weight: 600;
      color: #0B4F5D;
		}

    .content1 {
      position: relative;
      width: 1200px;
      height: 640px;
      margin-bottom: 30px;
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;

      .content-item {
        position: relative;
        width: 50px;
        height: 50px;
        margin: 15px;

        img {
          position: absolute;
          width: 50px;
        }
      }
    }

    .content2 {
      position: relative;
      width: 454px;
      height: 718px;
      margin-bottom: 48px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .input {
        position: relative;
        width: 312px;
        height: 186px;
        padding-bottom: 58px;

        .img {
          position: absolute;
          top: 0;
          left: 0;
          width: 312px;
          height: 128px;
        }

        .clear {
          position: absolute;
          top: 29px;
          right: 24px;
          width: 57px;
          height: 58px;
          cursor: pointer;
        }

        .number {
          position: absolute;
          top: 20px;
          left: 80px;
          width: 145px;
          height: 74px;
          font-size: 52px;
          line-height: 74px;
          text-align: center;
          font-family: Impact;
          color: #fff;
        }
      }

      .key {
        flex: 1;
        width: 100%;
        display: inline-flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;

        .btn {
          width: 118px;
          height: 118px;
          cursor: pointer;
        }

        .confirm {
          width: 278px;
          height: 118px;
          cursor: pointer;
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 88px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      width: 1620px;

      .img {
        height: 115px;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px;
      margin-bottom: 40px;
      z-index: 99;
    }
  }
}
</style>
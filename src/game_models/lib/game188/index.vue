<template>
  <div class="game188-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source src="/static/game_assets/audio/game188/title.mp3" type="audio/mpeg" />
    </audio>
    <settingPage title="超市商品" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、请根据提示，说出购买的物品要付多少钱，由训练者/治疗师判定并选择对应选项；</p>
        <p class="synopsis-content">2、训练结束会出现成绩单。</p>
      </div>
    </settingPage>

    <div class="game-content" v-if="status === 3">
      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />

        <span class="top-text">购买下面的物品要付多少钱？</span>
      </div>

      <div class="content">
        <div class="content-item" v-for="item in currect" :key="item.index + 'item'">
          <img class="item-img" :src="`/static/game_assets/game188/item_${item.index}.png`" />
          <span class="item-text">{{item.text}}</span>
        </div>
      </div>

      <div class="content-right">
        <p class="item">题目分数：{{store}}</p>
        <p class="item">题目数量：{{number}}</p>
        <p class="item">训练用时：{{time}}</p>
      </div>

      <div class="footer">
        <img class="icon1" src="/static/game_assets/game59/correct.png" @click="handleClick(1)" />
        <img class="icon2" src="/static/game_assets/game59/error.png" @click="handleClick(2)" />
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common'
 
export default {
  name: 'game188',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,

      currect: [],

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time() {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play() {
      this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnd() {
      this.isPlay = false
    },

    start() {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess() {
      const list = [
        {
          index: 1,
          text: '棒棒糖：5角'
        },
        {
          index: 2,
          text: '方便面：6元'
        },
        {
          index: 3,
          text: '雪碧：3元'
        },
        {
          index: 4,
          text: '旺旺雪饼：16元'
        },
        {
          index: 5,
          text: '牛奶：11元'
        },
        {
          index: 6,
          text: '火腿肠：9元'
        },
        {
          index: 7,
          text: '玩具车：13元'
        }
      ]
      const num = api.randomNum(1, 3)
      this.currect = api.getRandomArray(list, num)
      this.number++
      this.play()
    },

    handleClick(num) {
      this.pause()
      if (num === 1) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      this.store = 10 * this.succesNum

      if (this.number < 10) {
        this.startProcess()
      } else {
        this.submit()
      }
    },

    submit() {
      this.pause()
      this.isStop = true
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game188-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game188/bg.png");
  }

  .setting-page {
    .title {
      color: #fff;
    }
  }

  .game-synopsis {
    width: 860px;
    height: 500px;
    margin: 34px;
    padding: 33px 30px;
    background: #fff;
    border-radius: 36px;

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #5E381F;
      padding-bottom: 18px;
    }

    .synopsis-content {
      padding-bottom: 18px;
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 400;
      color: #5E381F;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 128px;
      left: 0;
      width: 100%;
      height: 82px;
      padding: 0 140px;

      .top-icon {
        position: absolute;
        top: 0;
        left: 140px;
        width: 82px;
        cursor: pointer;
      }

      .top-text {
        display: block;
        font-size: 50px;
        line-height: 82px;
        text-align: center;
        font-weight: 600;
        color: #fff;
      }
    }

    .content {
      position: relative;
      width: 1284px;
      height: 461px;
      margin-top: 35px;
      display: flex;
      justify-content: center;

      .content-item {
        width: 356px;
        height: 100%;
        margin: 0 36px;
        display: inline-flex;
        flex-direction: column;
        justify-content: space-between;

        .item-img {
          width: 356px;
          height: 393px;
        }

        .item-text {
          display: block;
          padding-top: 18px;
          font-size: 36px;
          line-height: 50px;
          text-align: center;
          font-weight: 500;
          color: #fff;
        }
      }
    }

    .content-right {
      position: absolute;
      top: 226px;
      right: 88px;
      width: 250px;
      height: 268px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .item {
        width: 250px;
        height: 76px;
        border: 2px solid #fff;
        border-radius: 4px;

        font-size: 24px;
        line-height: 72px;
        text-align: center;
        color: #fff;
      }
    }

    .footer {
      position: absolute;
      bottom: 30px;
      width: 582px;
      display: flex;
      justify-content: space-between;

      img {
        height: 115px;
        cursor: pointer;
      }
    }
  }
}
</style>
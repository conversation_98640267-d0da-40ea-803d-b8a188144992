<template>
  <div class="game44-page">
    <!-- <audio ref="music" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio> -->
    <div class="page-bg"></div>
    <settingPage @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、根据提示，记忆显示的形状，记忆结束后，请回答屏幕中间的形状在刚刚记忆中是否存在，有的话请选择“是”，反之选择“否”。</p>
        <p class="synopsis-content">2、一共三关，难度递增。</p>
        <p class="synopsis-content">3、错误数、正确数、做题总数和训练用时将作为成绩记录下来。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="title">
        <img class="bg" src="/static/game_assets/game8/brown_bg2.png">
        <p class="text">{{title}}</p>
      </div>

      <div class="content">
        <img :class="['content-img', isShowClass && 'item-opacity']" :src="`/static/game_assets/game44/item_${current}.png`" />
      </div>
      
      <div class="footer" v-if="answerStatus !== 'demo'">
        <div class="btn" @click="stop">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>

        <div class="btn-group">
          <div class="btn" @click="handleClick(1)">
            <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
            <span class="text">是</span>
          </div>

          <div class="btn" @click="handleClick(0)">
            <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
            <span class="text">否</span>
          </div>
        </div>
      </div>

      <div class="judge" v-if="answerStatus === 'judge'">
        <img class="img" :src="`/static/game_assets/game8/${isCorrect ? 'success' : 'error'}.png`">
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 1级 2个图形 4个选项
// 2   3      5
// 3   4      6

export default {
  name: 'game44',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      index: 0,
      show: false,
      isStop: false,
      isCorrect: false,
      isShowClass: false,
      title: '请记忆下面出现的形状',
      answerStatus: 'demo', // demo -- 演示 answer -- 答题 judge -- 判定
      questionList: [],
      answerList: [],
      current: 0,

      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
      this.startProcess()
    },

    // 开始流程
    startProcess() {
      const list = [1, 2, 3, 4, 5, 6]
      this.questionList = api.getRandomArray(list, this.level + 1)
      this.answerList = api.getRandomArray(list.filter(item => !this.questionList.includes(item)), 2).concat(this.questionList)
      this.answerList = api.shuffle(this.answerList)
      this.number++
      setTimeout(() => {
        this.setClass()
      }, 500)
    },

    setClass() {
      if (this.index >= this.questionList.length) {
        this.answerStatus = 'answer'
        this.title = '这个形状在刚才的场景中出现过吗？'
        this.index = 0
        setTimeout(() => {
          this.setAnswer()
        }, 500)
        return
      }
      this.current = this.questionList[this.index]
      this.isShowClass = true
      this.index++
      setTimeout(() => {
        this.isShowClass = false
      }, 1500)
      setTimeout(() => {
        this.setClass()
      }, 2000)
    },

    setAnswer() {
      if (this.index >= this.answerList.length) {
        this.submit()
        return
      }
      this.current = this.answerList[this.index]
      this.isShowClass = true
      this.index++
    },

    handleClick(type) {
      this.answerStatus = 'judge'
      if (type) {
        if (this.questionList.includes(this.current)) {
          this.succesNum++
          this.isCorrect = true
        } else {
          this.errorNum++
          this.isCorrect = false
        }
      } else {
        if (!this.questionList.includes(this.current)) {
          this.succesNum++
          this.isCorrect = true
        } else {
          this.errorNum++
          this.isCorrect = false
        }
      }
      setTimeout(() => {
        this.answerStatus = 'answer'
        this.isShowClass = false
      }, 800)
      setTimeout(() => {
        this.setAnswer()
      }, 1300)
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isStop = true
      if (this.level === 1) {
        this.store = 25 * this.succesNum
      } else if (this.level === 2) {
        this.store = 20 * this.succesNum
      } else if (this.level === 3) {
        this.store = this.succesNum ? this.succesNum * 16 + 4 : 0
      }
			
      this.infos[0].value = this.level
			this.infos[1].value = this.second
			this.infos[2].value = this.succesNum
			this.infos[3].value = this.errorNum
			this.status = 4
			this.params = {
				id: this.info.id,
				grade: this.level,
				time: this.second,
				totalPoints: this.store
			}
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.isCorrect = false
      this.isShowClass = false
      this.title = '请记忆下面出现的形状'
      this.answerStatus = 'demo'
      this.current = 0
      this.index = 0
      this.status = 3
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game44-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game44/bg.png");
  }

  .game-synopsis {

    .synopsis-content {
      padding-top: 20px;
      margin: 0;
      font-size: 30px;
      line-height: 42px;
      font-weight: 400;
      color: #A83A01;
    }
  }

  .game-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .title {
      position: absolute;
      top: 100px;
      width: 852px;
      height: 222px;
      z-index: 1;

      .bg {
        position: absolute;
        left: 0;
        width: 852px;
        height: 118px;
      }

      .text {
        position: relative;
        padding: 17px 0 34px 0;
        font-size: 45px;
        line-height: 67px;
        text-align: center;
        color: #A83A01;
      }
    }

    .content {
      position: relative;
      width: 100%;
      height: 100%;
      // padding-bottom: 100px;
      display: flex;
      justify-content: center;
      align-items: center;

      .content-img {
        width: 588px;
        opacity: 0;
      }

      .item-opacity {
        opacity: 1;
        transition: opacity 0.8s;
      }
    }

    .footer {
      position: absolute;
      bottom: 82px;
      width: 1554px;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px;
        height: 84px;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px;
          height: 84px;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px 0 21px 0;
          font-size: 38px;
          line-height: 53px;
          text-align: center;
          color: #A83A01;
        }
      }

      .btn-group {
        width: 680px;
        display: flex;
        justify-content: space-between;
      }
    }

    .judge {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding-top: 10px;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      justify-content: center;
      align-items: center;

      .img {
        width: 460px;
        height: 460px;
      }
    }
  }
}
</style>
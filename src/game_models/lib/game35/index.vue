<template>
  <div class="game35-page">
    <div class="page-bg" :class="status > 1 ? 'page-bg2': 'page-bg1'"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <audio ref="music2" muted controls="controls" style="display:none" loop="loop">
      <source src="/static/game_assets/audio/dida.mp3" type="audio/mpeg" />
    </audio>
    <settingPage title="时钟转换--简单" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <div class="synopsis-content">
          <p>1、这是一项注意力保持的训练任务。</p>
          <p>2、要求您计数录音里钟表报时的次数，声音播放完毕时，录音中会有“几点”的提问，这是请回答几点钟了（即数了几次报时声音）。</p>
          <p>3、钟表运行时有“嗒嗒”的指针的声音，这是用来干扰您注意力的，注意不要计数这个声音。只数钟表报时声音-“铛”的声音</p>
          <p>4、任务中的报时声音数不会超过12，也不会低于1.</p>
          <p>5、分级：1-4点为初级，5-8点为中级，9-12点为高级。</p>
        </div>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="clock-content">
        <div class="clock" v-for="item in 3" :key="item">
          <img class="clock-shadow" src="/static/game_assets/game34/shadow.png" />
          <img class="clock-bg" src="/static/game_assets/game34/yellow_bg.png" />
          <img class="clock-point" src="/static/game_assets/game34/yellow_point.png" />
          <img class="hour-hand" src="/static/game_assets/game34/hour_hand.png" />
          <img class="minute-hand" src="/static/game_assets/game34/minute_hand.png" />
        </div>
      </div>

      <div class="keyboard" v-if="answerStatus === 'answer'">
        <div class="left">
          <img v-for="item in 10" :key="item" class="btn" :src="`/static/game_assets/game35/btn_${item}.png`" @click="select(item)" />
        </div>
        <div class="right">
          <div class="input">
            <img class="img" src="/static/game_assets/game35/input.png" />
            <img class="clear" src="/static/game_assets/game35/clear.png" @click="time = ''" />
            <span class="number">{{ time }}</span>
          </div>
          <img class="img" src="/static/game_assets/game35/confirm.png" @click="submit" />
        </div>
      </div>

      <div class="btn-group">
        <img class="left" src="/static/game_assets/game34/stop.png" @click="stop" />
        <div class="right">
          <img v-if="answerStatus === 'wait'" class="start" src="/static/game_assets/game34/start.png" @click="startProcess" />
          <img v-if="answerStatus === 'result'" class="reset" src="/static/game_assets/game34/reset.png" @click="reset" />
          <img v-if="answerStatus === 'result'" class="continue" src="/static/game_assets/game34/continue.png" @click="continueTrain" />
        </div>
      </div>
    </div>

    <maskPage v-if="answerStatus === 'judge'">
      <div class="mask-content">
        <img v-if="!isCorrect" class="img" src="/static/game_assets/game34/error.png" />
        <img v-else class="img" src="/static/game_assets/game34/success.png" />
      </div>
    </maskPage>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import maskPage from '../component/mask.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'
// 游戏分为3个等级，每轮游戏3个回合
// 初级：1-4点
// 中级：5-8点
// 高级：9-12点

export default {
  name: 'game35',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [maskPage.name]: maskPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data() {
    return {
      musicUrl: '',
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isStop: false,
      show: false,
      isPlay: false,
      isContinue: false,
      answerStatus: 'wait', // wait--等待状态，play--播报题目，answer--答题，judge--判定对错，result--结果
      randomNumber: 0, // 几点
      bellNum: 0, // 报时数量
      time: '',
      isCorrect: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    playAudio() {
      setTimeout(() => {
        this.answerStatus = 'answer'
        this.isStop = false
        this.timing()
      }, 3000)
    },

    play(url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$nextTick(() => {
        this.$refs.music.play()
        this.isPlay = true
      })
    },

    playBg() {
      this.$refs.music2.play()
    },

    pause() {
      this.$refs.music.pause()
      this.$refs.music2.pause()
      this.isPlay = false
    },

     handleEnd() {
       this.isPlay = false
      if (this.answerStatus !== 'play') return

      this.bellNum++
      if (this.bellNum < this.randomNumber) {
        this.play('/static/game_assets/audio/bell.mp3')
      } else {
        this.answerStatus = 'answer'
        this.pause()
        this.play('/static/game_assets/audio/game34/audio1.mp3')
      }
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
    },

    select(item) {
      if (this.time.length >= 2) return
      this.time = this.time + (item === 10 ? 0 : item).toString()
    },

    startProcess() {
      this.answerStatus = 'play'
      this.bellNum = 0
      this.randomNumber = api.randomNum((this.level - 1) * 4 + 1, this.level * 4)
      this.play('/static/game_assets/audio/bell.mp3')
      this.playBg()
    },

    submit() {
      if (!this.time) return
      this.isStop = true
      this.answerStatus = 'judge'
      this.isCorrect = (this.randomNumber === Number(this.time))
      if (this.isCorrect) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      setTimeout(() => {
        this.number++
        if (this.number >= 3) {
          this.answerStatus = 'wait'
          this.store = this.succesNum ? (this.succesNum * 30 + 10) : 0
          this.infos[0].value = this.level
          this.infos[1].value = this.second
          this.infos[2].value = this.succesNum
          this.infos[3].value = this.errorNum
          this.status = 4
          this.params = {
            id: this.info.id,
            grade: this.level,
            time: this.second,
            totalPoints: this.store
          }
        } else {
          this.answerStatus = 'result'
        }
      }, 1000)
    },

    stop() {
      this.isStop = true
      this.show = true
      if (this.isPlay) {
        this.pause()
        this.isContinue = true
      }
    },

    reset() {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.timeNum = 0
      this.answerStatus = 'wait'
      this.randomNumber = 0
      this.time = ''
    },

    // 继续游戏, 继续计时
    cancel() {
      if (this.answerStatus === 'answer') {
        this.isStop = false
        this.timing()
      }
      if (this.isContinue) {
        this.play()
        if (this.answerStatus === 'play') this.playBg()
        this.isContinue = false
      }
    },

    again() {
      this.isStop = false
      this.status = 3
      this.reset()
      this.timing()
    },

    continueTrain() {
      this.answerStatus = 'wait'
      this.randomNumber = 0
      this.time = ''
    }
  }
}
</script>

<style lang="scss">
.game35-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background-image: url("/static/game_assets/game34/bg_1.png");
  }
  .page-bg2 {
    background-image: url("/static/game_assets/game34/bg_2.png");
  }

  .game-synopsis {
    width: 1000px;
    height: 504px; 
    padding-left: 200px;  
    padding-top: 50px;   

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #333;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 25px;

      p {
        margin: 0;
        font-size: 25px;
        line-height: 40px;
        font-weight: 400;
        color: #333;
        user-select: none;
      }
    }
  }

  .game-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative;
    width: 100%;
    height: 100%;
    padding: 30px 0 15px 0;

    .clock-content {
      display: flex;
      justify-content: space-between;
      width: 1330px;

      .clock {
        position: relative;
        width: 373px;
        height: 455px;

        .clock-bg {
          position: absolute;
          top: 0;
          left: 20px;
          width: 332px;
          height: 455px;
        }

        .clock-shadow {
          position: absolute;
          left: 0;
          bottom: 0;
          width: 373px;
          height: 53px;
          opacity: 0.2;
        }

        .clock-point {
          position: absolute;
          top: 251px;
          left: 167px;
          width: 49px;
          height: 47px;
          z-index: 1;
        }

        .hour-hand {
          position: absolute;
          top: 264px;
          left: 185px;
          width: 95px;
          height: 58px;
          animation: rotate 120s infinite linear;
          transform-origin: 3px 5px;
        }

        .minute-hand {
          position: absolute;
          top: 174px;
          left: 143px;
          width: 55px;
          height: 110px;
          animation: rotate 20s infinite linear;
          transform-origin: 48px 100px;
        }
      }
    }

    .keyboard {
      display: flex;
      width: 1300px;
      padding: 15px 0 10px 0;

      .left {
        display: flex;
        flex-wrap: wrap;
        width: 975px;
        height: 282px;

        img {
          width: 180px;
          height: 127px;
          margin-right: 15px;
          margin-bottom: 13px;
          margin-top: 1px;
          cursor: pointer;
        }
      }

      .right {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 325px;
        height: 282px;
        padding-left: 12px;
        padding-bottom: 12px;

        .input {
          position: relative;
          width: 313px;
          height: 129px;

          .img {
            position: absolute;
            top: 0;
            left: 0;
            width: 313px;
            height: 129px;
          }

          .clear {
            position: absolute;
            top: 29px;
            right: 24px;
            width: 57px;
            height: 58px;
            cursor: pointer;
          }

          .number {
            position: absolute;
            top: 20px;
            left: 80px;
            width: 145px;
            height: 74px;
            font-size: 52px;
            line-height: 74px;
            text-align: center;
            font-family: Impact;
            color: #fff;
            user-select: none;
          }
        }

        .img {
          width: 313px;
          height: 129px;
          cursor: pointer;
        }
      }
    }

    .btn-group {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding: 0 123px 30px 123px;

      .left {
        width: 270px;
        height: 115px;
        cursor: pointer;
      }

      .right {
        .start, .continue, .reset {
          width: 268px; 
          height: 115px;
          margin-left: 40px;
          cursor: pointer;
        }

        .submit {
          width: 270px; 
          height: 115px;
          cursor: pointer;
        }
      }
    }
  }

  .mask-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;

    .img {
      width: 287px;
      height: 310px;
      margin-top: 320px;
    }
  }
}
@keyframes rotate {
  100% {
    transform: rotateZ(360deg);
  }
}
</style>
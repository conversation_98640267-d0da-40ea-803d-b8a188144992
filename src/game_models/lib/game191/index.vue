<template>
  <div class="game191-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPage title="减法命题" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、请根据提示，进行减法计算，选择对应的选项;</p>
        <p class="synopsis-content">2、训练结束会出现成绩单;</p>
        <p class="synopsis-content">3、本训练难度位B（普通）。</p>
      </div>
    </settingPage>

    <div class="game-content" v-if="status === 3">
      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>
      <div class="title">
        <img class="title-bg" src="/static/game_assets/game206/title_bg.png" />
        <span class="title-text">{{`哪个选项表示${currect.number1}-${currect.number2}=${currect.number3}?`}}</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game199/content_bg.png" />
        <img class="left-icon" src="/static/game_assets/game199/img.png" />
        <img class="right-icon" src="/static/game_assets/game196/img2.png" />

        <div class="content-item" v-for="item in currect.answerList" :key="item.index + 'item'" @click="chooseItem(item.index)">
          <img class="item-bg" src="/static/game_assets/game190/item_bg.png" />
          <img v-if="answer === item.index" class="item-bg" src="/static/game_assets/game190/item_bg_2.png" />
          <div class="item" v-for="it in item.number1" :key="it + 'img' + item.index">
            <img class="item-img" :src="`/static/game_assets/game199/item_${currect.imgIndex}.png`" />
            <img v-if="it > (item.number1 - item.number2)" class="item-icon" src="/static/game_assets/game199/icon.png" />
          </div>
        </div>

        <div class="content-img store">
          <img class="item-bg" src="/static/game_assets/game196/item_bg_1.png" />
          <span class="item-text">题目分数</span>
          <span class="item-num">{{store}}</span>
        </div>

        <div class="content-img number">
          <img class="item-bg" src="/static/game_assets/game196/item_bg_2.png" />
          <span class="item-text">题目数量</span>
          <span class="item-num">{{number}}</span>
        </div>

        <div class="content-img time">
          <img class="item-bg" src="/static/game_assets/game196/item_bg_1.png" />
          <span class="item-text">训练用时</span>
          <span class="item-time">{{time}}</span>
        </div>
      </div>

      <div class="footer">
        <img v-if="answer" class="img" src="/static/game_assets/common/confirm.png" @click="confirm" />
      </div>
    </div>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import api from '../../utils/common.js'

export default {
  name: 'game191',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
  },

  data() {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      level: 2,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isPlay: false,
      show: false,

      answer: 0,
      currect: {
        answer: 0,
        number1: 0,
        number2: 0,
        number3: 0,
        imgIndex: 0,
        answerList: []
      },  
      

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time() {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    // TODO: 返回首页
		goHome() {
      this.pause()
			this.$router.go(-1)
		},

    play() {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start() {
      this.level = this.info.level || 2
      this.status = 3
      // this.timing()
      this.startProcess()
      this.play()
    },

    startProcess() {
      const imgIndex = [1, 2, 3, 4, 5, 6, 7, 8]
      const num1 = api.randomNum(11, 20)
      const num2 = api.randomNum(11, 20)
      this.currect.imgIndex = api.getRandomArray(imgIndex, 1)[0]
      this.currect.number1 = Math.max(num1, num2)
      this.currect.number2 = Math.min(num1, num2)
      this.currect.number3 = this.currect.number1 - this.currect.number2
      this.currect.answerList = []
      while(this.currect.answerList.length < 3) {
        const num1 = api.randomNum(11, 20)
        const num2 = api.randomNum(11, 20)
        const number1 = Math.max(num1, num2)
        const number2 = Math.min(num1, num2)
        if (!(this.currect.number1 === number1 && this.currect.number2 === number2)) {
          this.currect.answerList.push({
            number1: number1,
            number2: number2,
            index: this.currect.answerList.length + 1
          })
        }
      }
      this.currect.answerList.push({
        number1: this.currect.number1,
        number2: this.currect.number2,
        index: this.currect.answerList.length + 1
      })
      this.currect.answer = 4
      this.currect.answerList = api.shuffle(this.currect.answerList)
      this.number++
    },

    chooseItem(item) {
      this.answer = item
    },

    confirm() {
      if (this.currect.answer === Number(this.answer)) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      this.store = 10 * this.succesNum
      if (this.number >= 10) {
        this.submit()
      } else {
        this.answer = 0
        this.startProcess()
      }
    },

    submit() {
      this.pause()
      this.isStop = true
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answer = 0
      this.currect = {
        answer: 0,
        number1: 0,
        number2: 0,
        number3: 0,
        imgIndex: 0,
        answerList: []
      },
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game191-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game126/bg.png");
  } 

  .game-synopsis {
    width: 1605px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 40px;
      left: 105px;
      width: 175px;
      display: flex;
      justify-content: space-between;

      .top-icon {
        width: 80px;
        cursor: pointer;
      }
    }

    .title {
      position: absolute;
      top: 0;
      width: 932px;
      height: 125px;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px;
        height: 125px;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 28px 0 56px 0;
        font-size: 36px;
        line-height: 36px;
        text-align: center;
        color: #1B1D2D;
      }
    }

    .content {
      position: relative;
      width: 1892px;
      height: 922px;
      margin-top: 154px;
      padding: 104px 410px 188px 155px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .content-bg {
        position: absolute;
        top: 0;
        left: 78px;
        width: 1479px;
      }

      .left-icon {
        position: absolute;
        bottom: 13px;
        left: 0;
        width: 362px;
      }

      .right-icon {
        position: absolute;
        bottom: 44px;
        right: 0;
        width: 541px;
      }

      .content-item {
        position: relative;
        width: 1243px;
        height: 145px;
        padding: 29px 15px;
        display: inline-flex;
        cursor: pointer;
        
        .item-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 1243px;
        }

        .item {
          position: relative;
          width: 56px;
          height: 86px;
          margin-right: 2px;

          .item-img {
            position: relative;
            width: 56px;
          }

          .item-icon {
            position: absolute;
            top: 15px;
            left: 2px;
            width: 46px;
          }
        }
      }

      .content-img {
        position: absolute;
        right: 123px;
        width: 369px;
        height: 219px;
        padding: 28px 42px;
        display: flex;

        .item-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 369px;
        }

        .item-text {
          position: relative;
          display: inline-block;
          width: 36px;
          font-size: 36px;
          line-height: 40px;
          text-align: center;
          font-weight: 500;
          color: #253000;
        }

        .item-num {
          position: relative;
          display: block;
          width: 100%;
          height: 100%;
          padding-left: 37px;
          font-size: 92px;
          line-height: 170px;
          text-align: center;
          color: #312B4F;
          font-family: Impact;
        }
        
        .item-time {
          position: relative;
          display: block;
          width: 100%;
          height: 100%;
          padding-left: 37px;
          font-size: 48px;
          line-height: 170px;
          text-align: center;
          color: #312B4F;
          font-family: Impact;
        }
      }

      .store {
        top: 63px;
      }

      .number {
        top: 290px;
      }

      .time {
        top: 518px;
      }
    }

    .footer {
      position: absolute;
      bottom: 72px;
      display: flex;
      justify-content: space-between;
      width: 1480px;

      .img {
        height: 115px;
        cursor: pointer;
      }
    }
  }
}
</style>
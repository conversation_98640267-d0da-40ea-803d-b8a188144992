<template>
  <div class="game64-page">
    <div class="page-bg"></div>
    <settingPage title="语言排序——连句成段" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">语言排序-连句成段</p>
        <div class="synopsis-content">
          <p>1、将呈现的句子按要求的正确顺序拖到相应位置</p>
        </div>
      </div>
    </settingPage>
    <div class="page-content" v-if="status == 3">
      <div class="answer">
        <div class="answer-container">
          <div class="question-name">把下面的话排好</div>
          <template v-for="item of question">
            <div class="_item" :key="item.id" :draggable="true" @dragstart="dragstart(item)"
              @dragenter="(e)=>{dragenter(item,e)}" @dragend="(e)=>{dragend(item,e)}" @dragover="(e)=>{dragover(e)}">
              {{item.label}}
            </div>
          </template>
          <div class="question-progress">{{unit}}/{{total}}</div>
        </div>
      </div>
      <div class="_actions">
        <img src="/static/game_assets/game64/icon2.png" class="btn" alt="" @click="stop">
        <img src="/static/game_assets/game64/icon3.png" class="btn" alt="" @click="next">
      </div>
    </div>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'
export default {
  name: 'game64',
  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm
  },
  data () {
    return {
      formData: {
        question: {
          1: [
            { id: "1", label: "银杏树又叫白果树。" },
            { id: "2", label: "它的树干又粗又高,枝叶特别茂盛。" },
            { id: "3", label: "这一片片叶子像精美的小纸扇，又像漂亮的蝴蝶翅膀。" }
          ],
          2: [
            { id: "1", label: "1.银杏树又叫白果树。" },
            { id: "2", label: "3.这一片片叶子像精美的小纸扇，又像漂亮的蝴蝶翅膀。" },
            { id: "3", label: "2.它的树干又粗又高,枝叶特别茂盛。" }
          ]
        },
        answer: {
          1: "1,2,3",
          2: "1,3,2"
        },
      },
      total: 0,
      status: 1,
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ],
      level: 1,
      unit: 1,
      question: [],
      oldData: {},
      newData: {},
      answer: null,
      number: 0, //得分
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      params: {},
      isStop: false,
      show: false,
    }
  },

  mounted() {
    this.timing()
  },
  
  methods: {
    timing () {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },
    start () {
      this.level = Number(this.info.level) || 1
      this.startProcess()
      this.status = 3
      // this.timing()

    },
    startProcess () {
      const { question, answer } = this.formData
      let unit = this.unit
      this.question = question[unit]
      this.answer = answer[unit]
      this.total = Object.keys(question).length
    },
    dragstart (item) {
      // 开始拖拽
      this.oldData = item
    },
    // 记录移动过程中信息
    dragenter (item, e) {
      this.newData = item
      console.log(item, e)
    },
    // 拖拽最终操作
    dragend (item, e) {
      if (this.oldData !== this.newData) {
        let oldIndex = this.question.indexOf(this.oldData)
        let newIndex = this.question.indexOf(this.newData)
        let newItems = [...this.question]
        // 删除老的节点
        newItems.splice(oldIndex, 1)
        // 在列表中目标位置增加新的节点
        newItems.splice(newIndex, 0, this.oldData)
        this.question = [...newItems]

      }
    },
    dragover (e) {
      e.preventDefault()
    },
    next () { // 完成
      let _str = ""
      let _i = 0
      for (let item of this.question) {
        if (_i == this.question.length - 1) {
          _str += item.id
        } else {
          _str += item.id + ','
        }
        _i++
      }
      let unitPoint = Math.floor(100 / this.total)

      if (_str == this.answer) {
        this.number += (unitPoint - 0)
        this.succesNum++
      } else {
        this.errorNum++
      }
      if (this.unit < this.total) {
        this.unit++
        this.startProcess()
      } else {
        this.submit()
      }
    },
    submit () {
      if (this.succesNum == this.total) {
        this.number = 100
      }
      this.isStop = true
      this.store = this.number
      this.infos[0].value = this.level
      this.infos[1].value = api.getTime(this.second)
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },
    again () {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.unit = 1
      this.answer = null
      this.question = []
      this.isStop = false
      this.start()
      this.timing()
    },
    stop () {
      this.isStop = true
      this.show = true
    },
    // 继续游戏, 继续计时
    cancel () {
      this.isStop = false
      this.timing()
    }
  },
}
</script>

<style lang="scss">
.game64-page {
  width: 100%;
  height: 100%;
  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('/static/game_assets/game64/bg.png');
  }
  .game-synopsis {
    width: 707px;
    background: #fffef3;
    border-radius: 36px;
    border: 2px solid #014747;
    display: flex;
    flex-direction: column;
    padding: 33px 31px;

    .synopsis-title {
      margin: 0;
      font-size: 2rem;
      line-height: 3rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #014747;
    }

    .synopsis-content {
      padding-top: 25px;

      p {
        margin: 0;
        font-size: 2rem;
        line-height: 3rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #014747;
      }
    }
  }
  .page-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    .answer {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 1300px;
      max-height: 70%;
      &-container {
        width: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;

        .question-name {
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #014747;
          font-size: 3rem;
          line-height: 3rem;
          padding: 90px 0;
        }
        ._item {
          width: 100%;
          background-image: url('/static/game_assets/game64/icon1.png');
          background-size: 100% 100%;
          margin-bottom: 28px;
          padding: 33px 61px 58px;
          font-size: 2rem;
          line-height: 3rem;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #643f0c;
        }
        .question-progress {
          font-size: 2rem;
          line-height: 3rem;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #10623f;
          padding: 15px 41px;
          background: rgba(60, 178, 149, 0.51);
          border-radius: 38px;
          border: 1px solid #37b982;
        }
      }
    }
    ._actions {
      width: 1300px;
      position: fixed;
      bottom: 64px;
      display: flex;
      justify-content: space-between;
      .btn {
        width: 268px;
        height: 115px;
        margin-right: 28px;
        &:last-child {
          margin-right: 0;
        }
      }
      .stop-btn {
        width: 268px;
        height: 115px;
      }
    }
  }
}
</style>

<template>
  <div class="game183-page">
    <div class="page-bg"></div>
    <settingPage title="朗读数字" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、请您大声朗读屏幕中央的阿拉伯数字。</p>
        <p class="synopsis-content">2、随着数字表达转换能力的提高，训练难度将不断提高。</p>
        <p class="synopsis-content">3、该题共有五个等级，每4道题为一等级，4道题全答对将升级，正确不足2道题将降级。</p>
        <p class="synopsis-content">4、同一等级未做完4道题就停止训练时，不记录成绩。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="content">{{currect}}</div>

      <div class="footer">
        <img class="img" src="/static/game_assets/common/stop.png" @click="stop" >

        <img v-if="isShowCorrect || isShowError" class="img" src="/static/game_assets/common/next.png" @click="toNext">
        <div class="footer-right" v-else>
          <img class="img" src="/static/game_assets/game59/correct.png" @click="handleClick(true)" />
          <img class="img" src="/static/game_assets/game59/error.png" @click="handleClick(false)" />
        </div>
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 20个题 5个等级
// 1级 0-10
// 2级 11-20
// 3级 21-100
// 4级 101-500
// 5级 501-1000
 
export default {
  name: 'game183',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      level: 1,
      status: 1,
      show: false,
      isStop: false,
      isShowCorrect: false,
      isShowError: false,
      
      currect: 0,
      index: 0, // 连续正确数

      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.startProcess()
      this.status = 3
      // this.timing()
    },

    startProcess() {
      let num = 0
      if (this.level === 1) {
        num = api.randomNum(0, 10)
      } else if (this.level === 2) {
        num = api.randomNum(11, 20)
      } else if (this.level === 3) {
        num = api.randomNum(21, 100)
      } else if (this.level === 4) {
        num = api.randomNum(101, 500)
      } else {
        num = api.randomNum(501, 1000)
      }

      if (num === this.currect) {
        this.currect = num + 1
      } else {
        this.currect = num
      }
    },

    handleClick(flag) {
      if (flag) {
        this.succesNum++
        this.isShowCorrect = true
        if(this.index >= 0) {
          this.index++
        } else {
          this.index = 1
        }
      } else {
        this.errorNum++
        this.isShowError = true
        if(this.index > 0) {
          this.index = -1
        } else {
          this.index--
        }
      }
    },

    toNext() {
      this.number++
      this.isShowCorrect = false
      this.isShowError = false
      if (this.index >= 4 && this.level < 5) {
        this.level++
        this.index = 0
      } else if (this.index <= -2 && this.level > 1) {
        this.level--
        this.index = 0
      }

      if (this.number < 20) {
        this.startProcess()
      } else {
        this.isStop = true
        this.store = 5 * this.succesNum
        this.infos[0].value = this.level
        this.infos[1].value = this.second
        this.infos[2].value = this.succesNum
        this.infos[3].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: this.level,
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.isStop = false
      this.store = 0
      this.index = 0
      this.level = Number(this.info.level) || 1
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game183-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game183/bg.png");
  }

  .game-synopsis {
    width: 857px;
    height: 544px;
    margin: 34px;
    padding: 33px 30px;
    background: #fff;
    border-radius: 36px;

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #5E381F;
    }

    .synopsis-content {
      padding-top: 18px;
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 400;
      color: #5E381F;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      width: 100%;
      height: 300px;
      font-size: 211px;
      line-height: 300px;
      text-align: center;
      font-weight: 500;
      color: #000;
      font-family: PingFangSC-Medium, PingFang SC;
    }

    .footer {
      position: absolute;
      bottom: 88px;
      display: flex;
      justify-content: space-between;
      width: 1620px;

      .footer-right {
        width: 576px;
        display: flex;
        justify-content: space-between;
      }

      .img {
        height: 115px;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px;
      margin-bottom: 40px;
      z-index: 99;
    }
  }
}
</style>

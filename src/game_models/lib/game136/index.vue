<template>
  <div class="game136-page">
    <div class="page-bg"></div>
    <settingPage title="选择计算" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、本项目考察计算能力和心算能力。</p>
        <p class="synopsis-content">2、题型是选择题。</p>
        <p class="synopsis-content">3、随着计算能力的提高，题目的难度程度将会提升。</p>

        <p class="synopsis-title">训练方式</p>
        <div class="synopsis-choose">
          <div class="item">
            <div class="item-icon" @click="setChoose(1)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 1" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">加法题</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(2)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 2" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">减法题</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(3)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 3" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">乘法题</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(4)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 4" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">除法题</span>
          </div>
        </div>
      </div>
    </settingPage>

    <div class="game-content" v-if="status === 3">
      <div class="content">
        <div class="content-title">
          <img class="title-bg" src="/static/game_assets/game136/title_bg.png" />
          <span class="title-text">选择计算-{{type === 1 ? '加法' : type === 2 ? '减法' : type === 3 ? '乘法' : '除法'}}</span>
        </div>

        <div class="content-main">
          <div class="num-item">
            <img class="num-bg" src="/static/game_assets/game136/btn_bg_1.png" />
            <span class="num">{{currect.num1}}</span>
          </div>

          <p class="symbol">{{type === 1 ? '+' : type === 2 ? '-' : type === 3 ? '×' : '÷'}}</p>

          <div class="num-item">
            <img class="num-bg" src="/static/game_assets/game136/btn_bg_1.png" />
            <span class="num">{{currect.num2}}</span>
          </div>

          <p class="symbol">=</p>

          <div class="num-item">
            <img class="num-bg" src="/static/game_assets/game136/btn_bg_1.png" />
            <span :class="[answer !== null ? 'num' : 'symbol']">{{answer !== null ? answer : '?'}}</span>
          </div>
        </div>

        <div class="content-answer">
          <div class="answer-item" v-for="item in currect.answerList" :key="item + 'item'" @click="chooseItem(item)">
            <img class="num-bg" src="/static/game_assets/game136/btn_bg_1.png" />
            <img v-if="answer === item" class="num-bg" src="/static/game_assets/game136/btn_bg_3.png" />
            <span :class="['num', answer === item && 'choose-num']">{{item}}</span>
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="footer-left">
          <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
          <img v-show="showNext" class="img" src="/static/game_assets/game136/question.png" @click="toNext" />
          <img v-show="answer !== null && !showNext" class="img" src="/static/game_assets/common/confirm.png" @click="confirm" />
        </div>

        <div class="footer-right">
          <p class="item">等级：{{level}}</p>
          <p class="item">正确：{{succesNum}}</p>
          <p class="item">错误：{{errorNum}}</p>
        </div>
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 10道题 3个等级
// 1级 加法：0-10 0-10 减法：0-10 0-10 乘法：0-10 0-10 除法：0-10 0-10
// 2级 加法：0-20 0-20 减法：0-20 0-20 乘法：0-10 0-20 除法：0-20 0-10
// 3级 加法：10-50 10-50 减法：10-50 10-50 乘法：0-20 0-20 除法：0-50 0-20
// 连续答对三题则升一级
 
export default {
  name: 'game136',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      level: 1,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      showNext: false,
      isCanClick: true,

      type: 1, // 训练模式
      correctNum: 0,
      answer: null,
      currect: {
        num1: 0,
        num2: 0,
        answer: 0,
        answerList: []
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess() {
      this.isCanClick = true
      this.currect.answerList = []
      if (this.type === 1) {
        if (this.level === 1) {
          this.currect.num1 = api.randomNum(0, 10)
          this.currect.num2 = api.randomNum(0, 10)
        } else if (this.level === 2) {
          this.currect.num1 = api.randomNum(0, 20)
          this.currect.num2 = api.randomNum(0, 20)
        } else {
          this.currect.num1 = api.randomNum(11, 49)
          this.currect.num2 = api.randomNum(11, 49)
        }

        this.currect.answer = this.currect.num1 + this.currect.num2
      } else if (this.type === 2) {
        if (this.level === 1) {
          const num1 = api.randomNum(0, 10)
          const num2 = api.randomNum(0, 10)
          this.currect.num1 = num1 > num2 ? num1 : num2
          this.currect.num2 = num1 <= num2 ? num1 : num2
        } else if (this.level === 2) {
          const num1 = api.randomNum(0, 20)
          const num2 = api.randomNum(0, 20)
          this.currect.num1 = num1 > num2 ? num1 : num2
          this.currect.num2 = num1 <= num2 ? num1 : num2
        } else {
          const num1 = api.randomNum(11, 50)
          const num2 = api.randomNum(11, 50)
          this.currect.num1 = num1 > num2 ? num1 : num2
          this.currect.num2 = num1 <= num2 ? num1 : num2
        }

        this.currect.answer = this.currect.num1 - this.currect.num2
      } else if (this.type === 3) {
        if (this.level === 1) {
          this.currect.num1 = api.randomNum(0, 10)
          this.currect.num2 = api.randomNum(0, 10)
        } else if (this.level === 2) {
          this.currect.num1 = api.randomNum(0, 10)
          this.currect.num2 = api.randomNum(0, 20)
        } else {
          this.currect.num1 = api.randomNum(0, 20)
          this.currect.num2 = api.randomNum(0, 20)
        }

        this.currect.answer = this.currect.num1 * this.currect.num2
      } else {
        if (this.level === 1) {
          const num1 = api.randomNum(1, 5)
          const num2 = api.randomNum(1, 3)
          this.currect.num1 = num1 * num2
          this.currect.num2 = num1
        } else if (this.level === 2) {
          const num1 = api.randomNum(1, 7)
          const num2 = api.randomNum(2, 5)
          this.currect.num1 = num1 * num2
          this.currect.num2 = num2
        } else {
          const num1 = api.randomNum(1, 10)
          const num2 = api.randomNum(1, 10)
          this.currect.num1 = num1 * num2
          this.currect.num2 = num1
        }

        this.currect.answer = this.currect.num1 / this.currect.num2
      }

      this.currect.answerList.push(this.currect.answer)
      while(this.currect.answerList.length < 4) {
        const num = (this.type === 1 || this.type === 3) ? api.randomNum(0, 99) : this.type === 2 ? api.randomNum(0, 50) : api.randomNum(0, 15)
        if (!this.currect.answerList.includes(num)) {
          this.currect.answerList.push(num)
        }
      }
      this.currect.answerList = api.shuffle(this.currect.answerList)
    },

    setChoose(item) {
      this.type = item
    },

    chooseItem(item) {
      if (!this.isCanClick) return
      this.answer = item
    },

    toNext() {
      this.number++
      this.answer = null
      this.showNext = false
      if (this.correctNum >= 3 && this.level < 3) {
        this.level++
        this.correctNum = 0
      }
      this.startProcess()
    },

    confirm() {
      this.isCanClick = false
      if (this.answer === this.currect.answer) {
        this.succesNum++
        this.correctNum++
      } else {
        this.errorNum++
        this.correctNum = 0
      }
      
      if (this.number >= 9) {
        this.submit()
      }

      this.showNext = true
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isStop = true
      this.store = this.succesNum * 10
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    finish() {
      if (this.province.isCorrect) this.succesNum++
      if (this.city.isCorrect) this.succesNum++
      if (this.floor.isCorrect) this.succesNum++
      if (this.floorNum.isCorrect) this.succesNum++
      if (this.ward.isCorrect) this.succesNum++

      this.province.value = undefined
      this.province.isCorrect = null
      this.city.value = undefined
      this.city.isCorrect = null
      this.floor.value = undefined
      this.floor.isCorrect = null
      this.floorNum.value = undefined
      this.floorNum.isCorrect = null
      this.ward.value = undefined
      this.ward.isCorrect = null
      this.isShowJudge = false
      this.isShowSubmit = false
      this.isShowFinish = false

      if (this.number === 1) {
        this.title = '现在住址'
      } else if (this.number === 2) {
        this.title = '住院病房位置'
      } else {
        this.isStop = true
        this.errorNum = 7 - this.succesNum
        this.store = this.succesNum > 6 ? 100 : this.succesNum * 15
        this.infos[0].value = this.second
        this.infos[1].value = this.succesNum
        this.infos[2].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: this.level,
          time: this.second,
          totalPoints: this.store
        }
      }
      this.startProcess()
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.showNext = false
      this.correctNum = 0
      this.answer = null
      this.currect = {
        num1: 0,
        num2: 0,
        answer: 0,
        answerList: []
      },
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game136-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
     background-image: url("/static/game_assets/game136/bg.png");
  }

  .game-synopsis {
    width: 860px;
    height: 600px;
    margin: 34px;
    padding: 33px 30px;
    background: #FFFEF3;
    border-radius: 36px;

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #5E381F;
      padding-bottom: 18px;
    }

    .synopsis-content {
      padding-bottom: 18px;
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 400;
      color: #5E381F;
    }

    .synopsis-choose {
      display: flex;
      flex-wrap: wrap;
      width: 550px;

      .item {
        position: relative;
        display: inline-flex;
        align-items: center;
        padding-right: 25px;
        padding-bottom: 16px;

        .item-icon {
          position: relative;
          width: 41px;
          height: 35px;
          display: inline-flex;
          justify-content: center;
          cursor: pointer;

          .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 41px;
          }

          .icon1 {
            position: absolute;
            width: 41px;
          }
        }

        .item-text {
          display: inline-block;
          padding-left: 13px;
          padding-top: 10px;
          font-size: 36px;
          line-height: 50px;
          font-weight: 600;
          color: #5E381F;
        }
      }
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1024px;
      height: 700px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;

      .content-title {
        position: relative;
        width: 864px;
        height: 192px;

        .title-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 864px;
        }

        .title-text {
          position: relative;
          display: block;
          font-size: 52px;
          line-height: 192px;
          text-align: center;
          font-weight: 500;
          color: #5E381F;
        }
      }

      .content-main {
        width: 100%;
        padding: 0 100px;
        display: flex;
        justify-content: space-between;

        .num-item {
          position: relative;
          width: 178px;
          height: 178px;
          padding-bottom: 18px;

          .num-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 178px;
          }

          .num {
            position: relative;
            display: block;
            font-size: 78px;
            line-height: 160px;
            text-align: center;
            font-family: Impact;
            color: #F48B4D;
          }

          .symbol {
            position: relative;
            display: block;
            font-size: 78px;
            line-height: 160px;
            text-align: center;
            font-weight: 600;
            color: #F48B4D;
          }
        }

        .symbol {
          display: inline-block;
          margin: 0;
          font-size: 78px;
          line-height: 178px;
          font-weight: 600;
          color: #F48B4D;
        }
      }

      .content-answer {
        width: 100%;
        display: flex;
        justify-content: space-between;

        .answer-item {
          position: relative;
          width: 178px;
          height: 178px;
          padding-bottom: 18px;
          cursor: pointer;

          .num-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 178px;
          }

          .num {
            position: relative;
            display: block;
            font-size: 78px;
            line-height: 160px;
            text-align: center;
            font-family: Impact;
            color: #F48B4D;
          }

          .choose-num {
            color: #fff;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 47px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      width: 1738px;
      margin-left: 112px;

      .footer-left {
        width: 620px;
        display: inline-flex;
        justify-content: space-between;

        .img {
          height: 115px;
          cursor: pointer;
        }
      }

      .footer-right {
        width: 570px;
        display: inline-flex;
        justify-content: space-between;

        .item {
          margin: 0;
          width: 170px;
          height: 76px;
          border-radius: 4px;
          border: 1px solid #fff;
          background: #E2ECFF;

          font-size: 24px;
          text-align: center;
          line-height: 74px;
          color: #5F6FD7;
        }
      }
    }
  }
}

.drop-item {
  background: #F3F2F8;
  
  .ant-select-dropdown-menu-item {
    padding: 0 30px;
    font-size: 50px;
    line-height: 80px;
    color: #5D5E66;
  }

  .ant-select-dropdown-menu {
    padding: 0
  }

  .ant-select-dropdown-menu-item-selected {
    background: #9BAA26;
    color: #fff;
    border-radius: 8px;
  }
}
</style>
<template>
  <div class="game211-page">
    <div class="page-bg"></div>
    <audio ref="music2" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <settingPage title="乘法规则" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">本项目考察乘法规则。</p>
      </div>
    </settingPage>

    <div class="game-content" v-if="status === 3">
      <div class="title">
        <img class="title-bg" src="/static/game_assets/game206/title_bg.png" />
        <span class="title-text">乘法规则</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game211/content_bg.png" />
        <img class="content-icon" src="/static/game_assets/game211/icon.png" @click="handleShowInfo" />

        <template v-if="!showInfo">
          <div class="content-title">
            <span>请输入所有时钟的总数</span>
            <div class="title-answer">
              <img class="answer-bg" src="/static/game_assets/game211/answer_bg.png" />
              <span>{{answer}}</span>
            </div>
            <span>个</span>
          </div>

          <div class="content-top">
            <div class="num-item" v-for="item in currect.num1" :key="item + 'item'">
              <img class="num-bg" src="/static/game_assets/game209/text_bg.png" />
              <img class="num-img" v-for="item in currect.num2" :key="item + 'img'" src="/static/game_assets/game209/item_1.png" />
            </div>
          </div>

          <div class="content-bottom" v-if="!isShowCorrect && !isShowError">
            <div class="bottom-left">
              <div class="left-item" v-for="item in 10" :key="item + 'btn'" @click="chooseItem(item)">
                <img class="item-bg" src="/static/game_assets/game209/btn_bg_1.png" />
                <span class="item-num">{{item % 10}}</span>
              </div>
            </div>

            <div class="bottom-right">
              <div class="right-item item1">
                <img class="item-bg" src="/static/game_assets/game209/btn_bg_2.png" />
                <span class="item-num">{{answer}}</span>
              </div>

              <div class="right-item item2" @click="answer = ''">
                <img class="item-bg" src="/static/game_assets/game209/btn_bg_3.png" />
                <span class="item-num">X</span>
              </div>

              <div class="right-item item3" @click="confirm">
                <img class="item-bg" src="/static/game_assets/game209/btn_bg_4.png" />
                <span class="item-num">确定</span>
              </div>
            </div>
          </div>
        </template>

        <img v-show="showInfo" class="content-img" src="/static/game_assets/game211/content.png" />
      </div>

      <div class="footer">
        <div class="footer-left">
          <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
          <img v-if="isShowCorrect || isShowError" class="img" src="/static/game_assets/common/next.png" @click="toNext">
          <!-- 返回按钮 -->
          <img v-if="showInfo" class="img" src="/static/game_assets/game211/back.png" @click="showInfo = false" />
        </div>

        <img v-if="isShowCorrect || isShowError" class="img" src="/static/game_assets/common/reset.png" @click="reset">
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 10道题 

export default {
  name: 'game211',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      showInfo: false,
      isShowCorrect: false,
      isShowError: false,

      answer: '',
      currect: {
        num1: 0,
        num2: 0,
        answer: 0,
      },  

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    playError() {
      this.$refs.music2.play()
    },

    start() {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess() {
      this.currect.num1 = api.randomNum(1, 6)
      this.currect.num2 = api.randomNum(1, 9)
      this.currect.answer = this.currect.num1 * this.currect.num2
    },

    handleShowInfo() {
      if (this.isShowCorrect) {
        this.succesNum--
      } else {
        this.errorNum--
      }
      this.isShowCorrect = false
      this.isShowError = false
      this.showInfo = true
    },

    chooseItem(item) {
      if (this.answer.length >= 2) return
      this.answer = this.answer + (item === 10 ? 0 : item).toString()
    },

    confirm() {
      if (!this.answer) {
        this.playError()
        return
      }
      if (Number(this.answer) === this.currect.answer) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }
    },

    reset() {
      if (this.isShowCorrect) {
        this.succesNum--
      } else {
        this.errorNum--
      }
      this.isShowCorrect = false
      this.isShowError = false
      this.answer = ''
    },

    toNext() {
      this.isShowCorrect = false
      this.isShowError = false
      this.answer = ''
      this.number++
      if (this.number >= 10) {
        this.submit()
      } else {
        this.startProcess()
      }
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isStop = true
      this.store = this.succesNum * 10
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answer = ''
      this.currect = {
        num1: 0,
        num2: 0,
        answer: 0,
      },
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game211-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game126/bg.png");
  } 

  .game-synopsis {
    width: 1605px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .title {
      position: absolute;
      top: 0;
      width: 932px;
      height: 125px;
      z-index: 1;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px;
        height: 125px;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 28px 0 56px 0;
        font-size: 36px;
        line-height: 36px;
        text-align: center;
        color: #1B1D2D;
      }
    }

    .content {
      position: relative;
      width: 1914px;
      height: 977px;
      margin-top: 50px;
      padding: 217px 234px 181px 253px;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1914px;
      }

      .content-icon {
        position: absolute;
        top: 22px;
        right: 98px;
        width: 171px;
        cursor: pointer;
      }

      .content-img {
        position: absolute;
        top: 72px;
        left: 357px;
        width: 1236px;
      }

      .content-title {
        position: absolute;
        top: 105px;
        left: 700px;
        display: flex;
        align-items: center;
        
        span {
          font-size: 30px;
          line-height: 30px;
          text-align: center;
          color: #312B4F;
        }

        .title-answer {
          position: relative;
          width: 84px;
          height: 58px;

          .answer-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 84px;
          }

          span {
            position: relative;
            display: block;
            font-size: 42px;
            line-height: 58px;
            text-align: center;
            font-weight: 600;
            color: #2B4FF5;
          }
        }
      }

      .content-top {
        position: relative;
        display: flex;
        justify-content: center;

        .num-item {
          position: relative;
          width: 224px;
          height: 245px;
          padding: 10px 20px;
          margin-left: 17px;
          display: flex;
          flex-wrap: wrap;
          align-content: flex-start;

          .num-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 224px;
            height: 245px;
          }

          .num-img {
            position: relative;
            width: 60px;
            height: 60px;
          }
        }

        .right-item {
          width: 188px;
          height: 206px;
          margin-top: 50px;
        }

        .symbol1 {
          margin-top: 102px;
          width: 62px;
          height: 63px;
        }

        .symbol2 {
          margin-top: 120px;
          width: 63px;
          height: 33px;
        }
      }

      .content-bottom {
        position: relative;
        width: 100%;
        padding-top: 70px;
        padding-left: 223px;
        display: flex;

        .bottom-left {
          width: 557px;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;
          margin-right: -10px;

          .left-item {
            position: relative;
            width: 135px;
            height: 136px;
            margin: -12px -15px;
            cursor: pointer;

            .item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 135px;
            }

            .item-num {
              position: relative;
              display: block;
              font-size: 48px;
              line-height: 136px;
              text-align: center;
              color: #312B4F;
            }
          }
        }

        .bottom-right {
          width: 450px;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;

          .right-item {
            position: relative;

            .item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%
            }

            .item-num {
              position: relative;
              display: block;
              font-size: 48px;
              line-height: 136px;
              text-align: center;
              color: #312B4F;
            }
          }

          .item1 {
            width: 291px;
            height: 136px;
            margin: -12px -12px -12px 0;
          }

          .item2 {
            width: 190px;
            height: 136px;
            margin: -12px;
            cursor: pointer;
          }

          .item3 {
            width: 462px;
            height: 136px;
            margin: -12px -12px -12px 0;
            cursor: pointer;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 72px;
      display: flex;
      justify-content: space-between;
      width: 1480px;

      .footer-left {
        width: 620px;
        display: inline-flex;
        justify-content: space-between;
      }

      .img {
        height: 115px;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px;
      margin-bottom: 40px;
    }
  }
}
</style>
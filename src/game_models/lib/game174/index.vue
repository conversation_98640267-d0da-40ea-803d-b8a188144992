<template>
  <div class="game174-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPage title="数字数量" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、请根据提示选择正确的选项；</p>
        <p class="synopsis-content">2、训练结束后会出现成绩单；</p>
        <p class="synopsis-content">3、本训练难度为B（普通）。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />
        
        <div class="top-text">比较下面数字的大小</div>
      </div>

      <div class="content">
        <div class="content-item">
          <img class="item-bg" src="/static/game_assets/game173/content_bg.png" />
          <span class="item-text">{{currect.question[0]}}</span>
        </div>

        <div :class="['content-symbol', isJudge && 'symbol-center']">
          <template v-if="!isJudge">
            <img class="symbol-img" v-for="item in 3" :key="item + 'img3'" :src="`/static/game_assets/game164/symbol_${item}.png`" @click="chooseItem(item)" />
          </template>
          <img v-else class="symbol-img" :src="`/static/game_assets/game164/symbol_${answer}.png`" />
        </div>

        <div class="content-item">
          <img class="item-bg" src="/static/game_assets/game173/content_bg.png" />
          <span class="item-text">{{currect.question[1]}}</span>
        </div>
      </div>

      <div class="footer">
        <img class="img" :style="{'opacity': answer ? 1 : 0}" src="/static/game_assets/common/submit.png" @click="submit">

        <div class="footer-right">
          <p class="item">题目分数：{{store}}</p>
          <p class="item">题目数量：{{number}}</p>
          <p class="item">用时：{{time}}</p>
        </div>
      </div>
    </div>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import api from '../../utils/common.js'
 
export default {
  name: 'game174',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    draggable
  },

  data() {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      level: 2,
      status: 1,
      isPlay: false,
      isStop: false,
      isJudge: false,

      answer: 0,
      currect: {
        question: [],
        answer: 0
      },

      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time() {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play() {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start() {
      this.level = this.info.level || 2
      this.startProcess()
      this.status = 3
      // this.timing()
      this.play()
    },

    startProcess() {
      this.currect.question = []
      while (this.currect.question.length < 2) {
        const num = api.randomNum(11, 20)
        this.currect.question.push(num)
      }

      if (this.currect.question[0] > this.currect.question[1]) {
        this.currect.answer = 1
      } else if (this.currect.question[0] < this.currect.question[1]) {
        this.currect.answer = 2
      } else {
        this.currect.answer = 3
      }
      this.number++
    },

    chooseItem(item) {
      this.answer = item
      this.isJudge = true
    },

    submit() {
      if (!this.answer) return
      this.isJudge = false
      if (this.answer === this.currect.answer) {
        this.succesNum++
      } else {
        this.errorNum++
      }
      this.store = 10 * this.succesNum

      if (this.number < 10) {
        this.answer = 0
        this.startProcess()
      } else {
        this.pause()
        this.isStop = true
        this.infos[0].value = this.level
        this.infos[1].value = this.second
        this.infos[2].value = this.succesNum
        this.infos[3].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: this.level,
          time: this.second,
          totalPoints: this.store
        }
      }
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.isStop = false
      this.store = 0
      this.answer = 0
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game174-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game173/bg.png");
  }

  .game-synopsis {
    width: 707px;
    height: 444px;
    margin: 34px;
    padding: 33px 30px;
    background: #fff;
    border: 2px solid #58AD49;
    border-radius: 36px;

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #5E381F;
    }

    .synopsis-content {
      padding-top: 18px;
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 400;
      color: #5E381F;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
			position: absolute;
			top: 40px;
      left: 0;
      width: 100%;
      padding: 0 54px;

			.top-icon {
        position: absolute;
        top: 0;
        left: 50px;
				width: 80px;
				cursor: pointer;
        z-index: 1;
			}

      .top-text {
        position: relative;
        font-size: 50px;
        line-height: 82px;
        text-align: center;
        font-weight: 600;
        color: #0B4F5D;
      }
		}

    .content {
      position: relative;
      width: 1520px;
      height: 645px;
      margin-bottom: 30px;
      display: flex;
      justify-content: space-between;
      align-content: center;

      .content-item {
        position: relative;
        width: 556px;
        height: 614px;

        .item-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 556px;
          height: 614px;
        }

        .item-text {
          position: relative;
          display: block;
          font-size: 211px;
          line-height: 614px;
          text-align: center;
          font-weight: 500;
          color: #D78855;
        }
      }

      .content-symbol {
        width: 178px;
        height: 645;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .symbol-img {
          width: 178px;
          height: 178px;
          cursor: pointer;
        }
      }

      .symbol-center {
        justify-content: center;
      }
    }

    .footer {
      position: absolute;
      bottom: 88px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      width: 1620px;

      .img {
        height: 115px;
        cursor: pointer;
      }

      .footer-right {
        width: 690px;
        display: inline-flex;
        justify-content: space-between;

        .item {
          margin: 0;
          width: 210px;
          height: 76px;
          border-radius: 4px;
          border: 1px solid #53872A; 
          background: #B7EBA4;

          font-size: 24px;
          text-align: center;
          line-height: 74px;
          color: #53872A;
        }
      }
    }
  }
}
</style>
<template>
  <div class="game39-page">
    <audio ref="music" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <div class="page-bg"></div>
    <settingPage @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、本组训练分为“记忆”和“回忆”两部分。</p>
        <p class="synopsis-content">2、记忆阶段，您将看到有很多小方块，其中有几个小方块按顺序在很短的时间内改变颜色，请尽快完全记住这几个改变过颜色的小方块的位置和变色顺序。</p>
        <p class="synopsis-content">3、回忆阶段，请您按顺序点击在记忆阶段曾经改变过的颜色的小方块。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="content1" v-if="number < 3">
        <div class="content-item" v-for="item in 4" :key="item">
          <div :class="['item1', itemClass(item)]" @click="chooseItem(item)"></div>
          <div :class="['item2', errorIndex ? (errorIndex === item ? 'red-item' : (answerArr.includes(item) ? 'black-item' : '')) : '', itemClass(item)]"></div>
        </div>
      </div>

      <div class="content2" v-if="number > 2 && number < 5">
        <div class="content-item" v-for="item in 9" :key="item">
          <div :class="['item1', itemClass(item)]" @click="chooseItem(item)"></div>
          <div :class="['item2', errorIndex ? (errorIndex === item ? 'red-item' : (answerArr.includes(item) ? 'black-item' : '')) : '', itemClass(item)]"></div>
        </div>
      </div>

      <div class="content3" v-if="number > 4 && number < 7">
        <div class="content-item" v-for="item in 16" :key="item">
          <div :class="['item1', itemClass(item)]" @click="chooseItem(item)"></div>
          <div :class="['item2', errorIndex ? (errorIndex === item ? 'red-item' : (answerArr.includes(item) ? 'black-item' : '')) : '', itemClass(item)]"></div>
        </div>
      </div>
      
      <div class="btn-group">
        <div class="btn" @click="stop" v-if="answerStatus">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>

        <div class="btn" @click="startGame" v-if="answerStatus === 'answerWait' || answerStatus === 'continue'">
          <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
          <span class="text">{{ (answerStatus === 'answerWait' && '开始答题') || (answerStatus === 'continue' && '继续训练') }}</span>
        </div>
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 每轮游戏3个回合

export default {
  name: 'game39',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      questions: [],
      answerArr: [],
      answerStatus: '', // answer -- 回答 answerWait -- 回答前等待 continue -- 继续训练
      index: 0,
      classIndex: 0,
      errorIndex: 0,
      show: false,
      status: 1,
      isStop: false,

      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
      this.startProcess()
    },

    startGame() {
      if (this.answerStatus === 'answerWait') {
        this.answerStatus = 'answer'
      } else if (this.answerStatus === 'continue') {
        this.isStop = true
        this.answerStatus = ''
        this.index = 0
        this.classIndex = 0
        this.errorIndex = 0
        this.answerArr = []
        setTimeout(() => {
          this.startProcess()
        }, 2000)
      }
    },

    // 开始流程
    startProcess() {
      this.number++
      if (this.number === 1 || this.number === 2) {
        const list = [1, 2, 3, 4]
        this.questions = api.getRandomArray(list, 1)
      } else if (this.number === 3 || this.number === 4) {
        const list = [1, 2, 3, 4, 5, 6, 7, 8, 9]
        this.questions = api.getRandomArray(list, 3)
      } else if (this.number === 5 || this.number === 6) {
        const list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
        this.questions = api.getRandomArray(list, 5)
      }
      
      setTimeout(() => {
        this.classIndex = this.questions[this.index]
        this.index = 1
        this.changeIndex()
      }, 500)
    },

    changeIndex() {
      setTimeout(() => {
        this.classIndex = 0
      }, 2000)

      if (this.index > this.questions.length) {
        this.answerStatus = 'answerWait'
        this.index = 0
        return
      }

      setTimeout(() => {
        this.classIndex = this.questions[this.index]
        this.index++
        this.changeIndex()
      }, 2500)
    },

    itemClass(item) {
      if (this.classIndex === item) {
        return 'rotate-item'
      }

      if (this.answerArr.includes(item)) {
        return 'rotate-item'
      }

      if (this.errorIndex === item) {
        return 'rotate-item'
      }
    },

    chooseItem(item) { 
      if (this.answerStatus !== 'answer') return

      if (this.questions[this.index] === item) {
        this.answerArr.push(item)
        this.index++
      } else {
        this.errorNum++
        this.errorIndex = item
        this.submit()
      }

      if (this.index >= this.questions.length) {
        this.succesNum++
        this.submit()
      }
    },

    submit() {
      if (this.number < 6) {
        this.answerStatus = 'continue'
      } else {
        setTimeout(() => {
          this.store = this.succesNum ? 100 - this.errorNum * 15 : 0
          this.isStop = true
          this.infos[0].value = this.level
          this.infos[1].value = this.second
          this.infos[2].value = this.succesNum
          this.infos[3].value = this.errorNum
          this.status = 4
          this.params = {
            id: this.info.id,
            grade: this.level,
            time: this.second,
            totalPoints: this.store
          }
        }, 2000)
      }
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    reset() {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.answerStatus = ''
      this.index = 0
      this.classIndex = 0
      this.errorIndex = 0
      this.answerArr = []
      this.startProcess()
    },

    // 继续游戏, 继续计时
    cancel() {
      if (this.answerStatus !== 'answer' && this.answerStatus !== 'continue') return
      this.isStop = false
      this.timing()
    },

    again() {
      this.isStop = false
      this.status = 3
      this.reset()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game39-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game39/bg.png");
  }

  .game-synopsis {

    .synopsis-content {
      padding-top: 20px;
      margin: 0;
      font-size: 30px;
      line-height: 42px;
      font-weight: 400;
      color: #A83A01;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .content1 {
      width: 820px;
      height: 1002px;
      padding-bottom: 180px;
      display: flex;
      flex-wrap: wrap;

      .content-item > div:first-child {
        z-index: 1;
        backface-visibility: hidden;
      }

      .rotate-item {
        transform: rotateY(180deg);
      }

      .reverse-rotate-item {
        transform: rotateY(0deg);
      }

      .content-item {
        position: relative;
        width: 370px;
        height: 370px;
        margin: 20px;
        cursor: pointer;

        .item1 {
          position: absolute;
          top: 20px;
          left: 20px;
          width: 370px;
          height: 370px;
          border-radius: 38px;
          background: #fff;
          transition: all 2s;
        }

        .item2 {
          position: absolute;
          top: 20px;
          left: 20px;
          width: 370px;
          height: 370px;
          border-radius: 38px;
          border: 8px solid #fff;
          background: #006DD0;
          transition: all 2s;
        }

        .red-item {
          background: #FF2525;
        }

        .black-item {
          background: #282C2F;
        }
      }
    }

    .content2 {
      width: 822px;
      height: 1002px;
      padding-bottom: 180px;
      display: flex;
      flex-wrap: wrap;

      .content-item > div:first-child {
        z-index: 1;
        backface-visibility: hidden;
      }

      .rotate-item {
        transform: rotateY(180deg);
      }

      .reverse-rotate-item {
        transform: rotateY(0deg);
      }

      .content-item {
        position: relative;
        width: 244px;
        height: 244px;
        margin: 15px;
        cursor: pointer;

        .item1 {
          position: absolute;
          top: 15px;
          left: 15px;
          width: 244px;
          height: 244px;
          border-radius: 28px;
          background: #fff;
          transition: all 2s;
        }

        .item2 {
          position: absolute;
          top: 15px;
          left: 15px;
          width: 244px;
          height: 244px;
          border-radius: 28px;
          border: 6px solid #fff;
          background: #006DD0;
          transition: all 2s;
        }

        .red-item {
          background: #FF2525;
        }

        .black-item {
          background: #282C2F;
        }
      }
    }

    .content3 {
      width: 820px;
      height: 1002px;
      padding-bottom: 180px;
      display: flex;
      flex-wrap: wrap;

      .content-item > div:first-child {
        z-index: 1;
        backface-visibility: hidden;
      }

      .rotate-item {
        transform: rotateY(180deg);
      }

      .reverse-rotate-item {
        transform: rotateY(0deg);
      }

      .content-item {
        position: relative;
        width: 185px;
        height: 185px;
        margin: 10px;
        cursor: pointer;

        .item1 {
          position: absolute;
          top: 10px;
          left: 10px;
          width: 185px;
          height: 185px;
          border-radius: 18px;
          background: #fff;
          transition: all 2s;
        }

        .item2 {
          position: absolute;
          top: 10px;
          left: 10px;
          width: 185px;
          height: 185px;
          border-radius: 18px;
          border: 4px solid #fff;
          background: #006DD0;
          transition: all 2s;
        }

        .red-item {
          background: #FF2525;
        }

        .black-item {
          background: #282C2F;
        }
      }
    }

    .btn-group {
      position: absolute;
      bottom: 40px;
      width: 1072px;
      height: 84px;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px;
        height: 84px;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px;
          height: 84px;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px 0 21px 0;
          font-size: 38px;
          line-height: 53px;
          text-align: center;
          color: #A83A01;
          user-select: none;
        }
      }
    }
  }
}
</style>
<template>
  <div class="game41-page">
    <audio ref="music" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <div class="page-bg"></div>
    <settingPage @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、根据病人的情况，选择“训练等级”。</p>
        <p class="synopsis-content">2、点击“开始训练”，进入训练页面。</p>
        <p class="synopsis-content">3、点击“开始记忆”，图片将显示15秒钟。</p>
        <p class="synopsis-content">4、根据您的记忆，分别找出相同的图片。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="content-bg">
        <div class="content">
          <div class="content-item-wapper" v-for="item in 4" :key="item">
            <img v-show="haveImg(item)" class="content-img" :src="`/static/game_assets/game41/img_${item}.png`" />

            <div v-show="!haveImg(item)" :class="['content-item', 'content-item-' + item]" @click="chooseItem(item)">
              <img :class="[`img-${questions[item - 1]}`, itemClass(item)]" :src="`/static/game_assets/game41/item_${questions[item - 1]}.png`">
            </div>
          </div>
        </div>
      </div>
      
      <div class="btn-group">
        <div class="btn" @click="stop">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>

        <div class="btn" @click="startGame" v-if="answerStatus === 'playWait' || answerStatus === 'play' || answerStatus === 'continue'">
          <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
          <span class="text">{{ (answerStatus === 'playWait' && '开始记忆') || (answerStatus === 'play' && '结束记忆') || (answerStatus === 'continue' && '继续训练') }}</span>
        </div>
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 每轮游戏3个回合

export default {
  name: 'game41',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      questions: [1, 1, 2, 2],
      answerStatus: 'playWait', // play -- 题目 answer -- 回答 playWait -- 播放前等待 continue -- 继续训练
      index: 0,
      show: false,
      status: 1,
      isStop: false,
      chooseArr: [],
      indexArr: [],
      timer: null,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
    },

    startGame() {
      if (this.answerStatus === 'playWait') {
        this.answerStatus = 'play'
        this.startProcess()
      } else if (this.answerStatus === 'play') {
        clearTimeout(this.timer)
        this.answerStatus = 'answer'
      } else if (this.answerStatus === 'continue') {
        this.isStop = true
        this.answerStatus = ''
        this.index = 0
        this.indexArr = []
        this.chooseArr = []
        this.answerStatus = 'playWait'
      }
    },

    // 开始流程
    async startProcess() {
      this.number++
      this.questions = api.shuffle([1, 1, 2, 2])
      this.playAnimation()
    },

    playAnimation(item) {
      this.timer = setTimeout(() => {
        this.answerStatus = 'answer'
        this.isStop = false
        this.timing()
      }, 15000)
    },

    haveImg(item) {
      if (!this.indexArr.length) return false
      const len = this.chooseArr.filter(it => it === this.questions[item - 1]).length

      return len === 2
    },

    itemClass(item) {
      if (this.answerStatus === 'play' || this.indexArr.indexOf(item) !== -1) {
        return 'opacity-item-1'
      }

      if (this.answerStatus === 'answer') {
        return 'opacity-item-2'
      }
    },

    chooseItem(item) { 
      if (this.answerStatus !== 'answer') return

      if (!(this.chooseArr.length % 2)) {
        this.chooseArr.push(this.questions[item - 1])
        this.indexArr.push(item)
      } else {
        const choose = this.questions[item - 1]
        const answer = this.chooseArr[this.chooseArr.length - 1]

        if (choose !== answer) {
          this.errorNum++
          this.$refs.music.play()
        } else {
          this.succesNum++
          this.indexArr.push(item)
          this.chooseArr.push(this.questions[item - 1])
        }

        if (choose === answer && this.chooseArr.length === 4) {
          if (this.number < 3) {
            this.answerStatus = 'continue'
          } else {
            setTimeout(() => {
              this.store = 100 - this.errorNum * 10 > 0 ? 100 - this.errorNum * 10 : 0
              this.isStop = true
              this.infos[0].value = this.level
              this.infos[1].value = this.second
              this.infos[2].value = this.succesNum
              this.infos[3].value = this.errorNum
              this.status = 4
              this.params = {
                id: this.info.id,
                grade: this.level,
                time: this.second,
                totalPoints: this.store
              }
            }, 2000)
          }
        }
      }
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    reset() {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.answerStatus = 'playWait'
      this.index = 0
      this.indexArr = []
      this.chooseArr = []
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    again() {
      this.isStop = false
      this.status = 3
      this.reset()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game41-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game41/bg.png");
  }

  .game-synopsis {

    .synopsis-content {
      padding-top: 20px;
      margin: 0;
      font-size: 30px;
      line-height: 42px;
      font-weight: 400;
      color: #A83A01;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .content-bg {
      width: 770px;
      height: 770px;
      margin-top: 60px;
      padding: 20px;
      background: rgba(173, 30, 30, 0.26);

      .content {
        width: 730px;
        height: 730px;
        padding: 12px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        background: #FF5757;

        .content-item-wapper {
          position: relative;
          width: 353px;
          height: 353px;
          background: #FF5757;
        }

        .content-item-1 {
          position: absolute;
          top: 0;
          left: 0;
        }

        .content-item-2 {
          position: absolute;
          top: 0;
          right: 0;
        }

        .content-item-3 {
          position: absolute;
          bottom: 0;
          left: 0;
        }

        .content-item-4 {
          position: absolute;
          bottom: 0;
          right: 0;
        }

        .content-item {
          width: 347px;
          height: 347px;
          background: #fff;
          cursor: pointer;

          .img-1 {
            position: absolute;
            top: 63px;
            left: 114px;
            width: 148px;
            height: 220px;
            opacity: 0;
          }

          .img-2 {
            position: absolute;
            top: 88px;
            left: 114px;
            width: 148px;
            height: 176px;
            opacity: 0;
          }

          .opacity-item-1 {
            opacity: 1;
            transition: opacity 0.5s;
          }

          .opacity-item-2 {
            opacity: 0;
            transition: opacity 1.5s;
          }
        }

        .content-img {
          width: 353px;
          height: 353px;
        }
      }
    }

    .btn-group {
      position: relative;
      width: 1072px;
      height: 166px;
      display: flex;
      justify-content: space-between;
      padding-bottom: 82px;
      margin: 0 auto;

      .btn {
        position: relative;
        width: 295px;
        height: 84px;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px;
          height: 84px;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px 0 21px 0;
          font-size: 38px;
          line-height: 53px;
          text-align: center;
          color: #A83A01;
          user-select: none;
        }
      }
    }
  }
}
</style>
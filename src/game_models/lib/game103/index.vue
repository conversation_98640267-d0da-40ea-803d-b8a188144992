<template>
  <div class="game103-page">
    <div class="page-bg"></div>
		<settingPage title="简图定位" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、通过查看地图找到指定地点是日常生活中经常用到的技能。</p>
        <p class="synopsis-content">2、选择不同类型的地图， 会提出相应的问题请您回答。</p>
        <p class="synopsis-content">3、请耐心查找，不要急躁 。一时找不到时不要灰心，不要放弃。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <img class="left-icon" src="/static/game_assets/game102/left_icon.png" />
      <img class="right-icon" src="/static/game_assets/game102/right_icon.png" />

      <div class="game-guide" v-show="answerStatus === 'ready'">
        <div class="guide-left">
          <img class="left-bg" src="/static/game_assets/game102/left_bg.png" />
          <img class="left-img" src="/static/game_assets/game102/content_img.png" />
        </div>

        <div class="guide-right">
          <img class="right-bg right-bg1" src="/static/game_assets/game102/book1.png" />
          <img class="right-bg right-bg2" src="/static/game_assets/game102/book2.png" />

          <p class="right-title">选择地图</p>

          <div class="right-choose">
            <p class="choose-text">中国省图</p>
            <div class="choose-icon">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img class="icon" src="/static/game_assets/game32/correct.png" />
            </div>
          </div>

          <!-- <div class="right-choose">
            <div class="choose-item">
              <p class="choose-text">北京交通</p>
              <div class="choose-icon" @click="chooseMapItem(1)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="chooseMap === 1" class="icon" src="/static/game_assets/game32/correct.png" />
              </div>
            </div>
            
            <div class="choose-item">
              <p class="choose-text">北京市政</p>
              <div class="choose-icon" @click="chooseMapItem(2)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="chooseMap === 2" class="icon" src="/static/game_assets/game32/correct.png" />
              </div>
            </div>

            <div class="choose-item">
              <p class="choose-text">中国省图</p>
              <div class="choose-icon" @click="chooseMapItem(3)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="chooseMap === 3" class="icon" src="/static/game_assets/game32/correct.png" />
              </div>
            </div>
          </div> -->

          <img class="right-btn" src="/static/game_assets/game102/btn1.png" @click="startGame" />
        </div>
      </div>

      <div class="content" v-show="answerStatus === 'answer'">
        <div class="content-left">
          <img class="left-bg" src="/static/game_assets/game102/left_bg.png" />
          <img class="left-icon" src="/static/game_assets/game102/top_icon.png" />
          <img class="left-img" src="/static/game_assets/game102/item2.png" />
        </div>

        <div class="content-right">
          <p class="right-title">{{current.question}}</p>

          <div class="right-item" v-for="item in current.answerList" :key="item.index + 'item'" @click="chooseItem(item.index)">
            <img class="item-bg" src="/static/game_assets/game102/btn_bg2.png" />
            <img class="item-bg" v-if="choose === item.index" src="/static/game_assets/game102/btn_bg1.png" />

            <span class="item-text">{{item.value}}</span>
          </div>
        </div>          
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
        <div class="index" v-if="answerStatus === 'answer'">
          <img class="index-bg" src="/static/game_assets/game102/icon.png" />
          <span class="index-text">{{number}}/5</span>
        </div>
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'

export default {
  name: 'game103',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,
      answerStatus: 'ready',  // ready -- 准备 answer -- 答题
			question: [],
      current: {},
      chooseMap: 3, // 1 -- 北京交通 2 -- 北京市政 3 -- 中国省区
			choose: 0,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.status = 3
    },

    startGame() {
      if (!this.chooseMap) return

      this.answerStatus = 'answer'
      // this.timing()
      this.question = api.getRandomArray(data.data, 5)
      this.startProcess()
    },

    startProcess() {
      this.current = this.question[this.number]
      this.current.answerList = api.shuffle(this.current.answerList)
			this.number++
    },

    chooseMapItem(item) {
      this.chooseMap = item
    },

    chooseItem(item) {
      if (this.isShowCorrect || this.isShowError) return
			this.choose = item

      if (this.choose === this.current.answer) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }

      setTimeout(() => {
        this.isShowError = false
        this.isShowCorrect = false

        if (this.number >= 5) {
          this.submit()
        } else {
          this.choose = 0
          this.startProcess()
        }
      }, 800)
    },

    stop() {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isStop = true
			this.store = 20 * this.succesNum
			this.infos[0].value = this.second
			this.infos[1].value = this.succesNum
			this.infos[2].value = this.errorNum
			this.status = 4
			this.params = {
				id: this.info.id,
				grade: '',
				time: this.second,
				totalPoints: this.store
			}
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answerStatus = 'ready'
			this.question = []
			this.choose = 0
      this.chooseMap = 3
			this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game103-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game102/bg.png");
  } 

  .game-synopsis {
    width: 1576px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .left-icon {
      position: absolute;
      left: 69px;
      bottom: 0;
      width: 437px;
    }

    .right-icon {
      position: absolute;
      bottom: 0;
      right: 30px;
      width: 177px;
    }

    .game-guide {
      position: relative;
      width: 1890px;
      height: 960px;
      margin-right: 30px;
      margin-bottom: 58px;

      .guide-left {
        position: absolute;
        left: 0;
        top: 0;
        width: 1341px;
        height: 959px;

        .left-bg {
          position: absolute;
          left: 0;
          top: 0;
          width: 1341px;
          height: 959px;
        }

        .left-img {
          position: absolute;
          top: 259px;
          left: 208px;
          width: 724px;
        }
      }

      .guide-right {
        position: absolute;
        top: 139px;
        right: 0;
        width: 674px;
        height: 763px;
        display: flex;
        flex-direction: column;
        padding: 168px 210px 0 230px;
        align-items: center;

        .right-bg {
          position: absolute;
        }

        .right-bg1 {
          top: 0;
          left: 0;
          width: 627px;
        }

        .right-bg2 {
          top: 18px;
          left: 10px;
          width: 665px;
        }

        .right-title {
          position: relative;
          margin: 0;
          font-size: 56px;
          line-height: 56px;
          text-align: center;
          font-weight: 500;
          color: #FEFEFE;
          text-align: center;
        }

        .right-choose {
          display: inline-flex;
          justify-content: center;
          padding-top: 30px;
          padding-bottom: 140px;

          .choose-text {
            position: relative;
            margin: 0;
            padding-right: 18px;
            font-size: 36px;
            line-height: 41px;
            color: #fff;
          }

          .choose-icon {
            position: relative;
            width: 41px;
            height: 41px;

            .bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 41px;
              height: 41px;
            }

            .icon {
              position: relative;
              width: 41px;
              height: 35px;
            }
          }
        }

        // .right-choose {
        //   display: flex;
        //   flex-direction: column;
        //   justify-content: space-between;
        //   height: 219px;
        //   padding-top: 30px;
        //   padding-bottom: 36px;

        //   .choose-item {
        //     display: inline-flex;
        //     justify-content: center;
            

        //     .choose-text {
        //       position: relative;
        //       margin: 0;
        //       padding-right: 18px;
        //       font-size: 36px;
        //       line-height: 41px;
        //       color: #fff;
        //     }

        //     .choose-icon {
        //       position: relative;
        //       width: 41px;
        //       height: 41px;
        //       cursor: pointer;

        //       .bg {
        //         position: absolute;
        //         top: 0;
        //         left: 0;
        //         width: 41px;
        //         height: 41px;
        //       }

        //       .icon {
        //         position: relative;
        //         width: 41px;
        //         height: 35px;
        //       }
        //     }
        //   }
        // }

        .right-btn {
          position: relative;
          width: 213px;
          cursor: pointer;
        }
      }
    }

		.content {
      position: relative;
      width: 1997px;
      height: 991px;
      margin-right: 23px;
      margin-bottom: 89px;

      .content-left {
        position: absolute;
        top: 0;
        left: 0;
        width: 1330px;
        height: 991px;
        z-index: 1;

        .left-bg {
          position: absolute;
          top: 32px;
          left: 0;
          width: 1330px;
        }

        .left-icon {
          position: absolute;
          bottom: 88px;
          left: 71px;
          width: 204px;
        }

        .left-img {
          position: absolute;
          top: 85px;
          left: 107px;
          width: 995px;
        }
      }

      .content-right {
        position: absolute;
        top: 0;
        right: 0;
        width: 777px;
        height: 991px;
        background: #B8E09D;
        padding: 304px 92px 130px 201px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .right-title {
          position: absolute;
          top: 147px;
          left: 0;
          margin: 0;
          padding-left: 200px;
          padding-bottom: 17px;
          font-size: 42px;
          line-height: 42px;
          font-weight: 500;
          color: #192333;
        }

        .right-item {
          position: relative;
          width: 508px;
          height: 128px;
          padding: 32px 0 46px 0;
          cursor: pointer;

          .item-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 508px;
          }

          .item-text {
            position: relative;
            display: block;
            font-size: 50px;
            line-height: 50px;
            text-align: center;
            font-weight: 500;
            color: #F8F9FF;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 40px;
      display: flex;
      justify-content: space-between;
      width: 1775px;
      margin-left: 75px;
      z-index: 2;

      .img1 {
        width: 270px;
        height: 115px;
        cursor: pointer;
      }

      .index {
        position: relative;
        width: 96px;
        height: 97px;

        .index-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 96px;
          height: 97px;
        }

        .index-text {
          position: relative;
          display: block;
          font-size: 28px;
          line-height: 97px;
          text-align: center;
          font-weight: 500;
          color: #FEFEFE;
        }
      }
    }

    .center-icon {
      position: absolute;
      width: 287px;
      margin-bottom: 40px;
      z-index: 99;
    }
  }
}
</style>
<template>
  <div class="game220-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source :src="audioUrl" type="audio/mpeg" />
    </audio>
    <settingPage @start="status = 2" @challenge="start" v-if="status < 3" titleImg="/static/game_assets/game220/title.png">
      <div class="game-synopsis">
        <p class="synopsis-text">指令执行训练促进语言理解、提升认知功能、增强社交互动和改善生活技能，帮助患者实现功能性沟通，提高生活质量。此训练的目的通过系统化的训练，推动患者语言能力的全面发展，增强认知与学习的能力</p>
        <div class="synopsis-item">
          <img class="item-text" src="/static/game_assets/game219/text_1.png"></img>
          <div class="item-btn-group">
            <div class="item-btn" v-for="item in [3, 5, 10]" :key="item" @click="trainTime = item">
              <img v-if="trainTime === item" class="btn-bg" src="/static/game_assets/game219/btn_bg_2.png"></img>
              <img v-else class="btn-bg" src="/static/game_assets/game219/btn_bg_1.png"></img>
              <span class="btn-text" :class="[trainTime === item && 'choose-btn']">{{ item }}min</span>
            </div>
          </div>
        </div>
        <div class="synopsis-item">
          <img class="item-text" src="/static/game_assets/game219/text_2.png"></img>
          <div class="item-btn-group">
            <div class="item-btn" v-for="item in ['一步指令', '两步指令']" :key="item" @click="trainType = item">
              <img v-if="trainType === item" class="btn-bg" src="/static/game_assets/game219/btn_bg_2.png"></img>
              <img v-else class="btn-bg" src="/static/game_assets/game219/btn_bg_1.png"></img>
              <span class="btn-text" :class="[trainType === item && 'choose-btn']">{{ item }}</span>
            </div>
          </div>
        </div>
      </div>
    </settingPage>

    <div class="game-content" v-if="status === 3">
      <div class="btn-right">
        <div class="icon-img icon-play">
          <img class="top-icon" @click="play" src="/static/game_assets/game219/icon_2.png" />
        </div>
        <div class="icon-img">
          <img class="top-icon" src="/static/game_assets/game219/icon_1.png" />
          <span>{{ score }}分</span>
        </div>
        <div class="icon-img pl-13">
          <img class="top-icon" src="/static/game_assets/game219/icon_3.png" />
          <span>{{ time }}</span>
        </div>
      </div>

      <div class="content">
        <p class="question-text pb-210">{{ number }}、{{ currect.text }}</p>

        <div class="btn-group">
          <img class="img" src="/static/game_assets/game220/btn_yes.png" @click="setScore(1)" />
          <img class="img" src="/static/game_assets/game220/btn_no.png" @click="setScore(-1)">
        </div>
      </div>
    </div>
    <resultPage v-if="status === 4" :score="score" :info="infos" :params="params" @again="again"></resultPage>
  </div>
</template>

<script>
import settingPage from '../component/settingPage3.vue'
import resultPage from '../component/resultPage3.vue'
import api from '../../utils/common'
import data from './data/data.json'

export default {
  name: 'game218',

  props: {
    info: {
      type: Object,
      default: () => { }
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
  },

  data() {
    return {
      number: 0,
      second: 0,
      score: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isPlay: false,
      audioUrl: '',

      questionIndex: 0,
      questionList: [],
      currect: {},
      answer: '',

      trainTime: 3,
      trainType: '一步指令',
      showJudge: false,

      isStop: false,
      params: {},
      infos: [
        {
          key: '总分',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time() {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      // 计时结束，提交答案
      if (this.second > this.trainTime * 60) {
        this.submit()
        return
      }
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play() {
      this.audioUrl = this.currect.audio
      this.$nextTick(() => {
        this.$refs.music.load()
        this.$refs.music.play()
        this.isPlay = true
      })
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnd() {
      this.isPlay = false
    },

    start() {
      this.status = 3
      // this.timing()

      this.questionList = this.trainType === '一步指令' ? data. one : data.two
      this.questionList = api.shuffle(this.questionList)
      this.startProcess()
    },

    setScore(type) {
      if (type === 1) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      this.score = this.succesNum * 10
      this.next()
    },

    next() {
      this.answer = ''
      this.showJudge = false
      this.startProcess()
    },

    startProcess() {
      if (this.questionIndex >= this.questionList.length) {
        this.questionList = this.trainType === '一步指令' ? data.one : data.two
        this.questionList = api.shuffle(this.questionList)
        this.questionIndex = 0
      }
      this.currect = this.questionList[this.questionIndex]
      this.questionIndex++
      this.number++
      this.play()
    },

    handleClick(type) {
      this.answer = type
      this.showJudge = !!type
    },

    submit() {
      this.pause()
      this.infos[0].value = (this.succesNum + this.errorNum) * 10
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.score,
        successNumber: this.succesNum,
        failNumber: this.errorNum
      }
    },

    quit() {
      this.pause()
      this.isStop = true
      this.$router.go(-1)
    },

    again() {
      this.number = 0
      this.score = 0
      this.questionIndex = 0
      this.second = 0
      this.isStop = false
      this.start()
      this.timing()
    },
  }
}
</script>

<style lang="scss">
.game220-page {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game219/bg.png");
  }

  .game-synopsis {
    width: 1625px;
    height: 621px;
    margin-bottom: 10px;
    padding: 100px 71px 20px 83px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game219/title_bg.png");
    transform: scale(0.85);

    .synopsis-text {
      margin: 0;
      font-size: 32px;
      line-height: 35px;
      color: #11513F;
      padding-bottom: 24px;
    }

    .synopsis-item {
      display: flex;
      padding: 58px 44px;
      align-items: center;

      .item-text {
        width: 142px;
        height: 29px;
      }

      .item-btn-group {
        display: flex;
        align-items: center;
        .item-btn {
          position: relative;
          width: 183px;
          height: 81px;
          margin-left: 40px;
          cursor: pointer;

          .btn-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 183px;
            height: 81px;
          }

          .btn-text {
            display: block;
            position: relative;
            padding-top: 10px;
            padding-bottom: 0;
            font-size: 36px;
            font-weight: 600;
            color: #125340;
            text-align: center;
            line-height: 71px;
          }

          .choose-btn {
            padding-top: 0;
            padding-bottom: 10px;
          }
        }
      }
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .btn-right {
      position: absolute;
      top: 50px;
      right: 24px;
      height: 76px;
      display: flex;
      z-index: 99;
      align-items: center;
    }

    .icon-img {
      position: relative;
      padding-top: 10px;
      padding-left: 50px;
      margin-left: 17px;
      width: 262px;
      height: 76px;

      .top-icon {
        position: absolute;
        top: 0;
        left: 0;
        width: 262px;
        height: 76px;
        cursor: pointer;
      }

      span {
        position: relative;
        display: block;
        font-size: 30px;
        font-weight: 600;
        line-height: 60px;
        color: #FDFDFD;
        user-select: none;
        text-align: center;
      }
    }

    .pl-13 {
      padding-left: 13px;
    }

    .icon-play {
      cursor: pointer;
    }

    .top {
      position: absolute;
      top: 128px;
      left: 0;
      width: 100%;
      height: 82px;
      padding: 0 140px;

      .top-icon {
        position: absolute;
        top: 0;
        left: 140px;
        width: 82px;
        cursor: pointer;
      }

      .top-text {
        display: block;
        font-size: 40px;
        line-height: 56px;
        text-align: center;
        font-weight: 600;
        color: #0055A6;
      }
    }

    .content {
      position: relative;
      width: 1688px;
      height: 1044px;
      padding-top: 195px;
      margin-bottom: 20px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-repeat: no-repeat;
      background-size: cover;
      background-image: url("/static/game_assets/game219/content_bg.png");
      transform: scale(0.85);

      .question-text {
        margin: 0;
        text-align: center;
        font-size: 44px;
        line-height: 44px;
        font-weight: 600;
        color: #333333;
      }

      .pb-210 {
        padding-bottom: 210px;
      }

      .question-img {
        width: 651px;
        height: 360px;
        border-radius: 40px;
        margin: 35px 0 40px 0;
        overflow: hidden;
        object-fit: cover;
      }

      .btn-group {
        width: 960px;
        display: inline-flex;
        justify-content: space-between;

        .img {
          width: 330px;
          height: 176px;
          cursor: pointer;
        }
      }

      .judge {
        position: absolute;
        bottom: -15px;
        right: -82px;
        width: 648px;
        height: 322px;
        padding: 154px 146px 72px 152px;
        display: flex;
        justify-content: space-between;
        background-repeat: no-repeat;
        background-size: cover;
        background-image: url("/static/game_assets/game219/info_bg.png");

        .icon {
          width: 100px;
          height: 100px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
<template>
  <div class="game112-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnded">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <audio ref="music2" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <settingPage @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">本训练用于提高训练者的各个家属认知，采用卡通形象进行训练。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="content1" v-if="number === 1">
        <img class="content-bg" src="/static/game_assets/game112/content_bg.png" />

        <div class="content-left">
          <div :class="['left-item', (index === 0 || index === 6) && 'big-item-left', (index === 1 || index === 7) && 'big-item-right']" v-for="(item, index) in questions" :key="index + 'item'">
            <img :class="['item-img', current.index === item.index && 'flash-item']" :src="`/static/game_assets/game112/item_${item.index}.png`" />
            <p class="item-text">{{item.name}}</p>
          </div>
        </div>

        <div class="content-right">
          <img class="right-img" :src="`/static/game_assets/game112/item_${current.index}.png`" />
          <p class="right-text">{{current.name}}</p>
        </div>
      </div>

      <div class="content2" v-if="number === 2">
        <div class="content-item" v-for="(item, index) in questions" :key="index + 'item2'" @click="chooseItem(item.index)">
          <img class="item-img" :src="`/static/game_assets/game112/item_${item.index}.png`" />
          <img v-if="choose === item.index" class="item-img" :src="`/static/game_assets/game112/choose_${item.index}.png`" />

          <img v-if="choose === item.index" class="item-icon" src="/static/game_assets/game112/icon.png" />
        </div>
      </div>

      <div class="content3" v-if="number === 3">
        <div :class="['content-item', 'content-item-' + index, itemClass(item, index)]" v-for="(item, index) in list" :key="index + 'img2'" @click="setChoose(item, index)" >
          <img class="item-img" :src="`/static/game_assets/game112/item_${item.index}.png`" />
          <p class="item-name">{{item.name}}</p>
        </div>
      </div>
      
      <div class="btn-group">
        <div class="btn" @click="stop">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>
      </div>
    </div>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'
import data from './data/data.json'

export default {
  name: 'game112',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data() {
    return {
      musicUrl: '',
      number: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      show: false,
      status: 1,
      isStop: false,
      isPlay: false,
      canClick: true,
      isContinue: false,

      index: 0,
      current: {},
      choose: 0,
      questions: [],
      list: [],
      answer: [],
      chooseAnswer: [],
      
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    itemClass(item, index) {
      const list1 = this.chooseAnswer.filter(it => it.name === item.index)
      const list2 = this.chooseAnswer.filter(it => it.index === index)
      if (list2.length && list1.length === 1) {
        return 'scale-item-' + index
      }
      if (list2.length && list1.length === 2) {
        return 'translate-item-' + index
      }
      return ''
    },

    play(url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    playError() {
      this.$refs.music2.play()
    },

    pause() {
      this.$refs.music2.pause()
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnded() {
      if (this.number === 1) {
        setTimeout(() => {
          if (this.show) return
          if (this.index >=8) {
            this.index = 0
            this.number = 2
            this.isPlay = false
          }

          this.startProcess()
        }, 500)
      } else {
        this.isPlay = false
      }
    },

    start() {
      this.status = 3
      this.questions = api.getRandomArray(data.data, 1)[0]
      this.startProcess()
    },

    // 开始流程
    startProcess() {
      if (this.number === 1) {
        this.current = this.questions[this.index]
        this.index++
        this.play(this.current.audio)
      } else  if (this.number === 2) {
        this.questions = api.shuffle(this.questions)
        this.answer = api.getRandomArray(this.questions, 5)
        this.play(this.answer[this.index].audio)
      } else {
        this.play('/static/game_assets/audio/game112/question.mp3')
        this.list = api.getRandomArray(this.questions, 5)
        this.list = this.list.concat(this.list)
        this.list = api.shuffle(this.list)
      }
    },

    chooseItem(item) { 
      if (!this.canClick) return
      this.choose = item
      if (this.choose === this.answer[this.index].index) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      this.canClick = false
      setTimeout(() => {
        this.canClick = true
        if (this.index >= 4) {
          this.number = 3
          this.startProcess()
        } else {
          this.choose = 0
          this.index++
          this.play(this.answer[this.index].audio)
        }
      }, 800)
    },

    setChoose(item, index) {
      const list = this.chooseAnswer.filter(it => it.index === index)
      if (list.length) return
      if ((this.chooseAnswer.length % 2) && (item.index !== this.chooseAnswer[this.chooseAnswer.length - 1].name)) {
        this.errorNum++
        this.playError()
        return
      }
      this.succesNum++
      this.chooseAnswer.push({
        name: item.index,
        index
      })

      if (this.chooseAnswer.length >= 10) {
        setTimeout(() => {
          this.submit()
        }, 1500)
      }
    },

    stop() {
      this.isStop = true
      this.show = true
      if (this.isPlay) {
        this.pause()
        this.isContinue = true
      }
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
      if (this.isContinue) {
        this.play()
        this.isContinue = false
      }
    },

    submit() {
      this.pause()
      this.isStop = true
      this.store = 100 - 5 * this.errorNum > 0 ? 100 - 5 * this.errorNum : 0
			this.infos[0].value = this.second
			this.infos[1].value = this.succesNum
			this.infos[2].value = this.errorNum
			this.status = 4
			this.params = {
				id: this.info.id,
				grade: '',
				time: this.second,
				totalPoints: this.store
			}
    },

    again() {
      this.number = 1
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
			this.choose = 0
      this.index = 0
      this.current = {}
      this.chooseAnswer = []
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game112-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game109/bg.png");
  }

  .game-synopsis {

    .synopsis-content {
      padding-top: 20px;
      margin: 0;
      font-size: 30px;
      line-height: 42px;
      font-weight: 400;
      color: #A83A01;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .content1 {
      position: relative;
      width: 1465px;
      height: 816px;
      margin-bottom: 190px;
      margin-right: 15px;
      display: flex;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1465px;
      }

      .content-left {
        position: relative;
        flex: 1;
        display: inline-flex;
        flex-wrap: wrap;
        align-content: flex-start;
        padding: 88px 0 0 60px;

        .left-item {
          width: 151px;
          display: inline-flex;
          flex-direction: column;
          align-items: center;

          .item-img {
            width: 124px;
            height: 124px;
          }

          .flash-item {
            animation: flash 1s linear infinite;
          }

          .item-text {
            margin: 0;
            padding: 17px 0 24px 0;
            font-size: 28px;
            line-height: 40px;
            font-weight: 500;
            color: #A83A01;
          }
        }

        .big-item-left {
          width: 302px;
          padding-left: 151px;
        }

        .big-item-right {
          width: 302px;
          padding-right: 151px;
        }
      }

      .content-right {
        position: relative;
        flex: 1;
        display: inline-flex;
        flex-direction: column;
        align-items: center;

        .right-img {
          width: 375px;
          height: 375px;
          margin-top: 184px;
          margin-bottom: 86px;
        }

        .right-text {
          margin: 0;
          font-size: 28px;
          line-height: 40px;
          font-weight: 500;
          color: #A83A01;
        }
      }
    }

    .content2 {
      position: relative;
      width: 1000px;
      height: 870px;
      margin-bottom: 34px;
      margin-right: 98px;
      display: flex;
      flex-wrap: wrap;
      align-content: space-between;

      .content-item {
        position: relative;
        width: 306px;
        height: 248px;
        margin-left: 27px;
        cursor: pointer;

        .item-img {
          position: absolute;
          top: 0;
          right: 0;
          width: 248px;
        }

        .item-icon {
          position: absolute;
          top: 66px;
          left: 0;
          width: 95px;
        }
      }
    }

    .content3 {
      position: relative;
      width: 1750px;
      height: 864px;
      padding-bottom: 110px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;

      .content-item {
        position: relative;
        width: 286px;
        height: 286px;
        margin: 10px;
        cursor: pointer;
        transition: all 1.5s ease-in-out;

        .item-img {
          position: absolute;
          top: 0;
          left: 0;
          width: 286px;
          height: 286px;
        }

        .item-name {
          position: absolute;
          width: 100%;
          bottom: 25px;
          margin: 0;
          font-size: 28px;
          line-height: 40px;
          text-align: center;
          font-weight: 500;
          color: #A83A01;
        }
      }

      .scale-item-0, .scale-item-1, .scale-item-2, .scale-item-3, .scale-item-4, .scale-item-5, .scale-item-6, .scale-item-7, .scale-item-8, .scale-item-9 {
        transform: scale(1.2);
      }

      .translate-item-0 {
        transform: translate3d(722px, 279px, 0);
        opacity: 0;
      }

      .translate-item-1 {
        transform: translate3d(361px, 279px, 0);
        opacity: 0;
      }

      .translate-item-2 {
        transform: translate3d(0, 279px, 0);
        opacity: 0;
      }

      .translate-item-3 {
        transform: translate3d(-361px, 279px, 0);
        opacity: 0;
      }

      .translate-item-4 {
        transform: translate3d(-722px, 279px, 0);
        opacity: 0;
      }

      .translate-item-5 {
        transform: translate3d(722px, -279px, 0);
        opacity: 0;
      }

      .translate-item-6 {
        transform: translate3d(361px, -279px, 0);
        opacity: 0;
      }

      .translate-item-7 {
        transform: translate3d(0, -279px, 0);
        opacity: 0;
      }

      .translate-item-8 {
        transform: translate3d(-361px, -279px, 0);
        opacity: 0;
      }

      .translate-item-9 {
        transform: translate3d(-722px, -279px, 0);
        opacity: 0;
      }
    }

    .btn-group {
      position: absolute;
      bottom: 52px;
      width: 1572px;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px;
        height: 84px;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px;
          height: 84px;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px 0 21px 0;
          font-size: 38px;
          line-height: 53px;
          text-align: center;
          color: #A83A01;
        }
      }
    }
  }
}

@keyframes flash{
  0% {
    transform: scale(1);
  }
  80% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
</style>
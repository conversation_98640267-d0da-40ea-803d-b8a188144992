<template>
  <div class="game158-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPage @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、请根据提示选择正确的选项； </p>
        <p class="synopsis-content">2、训练结束会出现成绩单； </p>
        <p class="synopsis-content">3、本训练难度为B(普通)。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="top">
        <div class="top-left">
          <img class="icon" src="/static/game_assets/game157/icon.png" @click="play" />
          <span class="text">下面哪个选项数量更{{type === 1 ? '多' : '少'}}？</span>
        </div>

        <div class="top-right">
          <span class="text-item">题目分数：{{store}}</span>
          <span class="text-item">题目数量：{{number}}</span>
          <span class="text-item">训练用时：{{time}}</span>
        </div>
      </div>

      <div class="content">
        <div :class="['content-item', answer === item.num && 'choose-item']" v-for="item in currect.question" :key="item.num + 'item'" @click="chooseItem(item.num)">
          <div class="item" v-for="it in item.num" :key="it + 'point' + item" :style="{background: item.color}"></div>
        </div>
      </div>
      
      <div :class="['btn-group']">
        <div class="btn" @click="stop">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>

        <div v-if="answer" class="btn" @click="confirm">
          <img class="bg" src="/static/game_assets/game8/brown_bg.png">
          <span class="text">完成</span>
        </div>
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'

export default {
  name: 'game158',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      musicUrl: '',
      number: 0,
      second: 0,
      store: 0,
      level: 2,
      succesNum: 0,
      errorNum: 0,
      show: false,
      status: 1,
      isStop: false,
      isPlay: false,

      type: 1, // 1-更多 2-更少
      currect: {
        question: [],
        answer: 0,
      },
      answer: 0,

      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time() {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.level = this.info.level || 2
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess() {
      const list1 = ['#9F46C6', '#F29A36', '#FF7F70', '#36F274', '#FFDE36', '#545BFF']
      const list2 = [11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
      this.type = api.randomNum(1, 2)
      this.currect.question = []
      const colorList = api.getRandomArray(list1, 2)
      const numList = api.getRandomArray(list2, 2)

      for(let i = 0; i < 2; i++) {
        this.currect.question.push({
          num: numList[i],
          color: colorList[i]
        })
      }

      if (this.type === 1) {
        this.currect.answer = Math.max(numList[0], numList[1])
      } else {
        this.currect.answer = Math.min(numList[0], numList[1])
      }
      this.play()
      this.number++
    },

    chooseItem(item) {
      this.answer = item
    },

    play() {
      if (this.isPlay) return
      if (this.type === 1) {
        // 更大
        this.musicUrl = '/static/game_assets/audio/game157/title1.mp3'
      } else {
        // 更小
        this.musicUrl = '/static/game_assets/audio/game157/title2.mp3'
      }
      this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnd() {
      this.isPlay = false
    },

    confirm() {
      this.pause()
      if (this.answer === this.currect.answer) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      this.store = this.succesNum * 10
      if (this.number >= 10) {
        this.submit()
      } else {
        this.answer = 0
        this.startProcess()
      }
    },

    submit() {
      this.isStop = true
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    stop() {
      if (this.isPlay) this.pause()
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    again() {
      this.number = 0
      this.second = 0
      this.store = 0
      this.errorNum = 0
      this.succesNum = 0
      this.isStop = false
      this.isPlay = false
      this.answer = 0
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game158-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game157/bg.png");
  }

  .game-synopsis {

    .synopsis-content {
      padding-top: 20px;
      margin: 0;
      font-size: 30px;
      line-height: 42px;
      font-weight: 400;
      color: #A83A01;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .top {
      position: absolute;
      top: 36px;
      width: 1720px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .top-left {
        height: 105px;
        padding: 0 40px 0 12px;
        display: inline-flex;
        align-items: center;
        background: #57A8BB;
        border-radius: 53px;

        .icon {
          width: 90px;
          height: 90px;
          cursor: pointer;
        }

        .text {
          display: inline-block;
          padding: 0 23px;
          font-size: 48px;
          line-height: 105px;
          font-weight: 500;
          color: #fff;
        }
      }

      .top-right {
        width: 720px;
        height: 90px;
        padding: 0 35px;
        background: rgba(0,0,0,0.5800);
        border-radius: 45px;
        display: inline-flex;
        justify-content: space-between;
        align-items: center;

        .text-item {
          display: inline-block;
          font-size: 28px;
          line-height: 90px;
          color: #fff;
        }
      }
    }

    .content {
      position: relative;
      width: 1222px;
      height: 450px;
      margin-left: 160px;
      margin-bottom: 48px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .content-item {
        width: 1222px;
        height: 194px;
        padding: 0 48px 16px 28px;
        display: inline-flex;
        flex-wrap: wrap;
        background: #FFFFFF;
        border-radius: 19px;
        border: 2px solid #0BAFFC;
        cursor: pointer;

        .item {
          width: 72px;
          height: 72px;
          margin-left: 20px;
          margin-top: 12px;
          border-radius: 36px;
          border: 2px solid #fff;
        }
      }

      .choose-item {
        background: #0BAFFC;
      }
    }

    .btn-group {
      position: absolute;
      bottom: 82px;
      width: 1554px;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px;
        height: 84px;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px;
          height: 84px;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px 0 21px 0;
          font-size: 38px;
          line-height: 53px;
          text-align: center;
          color: #A83A01;
        }
      }
    }

    .judge {
      position: absolute;
      width: 460px;
      height: 460px;

      .img {
        position: absolute;
        width: 460px;
      }
    }
  }
}
</style>
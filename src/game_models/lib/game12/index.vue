<template>
  <div class="game12-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPage title="数字注意-找不同" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、这是一组针对数字进行的注意训练。</p>
        <p class="synopsis-content">2、找不同：屏幕上有两组数字和字母，其中一个数字或字母并不相同，请找出这个不同的数字或字母。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <img class="icon-img1" src="/static/game_assets/common/stop.png" @click="stop">
      <div class="icon-img">
        <img src="/static/game_assets/common/clock.png" />
        <span>{{ time }}</span>
      </div>

      <div class="content">
        <div class="left">
          <div :class="['item', 'item' + (index + 1), isCorrect && answer.includes(item) ? 'item-border' : '']" v-for="(item, index) in questions1" :key="item + '-1'">
            <img class="img" :src="`/static/game_assets/game12/card_${item}.png`" @click="chooseItem(item)">
          </div>
        </div>

        <div class="right">
          <div :class="['item', 'item' + (index + 1), isCorrect && answer.includes(item) ? 'item-border' : '']" v-for="(item, index) in questions2" :key="item + '-2'">
            <img class="img" :src="`/static/game_assets/game12/card_${item}.png`" @click="chooseItem(item)">
          </div>
        </div>
      </div>

      <!-- <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">
      </div> -->
    </div>

    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'

export default {
  name: 'game12',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      musicUrl: '/static/game_assets/audio/error_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      questions1: [],
      questions2: [],
      answer: [],
      isCorrect: false,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time() {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play() {
      this.$refs.music.play()
    },

    pause() {
      this.$refs.music.pause()
    },

    start() {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess() {
      const arr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
      this.answer = api.getRandomArray(arr, 2)
      const list = arr.filter(item => !this.answer.includes(item))
      this.questions1 = api.shuffle(list.concat([this.answer[0]]))
      const index = this.questions1.indexOf(this.answer[0])
      this.questions2 = JSON.parse(JSON.stringify(this.questions1))
      this.questions2[index] = this.answer[1]
    },

    chooseItem(item) {
      if (this.isCorrect) return
      if (!this.answer.includes(item)) {
        this.errorNum++
        this.play()
        return
      }

      this.succesNum++
      this.number++
      this.isCorrect = true

      setTimeout(() => {
        if (this.number >= 3) {
          this.pause()
          this.isStop = true
          this.store = parseInt(100 / (this.succesNum + this.errorNum) * this.succesNum)
          this.infos[0].value = this.second
          this.infos[1].value = this.succesNum
          this.infos[2].value = this.errorNum
          this.status = 4
          this.params = {
            id: this.info.id,
            grade: '',
            time: this.second,
            totalPoints: this.store
          }
        } else {
          this.answer = []
          this.questions1 = []
          this.questions2 = []
          this.isCorrect = false
          this.startProcess()
        }
      }, 800)
    },

    stop() {
      this.pause()
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answer = []
      this.questions1 = []
      this.questions2 = []
      this.isCorrect = false
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game12-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game12/bg.png");
  }

  .game-synopsis {
    width: 787px;
    height: 484px;
    margin: 34px;
    padding: 33px 30px;
    background: #fff;
    border: 2px solid #7BBD41;
    border-radius: 36px;

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #5E381F;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 18px;
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 400;
      color: #5E381F;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    width: 100%;

    .icon-img {
      position: absolute;
      top: 55px;
      right: 200px;
      width: 230px;
      height: 76px;
      padding: 4px 11px;
      border: 1px solid #37B982;
      border-radius: 38px;
      font-size: 0;

      img {
        width: 58px;
        height: 58px;
        vertical-align: middle;
      }

      span {
        display: inline-block;
        padding-left: 20px;
        font-size: 32px;
        line-height: 58px;
        font-weight: 500;
        vertical-align: middle;
        user-select: none;
      }
    }

    .icon-img1 {
      position: absolute;
      top: 30px;
      left: 100px;
      width: 270px;
      height: 115px;
      cursor: pointer;
    }

    .content {
      position: absolute;
      top: 132px;
      left: 0;
      height: 790px;
      width: 100%;
      display: flex;
      padding: 0 20px;

      .left, .right {
        position: relative;
        flex: 1;
        padding: 0 10px;
      }

      .item {
        position: absolute;
        width: 154px;
        height: 154px;

        .img {
          position: absolute;
          top: 23px;
          left: 23px;
          width: 94px;
          height: 94px;
          cursor: pointer;
        }
      }

      .item-border {
        border: 4px solid #FF9610;
        border-radius: 50%;
      }

      .item1, .item3, .item7, .item14, .item20 {
        transform: rotate(20deg);
        animation: swing1 1.5s .15s linear infinite;
      }

      .item5, .item11, .item18, .item22, .item25 {
        transform: rotate(45deg);
        animation: swing2 1.5s .15s linear infinite;
      }

      .item2, .item13, .item19, .item24, .item26 {
        transform: rotate(-20deg);
        animation: swing3 1.5s .15s linear infinite;
      }

      .item4, .item9, .item15, .item16, .item23 {
        transform: rotate(-40deg);
        animation: swing4 1.5s .15s linear infinite;
      }

      .item6, .item8, .item10, .item12, .item17, .item21 {
        animation: swing5 1.5s .15s linear infinite;
      }

      .item1 {
        top: 20px;
        left: 34px;
      }

      .item2 {
        top: 144px;
        left: 12px;
      }

      .item3 {
        top: 278px;
        left: 25px;
      }

      .item4 {
        top: 420px;
        left: 50px;
      }

      .item5 {
        top: 600px;
        left: 13px;
      }

      .item6 {
        top: 31px;
        left: 186px;
      }

      .item7 {
        top: 173px;
        left: 170px;
      }

      .item8 {
        top: 300px;
        left: 200px;
      }

      .item9 {
        top: 450px;
        left: 218px;
      }

      .item10 {
        top: 52px;
        left: 389px;
      }

      .item11 {
        top: 200px;
        left: 340px;
      }

      .item12 {
        top: 340px;
        left: 358px;
      }

      .item13 {
        top: 510px;
        left: 369px;
      }

      .item14 {
        top: 620px;
        left: 285px;
      }

      .item15 {
        top: 18px;
        left: 510px;
      }

      .item16 {
        top: 154px;
        left: 540px;
      }

      .item17 {
        top: 288px;
        left: 523px;
      }

      .item18 {
        top: 489px;
        left: 533px;
      }

      .item19 {
        top: 630px;
        left: 510px;
      }

      .item20 {
        top: 16px;
        left: 670px;
      }

      .item21 {
        top: 134px;
        left: 660px;
      }

      .item22 {
        top: 288px;
        left: 668px;
      }

      .item23 {
        top: 412px;
        left: 690px;
      }

      .item24 {
        top: 530px;
        left: 666px;
      }

      .item25 {
        top: 672px;
        left: 720px;
      }

      .item26 {
        top: 650px;
        left: 187px;
      }
    }

    .footer {
      position: absolute;
      bottom: 44px;
      left: 64px;

      .img1 {
        width: 270px;
        height: 115px;
        cursor: pointer;
      }
    }
  }
}

@keyframes swing1{
  0% {
    transform: rotate(20deg); 
  }
  10% { 
	  transform: rotate(-10deg); 
	} 
	20% { 
	  transform: rotate(-30deg); 
	} 
	30% { 
	  transform: rotate(-10deg); 
	} 
	40% { 
	  transform: rotate(10deg); 
	} 
	50%, 100% { 
	  transform: rotate(20deg); 
	}
}

@keyframes swing2{
  0% {
    transform: rotate(45deg); 
  }
  10% { 
	  transform: rotate(15deg); 
	} 
	20% { 
	  transform: rotate(-15deg); 
	} 
	30% { 
	  transform: rotate(-25deg); 
	} 
	40% { 
	  transform: rotate(10deg); 
	} 
	50%, 100% { 
	  transform: rotate(45deg); 
	}
}

@keyframes swing3{
  0% {
    transform: rotate(-20deg); 
  }
  10% { 
	  transform: rotate(10deg); 
	} 
	20% { 
	  transform: rotate(30deg); 
	} 
	30% { 
	  transform: rotate(10deg); 
	} 
	40% { 
	  transform: rotate(-10deg); 
	} 
	50%, 100% { 
	  transform: rotate(-20deg); 
	}
}

@keyframes swing4{
  0% {
    transform: rotate(-40deg); 
  }
  10% { 
	  transform: rotate(-15deg); 
	} 
	20% { 
	  transform: rotate(15deg); 
	} 
	30% { 
	  transform: rotate(25deg); 
	} 
	40% { 
	  transform: rotate(-10deg); 
	} 
	50%, 100% { 
	  transform: rotate(-40deg); 
	}
}

@keyframes swing5{ 
	10% { 
	  transform: rotate(15deg); 
	} 
	20% { 
	  transform: rotate(-10deg); 
	} 
	30% { 
	  transform: rotate(5deg); 
	} 
	40% { 
	  transform: rotate(-5deg); 
	} 
	50%,100% { 
	  transform: rotate(0deg); 
	} 
}
</style>
<template>
  <div class="result-page">
    <div class="content">
      <div class="main">
        <img class="content-bg" src="/static/game_assets/game219/end_bg.png">
        <div class="top">
          <span class="number">{{score}}</span>
          <span class="text">分</span>
        </div>
        <div class="bottom">
          <p class="info" v-for="(item, index) in info" :key="index">
            <span class="text">
              {{item.key}}：
            </span>
            <span class="num">
              {{item.value}}
            </span>
          </p>
        </div>
      </div>
    </div>
    <div class="footer">
      <img class="img" src="/static/game_assets/game219/btn_again.png" @click="again">
      <img class="img" src="/static/game_assets/game219/btn_next.png" @click="next">
    </div>
  </div>
</template>

<script>
import api from '../../utils/common.js'

export default {
  name: 'resultPage',

  props: {
    score: {
      type: Number,
      default: 0
    },

    info: {
      type: Array,
      default: () => []
    },

    params: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {

    }
  },

  methods: {
    again() {
      this.$emit('again')
    },

    next() {
      api.save(this.params).then(res => {
        if(res.result && res.result.id){
          window.location.href = window.location.origin + `/games/${res.result.nextPath}?id=${res.result.id}`
            // this.$router.replace(`/games/${res.result.nextPath}?id=${res.result.id}`)
        }else{
            this.$router.push({
              name: 'dashboard'
            })
            this.$notification['success']({
              message: '训练完成',
              description: '认知训练已完成'
            })
        }
      })
      // this.$router.go(-1)
    }
  },

  watch: {
    info: {
      handler(newValue) {
        newValue.forEach(item => {
          if (item.key === '训练时长') {
            if (typeof item.value === 'string') return
            let m = parseInt(item.value / 60)
            let s = item.value % 60

            item.value = m ? m + '分' + s + '秒' : s + '秒'
          }
        })
      },

      deep: true,
      immediate:true
    }
  }
}
</script>

<style lang="scss" scoped>
.result-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .content {
    flex: 1;
    display: inline-flex;
    align-items: center;
    padding-bottom: 36px;

    .main {
      position: relative;
      width: 1104px;
      height: 824px;
      padding: 423px 230px 0 230px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1104px;
        height: 824px;
      }

      .top {
        position: relative;
        text-align: center;

        .number {
          display: inline-block;
          font-size: 109px;
          font-weight: 600;
          line-height: 86px;
          font-family: zihunzhuimengti;
          color: #fff;
          user-select: none;
        }

        .text {
          position: relative;
          top: -10px;
          display: inline-block;
          padding-left: 17px;
          font-size: 48px;
          line-height: 67px;
          color: #fff;
          user-select: none;
        }
      }

      .bottom {
        position: relative;
        padding: 70px 0;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        .info {
          display: flex;
          align-items: flex-end;
          width: 320px;
          padding-left: 36px;
          padding-bottom: 30px;
          margin: 0;

          .text {
            font-size: 30px;
            line-height: 30px;
            font-weight: 600;
            color: #fff;
            user-select: none;
          }
          
          .num {
            font-size: 40px;
            line-height: 40px;
            font-weight: 600;
            color: #fff;
            user-select: none;
          }
        }
      }
    }
  }

  .footer {
    width: 960px;
    display: inline-flex;
    justify-content: space-between;

    .img {
      width: 330px;
      height: 176px;
      cursor: pointer;
    }
  }
}
</style>
<template>
  <div class="setting-page">
    <template v-if="!show">
      <div class="mark">
        <img class="bg" src="/static/game_assets/game8/mark.png">
        <span class="text">？</span>
      </div>

      <div class="content-warp">
        <div class="content">
          <img class="bg" src="/static/game_assets/game8/content_bg.png">
          <div class="main">
            <p class="title">训练说明</p>
            <slot></slot>
          </div>
        </div>
      </div>
      
      <div class="footer-warp">
        <div class="footer">
          <div class="btn" @click="quit">
            <img class="img" src="/static/game_assets/game8/yellow_bg.png">
            <span class="text">训练主页</span>
          </div>
          
          <div class="btn" @click="start">
            <img class="img" src="/static/game_assets/game8/yellow_bg.png">
            <span class="text">开始训练</span>
          </div>
        </div>
      </div>
    </template>
    <countdown :show.sync="show" />
  </div>
</template>

<script>
import countdown from './countdown.vue'

export default {
  name: 'settingPage',

  components: {
    [countdown.name]: countdown
  },

  props: {
    title: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      show: false
    }
  },

  methods: {
    quit() {
      this.$router.go(-1)
    },

    start() {
      this.show = true
      this.$emit('start')
    }
  },

  watch: {
    show(newValue) {
      if (!newValue) {
        this.$emit('challenge')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.setting-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  // align-items: center;

  .mark {
    position: relative;
    padding-top: 45px;
    padding-right: 60px;
    text-align: right;

    .bg {
      position: absolute;
      top: 45px;
      right: 60px;
      width: 126px;
      height: 88px;
    }

    .text {
      position: relative;
      display: inline-block;
      padding: 10px 18px 18px 0;
      font-size: 60px;
      line-height: 60px;
      font-weight: 500;
      color: #A83A01;
      text-align: center;
      user-select: none;
    }
  }
  
  .content-warp {
    width: 100%;
    height: 708px;
    padding-bottom: 32px;

    .content {
      position: relative;
      width: 1200px;
      height: 678px;
      margin: 0 auto;

      .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1200px;
        height: 678px;
      }

      .main {
        position: relative;
        padding: 35px 82px 0 71px;

        .title {
          padding-bottom: 26px;
          font-size: 60px;
          line-height: 60px;
          font-weight: bold;
          text-align: center;
          color: #A83A01;
          user-select: none;
        }
      }
    }
  }

  .footer-warp {
    width: 100%;
    padding-bottom: 45px;
    text-align: center;

    .footer {
      width: 682px;
      display: inline-flex;
      justify-content: space-between;
      margin: 0 auto;

      .btn {
        position: relative;
        width: 294px;
        height: 83px;
        cursor: pointer;

        .img {
          position: absolute;
          top: 0;
          left: 0;
          width: 294px;
          height: 83px;
        }

        .text {
          position: relative;
          display: inline-block;
          padding: 10px 0 20px 0;
          font-size: 38px;
          line-height: 54px;
          color: #A83A01;
          text-align: center;
          user-select: none;
        }
      }
    }
  }
}
</style>
<template>
  <div class="setting3-page">
    <template v-if="!show">
      <img v-if="titleImg" class="title-img" :src="titleImg" alt="" />
      <p v-else class="title">{{title}}</p>
      <slot></slot>
      <div class="footer">
        <img class="img" :src="`/static/game_assets/${game || 'game219'}/btn_back.png`" @click="quit">
        <img class="img" :src="`/static/game_assets/${game || 'game219'}/btn_start.png`" @click="start">
      </div>
    </template>
    <countdown :show.sync="show" />
  </div>
</template>

<script>
import countdown from './countdown.vue'

export default {
  name: 'settingPage',

  components: {
    [countdown.name]: countdown
  },

  props: {
    title: {
      type: String,
      default: ''
    },

    titleImg: {
      type: String,
      default: ''
    },

    showAnimation: {
      type: <PERSON><PERSON>an,
      default: true
    },

    game: {
      type: String,
      default: ''
    }
  },

  data () {
    return {
      show: false
    }
  },

  methods: {
    quit () {
      this.$router.go(-1)
    },

    start () {
      if (this.showAnimation) this.show = true
      this.$emit('start')
    }
  },

  watch: {
    show (newValue) {
      if (!newValue) {
        this.$emit('challenge')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.setting3-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .title {
    position: absolute;
    top: 77px;
    margin: 0;
    font-size: 50px;
    line-height: 70px;
    font-weight: 600;
    color: #5e381f;
    text-align: center;
    user-select: none;
  }

  .title-img {
    position: absolute;
    top: 68px;
    // width: 420px;
    height: 98px;
    transform: scale(0.85);
  }

  .footer {
    position: absolute;
    bottom: 18px;
    width: 960px;
    display: inline-flex;
    justify-content: space-between;
    transform: scale(0.85);

    .img {
      height: 160px;
      cursor: pointer;
    }
  }
}
</style>
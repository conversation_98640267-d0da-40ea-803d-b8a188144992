<template>
  <div class="result-page">
    <div class="content">
      <div class="main">
        <img class="content-bg" src="/static/game_assets/common/score_bg.png">
        <div class="top">
          <span class="number">{{store}}</span>
          <span class="text">分</span>
        </div>
        <div class="bottom">
          <p class="info" v-for="(item, index) in info" :key="index">
            {{item.key}}：{{item.value}}
          </p>
        </div>
      </div>
    </div>
    <div class="footer">
      <img class="left" src="/static/game_assets/common/again.png" @click="again">
      <img class="right" src="/static/game_assets/common/next.png" @click="next">
    </div>
  </div>
</template>

<script>
import api from '../../utils/common.js'

export default {
  name: 'resultPage',

  props: {
    store: {
      type: Number,
      default: 0
    },

    info: {
      type: Array,
      default: () => []
    },

    params: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {

    }
  },

  methods: {
    again() {
      this.$emit('again')
    },

    next() {
      api.save(this.params).then(res => {
        if(res.result && res.result.id){
          window.location.href = window.location.origin + `/games/${res.result.nextPath}?id=${res.result.id}`
            // this.$router.replace(`/games/${res.result.nextPath}?id=${res.result.id}`)
        }else{
            this.$router.push({
              name: 'dashboard'
            })
            this.$notification['success']({
              message: '训练完成',
              description: '认知训练已完成'
            })
        }
      })
      // this.$router.go(-1)
    }
  },

  watch: {
    info: {
      handler(newValue) {
        newValue.forEach(item => {
          if (item.key === '训练时长') {
            if (typeof item.value === 'string') return
            let m = parseInt(item.value / 60)
            let s = item.value % 60

            item.value = m ? m + '分' + s + '秒' : s + '秒'
          }
        })
      },

      deep: true,
      immediate:true
    }
  }
}
</script>

<style lang="scss" scoped>
.result-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .content {
    flex: 1;
    display: inline-flex;
    align-items: center;
    padding-top: 60px;

    .main {
      position: relative;
      width: 1148px;
      height: 683px;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1148px;
        height: 683px;
      }

      .top {
        position: relative;
        padding-top: 90px;
        text-align: center;

        .number {
          display: inline-block;
          font-size: 170px;
          font-weight: 600;
          line-height: 238px;
          color: transparent;
          background: radial-gradient(circle at -80px -50px, #FF7B00 0%, #E82F2F 100%);
          -webkit-background-clip: text;
          user-select: none;
        }

        .text {
          position: relative;
          top: -10px;
          display: inline-block;
          padding-left: 17px;
          font-size: 48px;
          line-height: 67px;
          color: transparent;
          background: radial-gradient(circle at 20px 30px, #FFC500 0%, #FF5353 100%);
          -webkit-background-clip: text;
          user-select: none;
        }
      }

      .bottom {
        position: relative;
        padding: 60px 240px 0 268px;
        display: flex;
        flex-wrap: wrap;

        .info {
          width: 320px;
          padding-left: 20px;
          padding-bottom: 16px;
          margin: 0;
          font-size: 32px;
          line-height: 45px;
          font-weight: 600;
          color: #5E381F;
          user-select: none;
        }
      }
    }
  }

  .footer {
    width: 800px;
    display: flex;
    justify-content: space-between;
    padding-bottom: 100px;

    img {
      width: 270px;
      height: 115px;
      cursor: pointer;
    }
  }
}
</style>
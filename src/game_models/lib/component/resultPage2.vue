<template>
  <div class="result-page">
    <div class="content">
      <div class="main">
        <img class="content-bg" src="/static/game_assets/game8/content_bg.png">

        <div class="title">
          <img class="title-bg" src="/static/game_assets/game8/title_bg.png">
          <p class="text">你的分数</p>
        </div>

        <div class="top">
          <span class="number">{{store}}</span>
          <span class="text">分</span>
        </div>

        <div class="bottom">
          <p class="info" v-for="(item, index) in info" :key="index">
            {{item.key}}：{{item.value}}
          </p>
        </div>
      </div>
    </div>

    <div class="footer">
      <div class="btn" @click="quit">
        <img class="img" src="/static/game_assets/game8/yellow_bg.png">
        <span class="text">下一题</span>
      </div>

      <div class="btn" @click="again">
        <img class="img" src="/static/game_assets/game8/yellow_bg.png">
        <span class="text">再来一次</span>
      </div>
    </div>
  </div>
</template>

<script>
import api from '../../utils/common.js'

export default {
  name: 'resultPage',

  props: {
    store: {
      type: Number,
      default: 0
    },

    info: {
      type: Array,
      default: () => []
    },

    params: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {

    }
  },

  methods: {
    quit() {
      api.save(this.params).then(res => {
        if(res.result && res.result.id){

          window.location.href = window.location.origin + `/games/${res.result.nextPath}?id=${res.result.id}`
            // this.$router.replace(`/games/${res.result.nextPath}?id=${res.result.id}`)
        }else{
            this.$router.push({
              name: 'dashboard'
            })
            this.$notification['success']({
              message: '训练完成',
              description: '认知训练已完成'
            })
        }
      })
      // this.$router.go(-1)
    },    
    
    again() {
      this.$emit('again')
    }
  },

  watch: {
    info: {
      handler(newValue) {
        newValue.forEach(item => {
          if (item.key === '训练时长') {
            if (typeof item.value === 'string') return
            let m = parseInt(item.value / 60)
            let s = item.value % 60

            item.value = m ? m + '分' + s + '秒' : s + '秒'
          }
        })
      },

      deep: true,
      immediate:true
    }
  }
}
</script>

<style lang="scss" scoped>
.result-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .content {
    flex: 1;
    display: inline-flex;
    align-items: center;
    padding-top: 40px;

    .main {
      position: relative;
      width: 1253px;
      height: 754px;

      .content-bg {
        position: absolute;
        top: 46px;
        left: 0;
        width: 1253px;
        height: 708px;
      }

      .title {
        position: absolute;
        top: 0;
        left: 346px;
        width: 547px;
        height: 124px;

        .title-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 547px;
          height: 124px;
        }

        .text {
          position: relative;
          margin: 0;
          padding: 24px 0 40px 0;
          font-size: 44px;
          line-height: 60px;
          text-align: center;
          font-weight: bold;
          color: #fff;
          user-select: none;
        }
      }

      .top {
        position: relative;
        padding-top: 220px;
        text-align: center;

        .number {
          display: inline-block;
          font-size: 140px;
          font-weight: bold;
          line-height: 140px;
          color: #A83A01;
          font-family: PingFang-SC-Bold, PingFang-SC;
          user-select: none;
        }

        .text {
          position: relative;
          top: -20px;
          display: inline-block;
          padding-left: 15px;
          font-size: 58px;
          line-height: 58px;
          font-weight: bold;
          color: #A83A01;
          user-select: none;
        }
      }

      .bottom {
        position: relative;
        padding: 60px 225px 0 307px;
        display: flex;
        flex-wrap: wrap;

        .info {
          width: 360px;
          padding-left: 20px;
          padding-bottom: 16px;
          margin: 0;
          font-size: 32px;
          line-height: 45px;
          font-weight: 600;
          color: #B86628;
          user-select: none;
        }
      }
    }
  }

  .footer {
    width: 800px;
    display: flex;
    justify-content: space-between;
    padding-bottom: 84px;

    .btn {
      position: relative;
      width: 294px;
      height: 83px;
      text-align: center;
      cursor: pointer;

      .img {
        position: absolute;
        top: 0;
        left: 0;
        width: 294px;
        height: 83px;
      }

      .text {
        position: relative;
        display: inline-block;
        padding: 10px 0 20px 0;
        font-size: 38px;
        line-height: 54px;
        color: #A83A01;
        text-align: center;
        user-select: none;
      }
    }
  }
}
</style>
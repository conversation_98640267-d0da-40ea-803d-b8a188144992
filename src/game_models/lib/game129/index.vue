<template>
  <div class="game129-page">
    <div class="page-bg"></div>
    <!-- <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio> -->
    <settingPage title="色彩斑斓" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、通过学习并对比来掌握对颜色的认知。</p>
        <p class="synopsis-content">2、根据提示选择与其他不同的颜色。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="top">
        <div class="top-left">
          <div class="left-item">
            <img class="item-icon" src="/static/game_assets/common/clock.png" />
            <span class="item-text">时间：{{countdown}}</span>
          </div>

          <div class="left-item">
            <img class="item-icon" src="/static/game_assets/game83/icon_2.png" />
            <span class="item-text">分数：{{store}}</span>
          </div>
        </div>
      </div>

      <div class="content">
        <div :class="['content-item', 'content-item-' + number]" :style="{'background': currentIndex === item ? current[1] : current[0]}" v-for="item in (number + 1) * (number + 1)" :key="item + 'item'" @click="chooseItem(item)"></div>
      </div>

      <img class="footer-img" src="/static/game_assets/common/stop.png" @click="stop" />
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'
 
export default {
  name: 'game129',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 1,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isPlay: false,
      isRePlay: false,
      show: false,
      index: 0,
      current: [],
      currentIndex: 0,
      countdown: 60,

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.countdown = 60 - this.second

        if (this.countdown <= 0) {
          this.submit()
        } else {
          this.timing()
        }
      }, 1000)
    },

    play() {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess() {
      this.current = api.getRandomArray(api.getRandomArray(data.data, 1)[0], 2)
      this.currentIndex = api.randomNum(1, (this.number + 1) * (this.number + 1))

      if (this.index < 5) {
        this.index++
      } else {
        this.number < 9 ? this.number++ : ''
        this.index = 0
      }
    },

    chooseItem(item) {
      if (item === this.currentIndex) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      this.store = (2 * this.succesNum) > 100 ? 100 : (2 * this.succesNum)
      this.startProcess()
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isStop = true
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again() {
      this.number = 1
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.current = []
      this.currentIndex = 0
      this.index = 0
      this.countdown = 60
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game129-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game83/bg.png");
  }

  .game-synopsis {
    width: 707px;
    height: 444px;
    margin: 34px;
    padding: 33px 30px;
    background: #FFFEF3;
    border: 2px solid #014747;
    border-radius: 36px;

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #5E381F;
    }

    .synopsis-content {
      padding-top: 18px;
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 400;
      color: #5E381F;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      padding: 30px 230px 0 70px;
      display: flex;
      justify-content: space-between;

      .top-left {
        display: flex;
        justify-content: space-between;
        width: 480px;

        .left-item {
          font-size: 0;
          .item-icon {
            width: 60px;
            height: 60px;
            vertical-align: middle;
          }

          .item-text {
            display: inline-block;
            padding-left: 14px;
            font-size: 37px;
            line-height: 60px;
            font-weight: 600;
            vertical-align: middle;
          }
        }
      }

      .top-right {
        display: flex;
        justify-content: space-between;
        width: 380px;

        .right-icon {
          width: 60px;
          height: 60px;
        }
      }
    }

    .content {
      position: relative;
      width: 726px;
      height: 726px;
      margin-left: 64px;
      margin-top: 62px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;

      .content-item {
        border-radius: 2px;
        border-style: solid;
        border-color: #fff;
        cursor: pointer;
      }

      .content-item-1 {
        width: 337px;
        height: 337px;
        margin: 13px;
        border-width: 3px;
      }

      .content-item-2 {
        width: 216px;
        height: 216px;
        margin: 13px;
        border-width: 3px;
      }

      .content-item-3 {
        width: 155px;
        height: 155px;
        margin: 13px;
        border-width: 3px;
      }

      .content-item-4 {
        width: 121px;
        height: 121px;
        margin: 12px;
        border-width: 2px;
      }

      .content-item-5 {
        width: 97px;
        height: 97px;
        margin: 12px;
        border-width: 2px;
      }

      .content-item-6 {
        width: 81px;
        height: 81px;
        margin: 11px;
        border-width: 2px;
      }

      .content-item-7 {
        width: 68px;
        height: 68px;
        margin: 11px;
        border-width: 1px;
      }

      .content-item-8 {
        width: 60px;
        height: 60px;
        margin: 10px;
        border-width: 1px;
      }

      .content-item-9 {
        width: 52px;
        height: 52px;
        margin: 10px;
        border-width: 1px;
      }
    }

    .footer-img {
      position: absolute;
      left: 200px;
      bottom: 30px;
      height: 115px;
      cursor: pointer;
    }

    .footer {
      position: absolute;
      bottom: 50px;
      display: flex;
      justify-content: space-between;
      width: 1790px;

      .img {
        height: 115px;
        cursor: pointer;
      }
    }
  }
}
</style>
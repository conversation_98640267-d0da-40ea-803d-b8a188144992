<template>
  <div class="game206-page">
    <div class="page-bg"></div>
    <img class="left-icon" src="/static/game_assets/game206/icon1.png" />
    <img class="right-icon" src="/static/game_assets/game206/icon2.png" />
    <settingPage title="四则估算" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、本项目考查四则运算的结果估算。</p>
        <p class="synopsis-content">2、训练对象:数字的四舍五入缺失/困难者。</p>
        <p class="synopsis-content">3、一组10题答对9-10题的您可以终止这一项目训练。</p>
        <p class="synopsis-content">4、有错误者复习一下答案，再训练1-2次。您第三次练习后仍有错误，也不再重新练习，而是做其他项目，次日接着训练本一项目。</p>

        <div class="synopsis-choose">
          <p class="choose-title">训练方式:</p>

          <div class="item">
            <div class="item-icon" @click="setChoose(1)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 1" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">加法</span>
          </div>
          
          <div class="item">
            <div class="item-icon" @click="setChoose(2)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 2" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">减法</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(3)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 3" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">乘法</span>
          </div>

          <div class="item">
            <div class="item-icon" @click="setChoose(4)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="type === 4" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>
            <span class="item-text">除法</span>
          </div>
        </div>
      </div>
    </settingPage>

    <div class="game-content" v-if="status === 3">
      <img class="right-icon" src="/static/game_assets/game206/icon3.png" />

      <div class="title">
        <img class="title-bg" src="/static/game_assets/game206/title_bg.png" />
        <span class="title-text">四则估算</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game206/bg.png" />
        <div class="content-top">{{currect.num1}} {{type === 1 ? '+' : type === 2 ? '-' : type === 3 ? '×' : '÷'}} {{currect.num2}} ≈ </div>

        <div class="content-bottom">
          <div class="bottom-item" v-for="item in currect.answerList" :key="item + 'item'" @click="chooseItem(item)">
            <img class="num-bg" src="/static/game_assets/game206/btn_bg_1.png" />
            <img v-if="answer === item" class="num-bg" src="/static/game_assets/game206/btn_bg_2.png" />
            <span :class="['num']">{{item}}</span>
          </div>
        </div>
      </div>

      <div class="footer">
        <div class="footer-left">
          <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
          <img v-show="showNext" class="img" src="/static/game_assets/common/next.png" @click="toNext" />
          <img v-show="answer !== null && !showNext" class="img" src="/static/game_assets/common/confirm.png" @click="confirm" />
        </div>

        <img v-show="showNext" class="img" src="/static/game_assets/common/reset.png" @click="reset" />
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
// 10道题 
// 加法：100-500 100-500 减法：100-999 100-999 乘法：10-100 10-100 除法：100-999 0-100

export default {
  name: 'game206',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,
      showNext: false,

      type: 1, // 训练模式
      answer: null,
      currect: {
        num1: 0,
        num2: 0,
        answer: 0,
        answerList: []
      },  
      

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess() {
      this.currect.answerList = []
      if (this.type === 1) {
        this.currect.num1 = api.randomNum(100, 500)
        this.currect.num2 = api.randomNum(100, 500)
        const mid = this.currect.num1 + this.currect.num2

        this.currect.answer = api.randomNum(mid - 50, mid + 50)
        let list = [[200, 300], [301, 400], [401, 500], [501, 600], [601, 700], [701, 800], [801, 900], [900, 1000]]
        list = list.filter(item => !((this.currect.answer - item[0] < 100) && (item[1] - this.currect.answer < 100)))
        const arr = api.getRandomArray(list, 3)
        for (let i = 0; i < 3; i++) {
          const num = api.randomNum(arr[i][0], arr[i][1])
          if ((this.currect.answer - num > 50) || (num - this.currect.answer > 100)) {
            this.currect.answerList.push(num)
          } else {
            if (num >= mid) {
              this.currect.answerList.push(mid + 103)
            } else {
              this.currect.answerList.push(mid - 103)
            }
          }
        }
      } else if (this.type === 2) {
        const num1 = api.randomNum(100, 999)
        const num2 = api.randomNum(100, 999)
        this.currect.num1 = num1 > num2 ? num1 : num2
        this.currect.num2 = num1 <= num2 ? num1 : num2
        const mid = this.currect.num1 - this.currect.num2

        this.currect.answer = api.randomNum(mid - 50, mid + 50)
        let list = [[1, 100], [101, 200], [201, 300], [301, 400], [401, 500], [501, 600], [601, 700], [701, 800], [801, 900]]
        list = list.filter(item => !((this.currect.answer - item[0] < 100) && (item[1] - this.currect.answer < 100)))
        const arr = api.getRandomArray(list, 3)
        for (let i = 0; i < 3; i++) {
          const num = api.randomNum(arr[i][0], arr[i][1])
          if ((this.currect.answer - num > 50) || (num - this.currect.answer > 100)) {
            this.currect.answerList.push(num)
          } else {
            if (num >= mid) {
              this.currect.answerList.push(mid + 103)
            } else {
              this.currect.answerList.push(mid - 103)
            }
          }
        }
      } else if (this.type === 3) {
        this.currect.num1 = api.randomNum(10, 33)
        this.currect.num2 = api.randomNum(10, 33)
        const mid = this.currect.num1 * this.currect.num2

        this.currect.answer = api.randomNum(mid - 50, mid + 50)
        let list = [[100, 200], [201, 300], [301, 400], [401, 500], [501, 600], [601, 700], [701, 800], [801, 900], [900, 1000]]
        list = list.filter(item => !((this.currect.answer - item[0] < 100) && (item[1] - this.currect.answer < 100)))
        const arr = api.getRandomArray(list, 3)
        for (let i = 0; i < 3; i++) {
          const num = api.randomNum(arr[i][0], arr[i][1])
          if ((this.currect.answer - num > 50) || (num - this.currect.answer > 100)) {
            this.currect.answerList.push(num)
          } else {
            if (num >= mid) {
              this.currect.answerList.push(mid + 103)
            } else {
              this.currect.answerList.push(mid - 103)
            }
          }
        }
      } else {
        this.currect.num1 = api.randomNum(100, 999)
        this.currect.num2 = api.randomNum(10, 100)
        this.currect.answer = Math.round(this.currect.num1 / this.currect.num2)
        for (let i = 0; i < 3; i++) {
          const num = api.randomNum(1, 99)
          if (this.currect.answerList.includes(num) || this.currect.answer === num) {
            this.currect.answerList.push(num + 5)
          } else {
            this.currect.answerList.push(num)
          }
        }
      }

      this.currect.answerList.push(this.currect.answer)
      this.currect.answerList = api.shuffle(this.currect.answerList)
    },

    setChoose(item) {
      this.type = item
    },

    chooseItem(item) {
      if (this.isShowCorrect || this.isShowError) return
      this.answer = item
    },

    toNext() {
      this.number++
      this.answer = null
      this.showNext = false
      this.isShowCorrect = false
      this.isShowError = false

      if (this.number >= 10) {
        this.submit()
      } else {
        this.startProcess()
      }
    },

    confirm() {
      if (this.answer === this.currect.answer) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }

      this.showNext = true
    },

    reset() {
      this.answer = null
      this.showNext = false
      if (this.isShowCorrect) this.succesNum--
      if (this.isShowError) this.errorNum--
      this.isShowCorrect = false
      this.isShowError = false
    },

    stop() {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isStop = true
      this.store = this.succesNum * 10
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.showNext = false
      this.answer = null
      this.currect = {
        num1: 0,
        num2: 0,
        answer: 0,
        answerList: []
      },
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game206-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game126/bg.png");
  } 
  
  .left-icon {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 413px;
  }

  .right-icon {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 426px;
  }

  .game-synopsis {
    width: 1605px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
    }

    .synopsis-choose {
      display: flex;
      margin-top: 25px;
      padding: 13px 30px;
      border: 1px solid #C49E68;
      border-radius: 10px;
      background: #FFF6E3;

      .choose-title {
        margin: 0;
        font-size: 36px;
        line-height: 41px;
        font-weight: 600;
        color: #414043;
      }

      .item {
        position: relative;
        display: inline-flex;
        align-items: center;
        padding-left: 25px;

        .item-icon {
          position: relative;
          width: 41px;
          height: 35px;
          display: inline-flex;
          justify-content: center;
          cursor: pointer;

          .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 41px;
          }

          .icon1 {
            position: absolute;
            width: 41px;
          }
        }

        .item-text {
          position: relative;
          top: 2px;
          display: inline-block;
          padding-left: 10px;
          font-size: 36px;
          line-height: 41px;
          font-weight: 600;
          color: #414043;
        }
      }
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .right-icon {
      position: absolute;
      right: 0;
      bottom: 90px;
      width: 254px;
    }

    .title {
      position: absolute;
      top: 0;
      width: 932px;
      height: 125px;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px;
        height: 125px;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 28px 0 56px 0;
        font-size: 36px;
        line-height: 36px;
        text-align: center;
        color: #1B1D2D;
      }
    }

    .content {
      position: relative;
      width: 1473px;
      height: 792px;
      padding: 270px 110px 0 110px;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1473px;
      }

      .content-top {
        position: relative;
        padding-bottom: 70px;
        font-size: 98px;
        line-height: 98px;
        font-weight: 500;
        text-align: center;
        color: #1B1D2D;
      }

      .content-bottom {
        position: relative;
        width: 100%;
        display: inline-flex;
        justify-content: space-between;

        .bottom-item {
          position: relative;
          width: 299px;
          height: 127px;
          cursor: pointer;

          .num-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 299px;
          }

          .num {
            position: relative;
            display: block;
            padding: 27px 0 40px 0;
            font-size: 60px;
            line-height: 60px;
            text-align: center;
            font-weight: 600;
            color: #F8F9FF;
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 72px;
      display: flex;
      justify-content: space-between;
      width: 1480px;

      .footer-left {
        width: 620px;
        display: inline-flex;
        justify-content: space-between;
      }

      .img {
        height: 115px;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px;
      margin-bottom: 40px;
    }
  }
}
</style>
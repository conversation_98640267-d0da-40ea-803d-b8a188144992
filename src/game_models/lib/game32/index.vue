<template>
  <div class="game32-page">
    <div class="page-bg" :class="status === 3 ? 'page-bg2': 'page-bg1'"></div>
    <settingPage title="定向训练-地点" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、定向能力是指一个人自己对时间、地点、人物以及对自己本身状态的人事能力。</p>
        <p class="synopsis-content">2、回答问题后，按“提交”键。答案的右侧出现“√”和“×”。需要人工判断对错，即分别点击“√”或“×”。</p>
        <p class="synopsis-content">3、地点定向训练中，在要求您填写家庭住址或现在住址时，选择正确的地点即可。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <div class="content-title">
          <p class="text">{{title}}</p>

          <div class="icon" v-if="isShowJudge">
            <img class="icon1" src="/static/game_assets/game32/correct.png" />
            <img class="icon2" src="/static/game_assets/game32/error.png" />
          </div>
        </div>

        <template v-if="number < 3">
          <div class="content-item">
            <a-select class="left-item" :value="province.value" dropdownClassName="drop-item" placeholder="请选择省" @change="handleChangeProvince">
              <a-select-option v-for="item in provinceList" :key="item.code" :value="item.code">{{item.name}}</a-select-option>
            </a-select>

            <div class="right-item" v-if="isShowJudge">
              <div class="item" @click="clickCorrect(1)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="province.isCorrect" class="icon1" src="/static/game_assets/game32/correct.png" />
              </div>

              <div class="item" @click="clickError(1)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="province.isCorrect === false" class="icon2" src="/static/game_assets/game32/error.png" />
              </div>
            </div>
          </div>

          <div class="content-item">
            <a-select class="left-item" :value="city.value" dropdownClassName="drop-item" placeholder="请选择市" @change="handleChangeCity">
              <a-select-option v-for="item in cityList" :key="item.code" :value="item.code">{{item.name}}</a-select-option>
            </a-select>

            <div class="right-item" v-if="isShowJudge">
              <div class="item" @click="clickCorrect(2)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="city.isCorrect" class="icon1" src="/static/game_assets/game32/correct.png" />
              </div>

              <div class="item" @click="clickError(2)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="city.isCorrect === false" class="icon2" src="/static/game_assets/game32/error.png" />
              </div>
            </div>
          </div>
        </template>

        <template v-else>
          <div class="content-item">
            <a-select class="left-item" :value="floor.value" dropdownClassName="drop-item" placeholder="请选择楼号" @change="handleChangeFloor">
              <a-select-option v-for="item in floorList" :key="item.code" :value="item.code">{{item.name}}</a-select-option>
            </a-select>

            <div class="right-item" v-if="isShowJudge">
              <div class="item" @click="clickCorrect(1)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="floor.isCorrect" class="icon1" src="/static/game_assets/game32/correct.png" />
              </div>

              <div class="item" @click="clickError(1)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="floor.isCorrect === false" class="icon2" src="/static/game_assets/game32/error.png" />
              </div>
            </div>
          </div>

          <div class="content-item">
            <a-select class="left-item" :value="floorNum.value" dropdownClassName="drop-item" placeholder="请选择楼层" @change="handleChangeFloorNum">
              <a-select-option v-for="item in floorNumList" :key="item.code" :value="item.code">{{item.name}}</a-select-option>
            </a-select>

            <div class="right-item" v-if="isShowJudge">
              <div class="item" @click="clickCorrect(2)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="floorNum.isCorrect" class="icon1" src="/static/game_assets/game32/correct.png" />
              </div>

              <div class="item" @click="clickError(2)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="floorNum.isCorrect === false" class="icon2" src="/static/game_assets/game32/error.png" />
              </div>
            </div>
          </div>

          <div class="content-item">
            <a-select class="left-item" :value="ward.value" dropdownClassName="drop-item" placeholder="请选择病房" @change="handleChangeWard">
              <a-select-option v-for="item in wardList" :key="item.code" :value="item.code">{{item.name}}</a-select-option>
            </a-select>

            <div class="right-item" v-if="isShowJudge">
              <div class="item" @click="clickCorrect(3)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="ward.isCorrect" class="icon1" src="/static/game_assets/game32/correct.png" />
              </div>

              <div class="item" @click="clickError(3)">
                <img class="bg" src="/static/game_assets/game32/icon.png" />
                <img v-if="ward.isCorrect === false" class="icon2" src="/static/game_assets/game32/error.png" />
              </div>
            </div>
          </div>
        </template>
        
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">

        <img v-if="isShowSubmit" class="img3" src="/static/game_assets/common/submit.png" @click="submit">
        <img v-if="isShowFinish" class="img1" src="/static/game_assets/common/finish.png" @click="finish" />
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import cityData from "./data/city.json"
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
 
export default {
  name: 'game32',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowJudge: false,
      isShowSubmit: false,
      isShowFinish: false,
      title: '家庭住址',
      provinceList: [],
      cityList: [],
      province: {
        value: undefined,
        isCorrect: null
      },
      city: {
        value: undefined,
        isCorrect: null
      },
      floorList: [
        {
          code: 1,
          name: 'A1'
        },
        {
          code: 2,
          name: 'A2'
        },
        {
          code: 3,
          name: 'A3'
        },
        {
          code: 4,
          name: 'A4'
        },
        {
          code: 5,
          name: 'B1'
        },
        {
          code: 6,
          name: 'B2'
        },
        {
          code: 7,
          name: 'B3'
        },
        {
          code: 8,
          name: 'B4'
        },
        {
          code: 9,
          name: 'C1'
        },
        {
          code: 10,
          name: 'C2'
        },
        {
          code: 11,
          name: 'C3'
        },
        {
          code: 12,
          name: 'C4'
        },
      ],
      floorNumList: [
        {
          code: 13,
          name: '1楼层'
        },
        {
          code: 14,
          name: '2楼层'
        },
        {
          code: 15,
          name: '3楼层'
        },
        {
          code: 16,
          name: '4楼层'
        },
        {
          code: 17,
          name: '5楼层'
        },
        {
          code: 18,
          name: '6楼层'
        },
      ],
      wardList: [
        {
          code: 19,
          name: '1病房'
        },
        {
          code: 20,
          name: '2病房'
        },
        {
          code: 21,
          name: '3病房'
        },
        {
          code: 22,
          name: '4病房'
        },
        {
          code: 23,
          name: '5病房'
        },
        {
          code: 24,
          name: '6病房'
        },
        {
          code: 25,
          name: '7病房'
        },
        {
          code: 26,
          name: '8病房'
        },
      ],
      floor: {
        value: undefined,
        isCorrect: null
      },
      floorNum: {
        value: undefined,
        isCorrect: null
      },
      ward: {
        value: undefined,
        isCorrect: null
      },


      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time() {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess() {
      this.number++
      if (this.number < 3) {
        this.provinceList = cityData.provinces
      }
    },

    handleChangeProvince(value) {
      this.province.value = value
      this.cityList = this.provinceList.filter(item => item.code === value)[0].cities
    },

    handleChangeCity(value) {
      this.city.value = value
      if (value) {
        this.isShowSubmit = true
      }
    },

    handleChangeFloor(value) {
      this.floor.value = value

      if (this.floor.value && this.floorNum.value && this.ward.value) this.isShowSubmit = true
    },

    handleChangeFloorNum(value) {
      this.floorNum.value = value

      if (this.floor.value && this.floorNum.value && this.ward.value) this.isShowSubmit = true
    },

    handleChangeWard(value) {
      this.ward.value = value

      if (this.floor.value && this.floorNum.value && this.ward.value) this.isShowSubmit = true
    },

    clickCorrect(index) {
      if (this.number < 3) {
        if (index === 1) {
          this.province.isCorrect = true
        } else if (index === 2) {
          this.city.isCorrect = true
        }

        if (this.city.isCorrect !== null && this.province.isCorrect !== null) this.isShowFinish = true
      } else {
        if (index === 1) {
          this.floor.isCorrect = true
        } else if (index === 2) {
          this.floorNum.isCorrect = true
        } else if (index === 3) {
          this.ward.isCorrect = true
        }

        if (this.floor.isCorrect !== null && this.floorNum.isCorrect !== null && this.ward.isCorrect !== null) this.isShowFinish = true
      }
    },

    clickError(index) {
      if (this.number < 3) {
        if (index === 1) {
          this.province.isCorrect = false
        } else if (index === 2) {
          this.city.isCorrect = false
        }

        if (this.city.isCorrect !== null && this.province.isCorrect !== null) this.isShowFinish = true
      } else {
        if (index === 1) {
          this.floor.isCorrect = false
        } else if (index === 2) {
          this.floorNum.isCorrect = false
        } else if (index === 3) {
          this.ward.isCorrect = false
        }

        if (this.floor.isCorrect !== null && this.floorNum.isCorrect !== null && this.ward.isCorrect !== null) this.isShowFinish = true
      }
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isShowSubmit = false
      this.isShowJudge = true
    },

    finish() {
      if (this.province.isCorrect) this.succesNum++
      if (this.city.isCorrect) this.succesNum++
      if (this.floor.isCorrect) this.succesNum++
      if (this.floorNum.isCorrect) this.succesNum++
      if (this.ward.isCorrect) this.succesNum++

      this.province.value = undefined
      this.province.isCorrect = null
      this.city.value = undefined
      this.city.isCorrect = null
      this.floor.value = undefined
      this.floor.isCorrect = null
      this.floorNum.value = undefined
      this.floorNum.isCorrect = null
      this.ward.value = undefined
      this.ward.isCorrect = null
      this.isShowJudge = false
      this.isShowSubmit = false
      this.isShowFinish = false

      if (this.number === 1) {
        this.title = '现在住址'
      } else if (this.number === 2) {
        this.title = '住院病房位置'
      } else {
        this.isStop = true
        this.errorNum = 7 - this.succesNum
        this.store = this.succesNum > 6 ? 100 : this.succesNum * 15
        this.infos[0].value = this.second
        this.infos[1].value = this.succesNum
        this.infos[2].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: this.level,
          time: this.second,
          totalPoints: this.store
        }
      }
      this.startProcess()
    },

    again() {
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.number = 0
      this.title = '家庭住址'
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game32-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background-image: url("/static/game_assets/game32/bg1.png");
  }
  .page-bg2 {
    background: url("/static/game_assets/game32/bg.png") center center no-repeat;
    background-size: cover;
  }

  .game-synopsis {
    width: 1576px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 644px;
      height: 656px;
      padding-left: 55px;

      .content-title {
        display: flex;
        justify-content: space-between;

        .text {
          width: 470px;
          margin: 0;
          padding-bottom: 45px;
          font-size: 50px;
          font-weight: 600;
          text-align: center;
          color: #414043;
          user-select: none;
        }

        .icon {
          width: 88px;
          padding-top: 30px;
          display: inline-flex;
          justify-content: space-between;

          .icon1 {
            height: 35px;
          }

          .icon2 {
            height: 35px;
          }
        }
      }

      .content-item {
        display: flex;
        justify-content: space-between;

        .left-item {
          width: 470px;
          height: 82px;
          margin-bottom: 10px;

          .ant-select-selection {
            width: 470px;
            height: 82px;
            background: #F3F2F8;
            border: 2px solid #CED0B7;
            border-radius: 15px;

            font-size: 50px;
            line-height: 82px;
            color: #5D5E66;
            user-select: none;
          }

          .ant-select-selection__rendered {
            height: 78px;
          }

          .ant-select-selection__placeholder, .ant-select-selection-selected-value {
            top: 4px;
            height: 78px;
            font-size: 50px;
            line-height: 78px;
            user-select: none;
          }

          .anticon {
            font-size: 24px;
            color: #9BAA26;
            user-select: none;
          }
        }

        .right-item {
          width: 93px;
          display: inline-flex;
          justify-content: space-between;
          align-items: center;

          .item {
            position: relative;
            width: 41px;
            height: 35px;
            display: inline-flex;
            justify-content: center;
            cursor: pointer;

            .bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 41px;
              height: 35px;
            }

            .icon1 {
              position: absolute;
              width: 41px;
              height: 35px;
            }

            .icon2 {
              position: absolute;
              height: 35px;
            }
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 47px;
      display: flex;
      justify-content: space-between;
      width: 1509px;

      .left {
        width: 566px;
        display: inline-flex;
        justify-content: space-between;
      }

      .right {
        width: 564px;
        display: inline-flex;
        justify-content: space-between;
      }

      .img1 {
        width: 270px;
        height: 115px;
        cursor: pointer;
      }

      .img3 {
        width: 267px;
        height: 115px;
        cursor: pointer;
      }
    }
  }
}

.drop-item {
  background: #F3F2F8;
  
  .ant-select-dropdown-menu-item {
    padding: 0 30px;
    font-size: 50px;
    line-height: 80px;
    color: #5D5E66;
    user-select: none;
  }

  .ant-select-dropdown-menu {
    padding: 0
  }

  .ant-select-dropdown-menu-item-selected {
    background: #9BAA26;
    color: #fff;
    border-radius: 8px;
  }
}
</style>
<template>
  <div class="game33-page">
    <div class="page-bg" :class="status === 3 ? 'page-bg2': 'page-bg1'"></div>
    <settingPage title="定向训练-时间" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、定向能力是指一个人自己对时间、地点、人物以及对自己本身状态的人事能力。</p>
        <p class="synopsis-content">2、回答问题后，按“提交”键。答案的右侧出现“√”和“×”。需要人工判断对错，即分别点击“√”或“×”。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="content">
        <div class="content-title">
          <p class="text">{{title}}</p>

          <div class="icon" v-if="isShowJudge">
            <img class="icon1" src="/static/game_assets/game32/correct.png" />
            <img class="icon2" src="/static/game_assets/game32/error.png" />
          </div>
        </div>

        <div class="content-item">
          <a-select class="left-item" :value="year.value" dropdownClassName="drop-item" placeholder="请选择年份" @change="handleChangeYear">
            <a-select-option v-for="item in yearList" :key="item.code" :value="item.code">{{item.name}}</a-select-option>
          </a-select>

          <div class="right-item" v-if="isShowJudge">
            <div class="item" @click="clickCorrect(1)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="year.isCorrect" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>

            <div class="item" @click="clickError(1)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="year.isCorrect === false" class="icon2" src="/static/game_assets/game32/error.png" />
            </div>
          </div>
        </div>

        <div class="content-item">
          <a-select class="left-item" :value="month.value" dropdownClassName="drop-item" placeholder="请选择月份" @change="handleChangeMonth">
            <a-select-option v-for="item in 12" :key="item + 'month'" :value="item">{{item + '月份'}}</a-select-option>
          </a-select>

          <div class="right-item" v-if="isShowJudge">
            <div class="item" @click="clickCorrect(2)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="month.isCorrect" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>

            <div class="item" @click="clickError(2)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="month.isCorrect === false" class="icon2" src="/static/game_assets/game32/error.png" />
            </div>
          </div>
        </div>

        <div class="content-item">
          <a-select class="left-item" :value="day.value" dropdownClassName="drop-item" placeholder="请选择日期" @change="handleChangeDay">
            <a-select-option v-for="item in 31" :key="item + 'day'" :value="item">{{item + '日'}}</a-select-option>
          </a-select>

          <div class="right-item" v-if="isShowJudge">
            <div class="item" @click="clickCorrect(3)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="day.isCorrect" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>

            <div class="item" @click="clickError(3)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="day.isCorrect === false" class="icon2" src="/static/game_assets/game32/error.png" />
            </div>
          </div>
        </div>

        <div class="content-item">
          <a-select class="left-item" :value="week.value" dropdownClassName="drop-item" placeholder="请选择星期" @change="handleChangeWeek">
            <a-select-option v-for="item in weekList" :key="item.code" :value="item.code">{{item.name}}</a-select-option>
          </a-select>

          <div class="right-item" v-if="isShowJudge">
            <div class="item" @click="clickCorrect(4)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="week.isCorrect" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>

            <div class="item" @click="clickError(4)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="week.isCorrect === false" class="icon2" src="/static/game_assets/game32/error.png" />
            </div>
          </div>
        </div>

        <div class="content-item" v-if="number < 2">
          <a-select class="left-item" :value="times.value" dropdownClassName="drop-item" placeholder="请选择时间" @change="handleChangeTime">
            <a-select-option v-for="item in 24" :key="item + 'times'" :value="item">{{item > 12 ? ('下午' + (item - 12) + '点') : ('上午' + item + '点')}}</a-select-option>
          </a-select>

          <div class="right-item" v-if="isShowJudge">
            <div class="item" @click="clickCorrect(5)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="times.isCorrect" class="icon1" src="/static/game_assets/game32/correct.png" />
            </div>

            <div class="item" @click="clickError(5)">
              <img class="bg" src="/static/game_assets/game32/icon.png" />
              <img v-if="times.isCorrect === false" class="icon2" src="/static/game_assets/game32/error.png" />
            </div>
          </div>
        </div>
      </div>

      <div class="footer">
        <img class="img1" src="/static/game_assets/common/stop.png" @click="stop">

        <img v-if="isShowSubmit" class="img3" src="/static/game_assets/common/submit.png" @click="submit">
        <img v-if="isShowFinish" class="img1" src="/static/game_assets/common/finish.png" @click="finish" />
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import date from "./data/date.json"
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
 
export default {
  name: 'game33',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowJudge: false,
      isShowSubmit: false,
      isShowFinish: false,
      title: '今天的日期',
      yearList: [],
      weekList: [],
      year: {
        value: undefined,
        isCorrect: null
      },
      month: {
        value: undefined,
        isCorrect: null
      },
      day: {
        value: undefined,
        isCorrect: null
      },
      week: {
        value: undefined,
        isCorrect: null
      },
      times: {
        value: undefined,
        isCorrect: null
      },

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess() {
      this.number++
      this.yearList = date.year
      this.weekList = date.week
    },

    handleChangeYear(value) {
      this.year.value = value
      if (this.number < 2) {
        if (this.year.value && this.month.value && this.day.value && this.week.value && this.times.value) this.isShowSubmit = true
      } else {
        if (this.year.value && this.month.value && this.day.value && this.week.value) this.isShowSubmit = true
      }
    },

    handleChangeMonth(value) {
      this.month.value = value
      if (this.number < 2) {
        if (this.year.value && this.month.value && this.day.value && this.week.value && this.times.value) this.isShowSubmit = true
      } else {
        if (this.year.value && this.month.value && this.day.value && this.week.value) this.isShowSubmit = true
      }
    },

    handleChangeDay(value) {
      this.day.value = value
      if (this.number < 2) {
        if (this.year.value && this.month.value && this.day.value && this.week.value && this.times.value) this.isShowSubmit = true
      } else {
        if (this.year.value && this.month.value && this.day.value && this.week.value) this.isShowSubmit = true
      }
    },

    handleChangeWeek(value) {
      this.week.value = value
      if (this.number < 2) {
        if (this.year.value && this.month.value && this.day.value && this.week.value && this.times.value) this.isShowSubmit = true
      } else {
        if (this.year.value && this.month.value && this.day.value && this.week.value) this.isShowSubmit = true
      }
    },

    handleChangeTime(value) {
      this.times.value = value
      if (this.number < 2) {
        if (this.year.value && this.month.value && this.day.value && this.week.value && this.times.value) this.isShowSubmit = true
      } else {
        if (this.year.value && this.month.value && this.day.value && this.week.value) this.isShowSubmit = true
      }
    },

    clickCorrect(index) {
      if (index === 1) {
        this.year.isCorrect = true
      } else if (index === 2) {
        this.month.isCorrect = true
      } else if (index === 3) {
        this.day.isCorrect = true
      } else if (index === 4) {
        this.week.isCorrect = true
      } else if (index === 5) {
        this.times.isCorrect = true
      }

      if (this.number < 2 && this.year.isCorrect !== null && this.month.isCorrect !== null && this.day.isCorrect !== null && this.week.isCorrect !== null && this.times.isCorrect !== null) this.isShowFinish = true
      if (this.number >= 2 && this.year.isCorrect !== null && this.month.isCorrect !== null && this.day.isCorrect !== null && this.week.isCorrect !== null) this.isShowFinish = true
    },

    clickError(index) {
      if (index === 1) {
        this.year.isCorrect = false
      } else if (index === 2) {
        this.month.isCorrect = false
      } else if (index === 3) {
        this.day.isCorrect = false
      } else if (index === 4) {
        this.week.isCorrect = false
      } else if (index === 5) {
        this.times.isCorrect = false
      }

      if (this.number < 2 && this.year.isCorrect !== null && this.month.isCorrect !== null && this.day.isCorrect !== null && this.week.isCorrect !== null && this.times.isCorrect !== null) this.isShowFinish = true
      if (this.number >= 2 && this.year.isCorrect !== null && this.month.isCorrect !== null && this.day.isCorrect !== null && this.week.isCorrect !== null) this.isShowFinish = true
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isShowSubmit = false
      this.isShowJudge = true
    },

    finish() {
      if (this.year.isCorrect) this.succesNum++
      if (this.month.isCorrect) this.succesNum++
      if (this.day.isCorrect) this.succesNum++
      if (this.week.isCorrect) this.succesNum++
      if (this.times.isCorrect) this.succesNum++

      this.year.value = undefined
      this.year.isCorrect = null
      this.month.value = undefined
      this.month.isCorrect = null
      this.day.value = undefined
      this.day.isCorrect = null
      this.week.value = undefined
      this.week.isCorrect = null
      this.times.value = undefined
      this.times.isCorrect = null
      this.isShowJudge = false
      this.isShowSubmit = false
      this.isShowFinish = false

      if (this.number === 1) {
        this.title = '住院的日期'
      } else {
        this.isStop = true
        this.errorNum = 9 - this.succesNum
        this.store = this.succesNum ? this.succesNum * 10 + 10 : 0
        this.infos[0].value = this.second
        this.infos[1].value = this.succesNum
        this.infos[2].value = this.errorNum
        this.status = 4
        this.params = {
          id: this.info.id,
          grade: this.level,
          time: this.second,
          totalPoints: this.store
        }
      }
      this.startProcess()
    },

    again() {
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.number = 0
      this.title = '今天的日期'
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game33-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background-image: url("/static/game_assets/game32/bg1.png");
  }
  .page-bg2 {
    background: url("/static/game_assets/game32/bg.png") center center no-repeat;
    background-size: cover;
  }

  .game-synopsis {
    width: 1576px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 644px;
      height: 656px;
      padding-left: 55px;

      .content-title {
        display: flex;
        justify-content: space-between;

        .text {
          width: 470px;
          margin: 0;
          padding-bottom: 45px;
          font-size: 50px;
          font-weight: 600;
          text-align: center;
          color: #414043;
          user-select: none;
        }

        .icon {
          width: 88px;
          padding-top: 30px;
          display: inline-flex;
          justify-content: space-between;

          .icon1 {
            height: 35px;
          }

          .icon2 {
            height: 35px;
          }
        }
      }

      .content-item {
        display: flex;
        justify-content: space-between;

        .left-item {
          width: 470px;
          height: 82px;
          margin-bottom: 10px;

          .ant-select-selection {
            width: 470px;
            height: 82px;
            background: #F3F2F8;
            border: 2px solid #CED0B7;
            border-radius: 15px;

            font-size: 50px;
            line-height: 82px;
            color: #5D5E66;
            user-select: none;
          }

          .ant-select-selection__rendered {
            height: 78px;
          }

          .ant-select-selection__placeholder, .ant-select-selection-selected-value {
            top: 4px;
            height: 78px;
            font-size: 50px;
            line-height: 78px;
            user-select: none;
          }

          .anticon {
            font-size: 24px;
            color: #9BAA26;
            user-select: none;
          }
        }

        .right-item {
          width: 93px;
          display: inline-flex;
          justify-content: space-between;
          align-items: center;

          .item {
            position: relative;
            width: 41px;
            height: 35px;
            display: inline-flex;
            justify-content: center;
            cursor: pointer;

            .bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 41px;
              height: 35px;
            }

            .icon1 {
              position: absolute;
              width: 41px;
              height: 35px;
            }

            .icon2 {
              position: absolute;
              height: 35px;
            }
          }
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 47px;
      display: flex;
      justify-content: space-between;
      width: 1509px;

      .left {
        width: 566px;
        display: inline-flex;
        justify-content: space-between;
      }

      .right {
        width: 564px;
        display: inline-flex;
        justify-content: space-between;
      }

      .img1 {
        width: 270px;
        height: 115px;
        cursor: pointer;
      }

      .img3 {
        width: 267px;
        height: 115px;
        cursor: pointer;
      }
    }
  }
}

.drop-item {
  background: #F3F2F8;
  
  .ant-select-dropdown-menu-item {
    padding: 0 30px;
    font-size: 50px;
    line-height: 80px;
    color: #5D5E66;
    user-select: none;
  }

  .ant-select-dropdown-menu {
    padding: 0
  }

  .ant-select-dropdown-menu-item-selected {
    background: #9BAA26;
    color: #fff;
    border-radius: 8px;
  }
}
</style>
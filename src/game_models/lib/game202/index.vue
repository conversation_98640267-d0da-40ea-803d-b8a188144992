<template>
  <div class="game202-page">
    <div class="page-bg"></div>
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <audio ref="music2" muted controls="controls" style="display:none">
      <source src="/static/game_assets/audio/error_audio.mp3" type="audio/mpeg" />
    </audio>
    <settingPage title="两数相加" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、根据提示，进行两数相加，在屏幕下方的键盘中选择相应的答案;</p>
        <p class="synopsis-content">2、训练结束会出现成绩单:</p>
        <p class="synopsis-content">3、本训练难度为A（简单）。</p>
      </div>
    </settingPage>

    <div class="game-content" v-if="status === 3">
      <div class="top">
        <img v-if="!isPlay" @click="play" class="top-icon" src="/static/game_assets/common/play.png" />
        <img v-else @click="pause" class="top-icon" src="/static/game_assets/common/pause.png" />
        <img @click="goHome" class="top-icon" src="/static/game_assets/common/home_icon.png" />
      </div>
      <div class="title">
        <img class="title-bg" src="/static/game_assets/game206/title_bg.png" />
        <span class="title-text">两数相加A（简单）</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game193/content_bg.png" />
        <img class="left-icon" src="/static/game_assets/game193/icon.png" />
        <img class="right-icon" src="/static/game_assets/game192/img2.png" />

        <div class="content-top">
          <span class="top-text">{{currect.num1}}</span>

          <span class="top-text">+</span>

          <span class="top-text">{{currect.num2}}</span>

          <span class="top-text">=</span>

          <div class="num-item">
            <img class="num-bg" src="/static/game_assets/game209/text_bg.png" />
            <span :class="[answer !== '' ? 'num' : 'symbol']">{{answer !== '' ? answer : '?'}}</span>
          </div>
        </div>

        <div class="content-bottom">
          <div class="bottom-left">
            <div class="left-item" v-for="item in 10" :key="item + 'btn'" @click="chooseItem(item)">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_1.png" />
              <span class="item-num">{{item % 10}}</span>
            </div>
          </div>

          <div class="bottom-right">
            <div class="right-item item1">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_2.png" />
              <span class="item-num">{{answer}}</span>
            </div>

            <div class="right-item item2" @click="reset">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_3.png" />
              <span class="item-num">X</span>
            </div>

            <div class="right-item item3" @click="confirm">
              <img class="item-bg" src="/static/game_assets/game209/btn_bg_4.png" />
              <span class="item-num">确定</span>
            </div>
          </div>
        </div>

        <div class="content-img store">
          <img class="item-bg" src="/static/game_assets/game196/item_bg_1.png" />
          <span class="item-text">题目分数</span>
          <span class="item-num">{{store}}</span>
        </div>

        <div class="content-img number">
          <img class="item-bg" src="/static/game_assets/game196/item_bg_2.png" />
          <span class="item-text">题目数量</span>
          <span class="item-num">{{number}}</span>
        </div>

        <div class="content-img time">
          <img class="item-bg" src="/static/game_assets/game196/item_bg_1.png" />
          <span class="item-text">训练用时</span>
          <span class="item-time">{{time}}</span>
        </div>
      </div>

      <div class="footer">
        <div class="footer-left">
          <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
        </div>
      </div>
    </div>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'
// 10道题 

export default {
  name: 'game202',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data() {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      number: 0,
      second: 0,
      store: 0,
      level: 1,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,

      type: 1, // 训练模式
      law: 1, // 考查法则
      answer: '',
      currect: {
        num1: 0,
        num2: 0,
        answer: 0,
      },  

      isStop: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  computed: {
    time() {
      let m = parseInt(this.second / 60)
      let s = this.second % 60
      m = m > 9 ? m : '0' + m
      s = s > 9 ? s : '0' + s

      return m + ' : ' + s
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    // TODO: 返回首页
		goHome() {
      this.pause()
			this.$router.go(-1)
		},

    play() {
      this.$refs.music.play()
      this.isPlay = true
    },

    playError() {
      this.$refs.music2.play()
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
      // this.timing()
      this.startProcess()
      this.play()
    },

    startProcess() {
      this.currect.num1 = api.randomNum(0, 10)
      this.currect.num2 = api.randomNum(0, 10)
      this.currect.answer = this.currect.num1 + this.currect.num2
      this.number++
    },

    chooseItem(item) {
      if (this.answer.length >= 3) return
      this.answer = this.answer + (item === 10 ? 0 : item).toString()
    },

    confirm() {
      if (!this.answer) {
        this.playError()
        return
      }
      if (Number(this.answer) === this.currect.answer) {
        this.succesNum++
      } else {
        this.errorNum++
      }
      this.store = this.succesNum * 10

      if (this.number < 10) {
        this.answer = ''
        this.startProcess()
      } else {
        this.submit()
      }
    },

    reset() {
      this.answer = ''
    },

    stop() {
      this.pause()
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
      this.play()
    },

    submit() {
      this.pause()
      this.isStop = true
      this.infos[0].value = this.level
      this.infos[1].value = this.second
      this.infos[2].value = this.succesNum
      this.infos[3].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: this.level,
        time: this.second,
        totalPoints: this.store
      }
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.answer = ''
      this.currect = {
        num1: 0,
        num2: 0,
        answer: 0,
      },
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game202-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game126/bg.png");
  } 

  .game-synopsis {
    width: 1605px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .top {
      position: absolute;
      top: 40px;
      left: 105px;
      width: 175px;
      display: flex;
      justify-content: space-between;

      .top-icon {
        width: 80px;
        cursor: pointer;
      }
    }

    .title {
      position: absolute;
      top: 0;
      width: 932px;
      height: 125px;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px;
        height: 125px;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 28px 0 56px 0;
        font-size: 36px;
        line-height: 36px;
        text-align: center;
        color: #1B1D2D;
      }
    }

    .content {
      position: relative;
      width: 1768px;
      height: 855px;
      margin-right: 150px;
      margin-top: 155px;
      padding: 187px 447px 160px 340px;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1768px;
      }

      .left-icon {
        position: absolute;
        bottom: 74px;
        left: 158px;
        width: 214px;
      }

      .right-icon {
        position: absolute;
        bottom: 19px;
        right: 10px;
        width: 1219px;
      }

      .content-top {
        position: relative;
        padding: 0 240px;
        display: flex;
        justify-content: space-between;

        .top-text {
          font-size: 96px;
          line-height: 166px;
          font-family: Impact;
          color: #312B4F;
        }

        .num-item {
          position: relative;
          width: 188px;
          height: 206px;

          .num-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 188px;
          }

          .num {
            position: relative;
            display: block;
            width: 100%;
            font-size: 96px;
            line-height: 166px;
            text-align: center;
            font-family: Impact;
            color: #312B4F;
          }

          .symbol {
            position: relative;
            display: block;
            width: 100%;
            font-size: 96px;
            line-height: 166px;
            text-align: center;
            font-weight: 500;
            color: #312B4F;
          }
        }
      }

      .content-bottom {
        position: relative;
        width: 100%;
        padding-top: 25px;
        display: flex;

        .bottom-left {
          width: 557px;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;
          margin-right: -10px;

          .left-item {
            position: relative;
            width: 135px;
            height: 136px;
            margin: -12px -15px;
            cursor: pointer;

            .item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 135px;
            }

            .item-num {
              position: relative;
              display: block;
              font-size: 48px;
              line-height: 136px;
              text-align: center;
              color: #312B4F;
            }
          }
        }

        .bottom-right {
          width: 450px;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-content: space-between;

          .right-item {
            position: relative;

            .item-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%
            }

            .item-num {
              position: relative;
              display: block;
              font-size: 48px;
              line-height: 136px;
              text-align: center;
              color: #312B4F;
            }
          }

          .item1 {
            width: 291px;
            height: 136px;
            margin: -12px -12px -12px 0;
          }

          .item2 {
            width: 190px;
            height: 136px;
            margin: -12px;
            cursor: pointer;
          }

          .item3 {
            width: 462px;
            height: 136px;
            margin: -12px -12px -12px 0;
            cursor: pointer;
          }
        }
      }

      .content-img {
        position: absolute;
        right: 4px;
        width: 369px;
        height: 219px;
        padding: 28px 42px;
        display: flex;

        .item-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 369px;
        }

        .item-text {
          position: relative;
          display: inline-block;
          width: 36px;
          font-size: 36px;
          line-height: 40px;
          text-align: center;
          font-weight: 500;
          color: #253000;
        }

        .item-num {
          position: relative;
          display: block;
          width: 100%;
          height: 100%;
          padding-left: 37px;
          font-size: 92px;
          line-height: 170px;
          text-align: center;
          color: #312B4F;
          font-family: Impact;
        }
        
        .item-time {
          position: relative;
          display: block;
          width: 100%;
          height: 100%;
          padding-left: 37px;
          font-size: 48px;
          line-height: 170px;
          text-align: center;
          color: #312B4F;
          font-family: Impact;
        }
      }

      .store {
        top: 51px;
      }

      .number {
        top: 278px;
      }

      .time {
        top: 506px;
      }
    }

    .footer {
      position: absolute;
      bottom: 72px;
      display: flex;
      justify-content: space-between;
      width: 1480px;

      .footer-left {
        width: 620px;
        display: inline-flex;
        justify-content: space-between;
      }

      .img {
        height: 115px;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px;
      margin-bottom: 40px;
    }
  }
}
</style>
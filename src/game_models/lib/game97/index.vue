<template>
  <div class="game97-page">
    <div class="page-bg"></div>
		<settingPage title="残字识别-数字" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、请尽快识别屏幕上出现的数字。</p>
        <p class="synopsis-content">2、如识别，请点击屏幕上的数字，然后选择数字。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="content" v-show="answerStatus === 'question'">
        <img class="content-bg1" src="/static/game_assets/game95/content_bg1.png" />
        <img class="content-icon" src="/static/game_assets/game95/icon.png">
        <div class="content-main">
          <img class="item-bg" src="/static/game_assets/game95/item_bg.png" />
          <div class="item-text">{{current.question}}</div>
        </div>
        <div class="content-bar">
          <img class="bar-bg" src="/static/game_assets/game95/bar_bg.png" />
          <span class="bar-text">剩余时间:</span>
          <div class="bar-box">
            <div :class="['bar', isShowBar && 'line']"></div>
          </div>
        </div>
      </div>

      <div class="content" v-show="answerStatus === 'answer'">
        <img class="content-bg2" src="/static/game_assets/game95/content_bg2.png" />
        <div class="content-answer">
          <div :class="['content-item', choose === item && 'content-choose-item']" v-for="item in current.answerList" :key="item + 'text'" @click="chooseItem(item)">{{item}}</div>
        </div>
      </div>

      <div class="footer">
        <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
        <img class="img" v-if="answerStatus === 'answer' && choose" src="/static/game_assets/common/confirm.png" @click="confirm">
        <img class="img" v-if="answerStatus === 'question'" src="/static/game_assets/common/choose.png" @click="startAnswer">
      </div>

      <img v-if="isShowCorrect" class="center-icon" src="/static/game_assets/game56/correct_icon.png">
      <img v-if="isShowError" class="center-icon" src="/static/game_assets/game56/error_icon.png">
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from '../game95/data/data.json'
// 每轮游戏4个题目


export default {
  name: 'game97',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isShowCorrect: false,
      isShowError: false,
      isShowBar: false,
      questionList: [],
			current: {},
      choose: '',
      answerStatus: 'question',
      timer: null,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.status = 3
      const list = api.getRandomArray(data.number, 4)
      list.forEach(item => {
        const li1 = data.number.filter(it => it !== item)
        let li2 = api.getRandomArray(li1, 8)
        this.questionList.push({
          question: item,
          answerList: api.shuffle(li2.concat(item)),
          img: ''
        })
      })
      this.startProcess()
    },

    startProcess() {
      this.current = this.questionList[this.number]
      this.number++
      
      setTimeout(() => {
        this.isShowBar = true
      }, 50)
      this.startTimer()
    },

    startTimer() {
      this.timer = setTimeout(() => {
        this.answerStatus = 'answer'
        this.isShowBar = false
      }, 5050)
    },

    startAnswer() {
      clearTimeout(this.timer)
      this.answerStatus = 'answer'
      this.isShowBar = false
    },

    chooseItem(item) {
      this.choose = item
    },

    confirm() {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      if (this.choose === this.current.question) {
        this.succesNum++
        this.isShowCorrect = true
      } else {
        this.errorNum++
        this.isShowError = true
      }

      setTimeout(() => {
        this.choose = ''
        this.isShowCorrect = false
        this.isShowError = false

        if (this.number < 4) {
          this.answerStatus = 'question'
          this.startProcess()
        } else {
          this.submit()
        }
      }, 800)
    },

    stop() {
      if (this.isShowCorrect || this.isShowError) return
      this.isStop = true
      this.show = true
      if (this.answerStatus === 'question') {
        clearTimeout(this.timer)
        this.isShowBar = false
      }
    },

    // 继续游戏, 继续计时
    cancel() {
      if (this.answerStatus === 'question') {
        this.isShowBar = true
        this.startTimer()
        return
      }
      this.isStop = false
      this.timing()
    },

    submit() {
			this.store = 25 * this.succesNum
			this.infos[0].value = this.second
			this.infos[1].value = this.succesNum
			this.infos[2].value = this.errorNum
			this.status = 4
			this.params = {
				id: this.info.id,
				grade: '',
				time: this.second,
				totalPoints: this.store
			}
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.choose = ''
			this.current = {}
      this.answerStatus = 'question'
      this.timer = null
			this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game97-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game95/bg.png");
  } 

  .game-synopsis {
    width: 1576px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      position: relative;
      width: 1920px;
      height: 1062px;
      margin-top: 18px;

      .content-bg1 {
        position: absolute;
        top: 42px;
        left: 0;
        width: 1920px;
      }

      .content-icon {
        position: absolute;
        top: 229px;
        left: 411px;
        width: 384px;
        z-index: 1;
      }

      .content-main {
        position: relative;
        padding: 139px 407px 80px 704px;

        .item-bg {
          position: absolute;
          top: 139px;
          left: 704px;
          width: 809px;
        }

        .item-text {
          position: relative;
          width: 809px;
          height: 504px;
          font-size: 300px;
          text-align: center;
          line-height: 500px;
          color: #fff;
        }
      }

      .content-bar {
        position: relative;
        display: flex;
        padding: 0 443px;

        .bar-bg {
          position: absolute;
          top: 0;
          right: 443px;
          width: 847px;
        }

        .bar-text {
          display: inline-block;
          padding-right: 28px;
          font-size: 37px;
          line-height: 70px;
          font-weight: 600;
          color: #fff;
        }

        .bar-box {
          position: relative;
          width: 847px;
          height: 70px;
          padding: 11px 15px 19px 14px;

          .bar {
            width: 0px;
            height: 100%;
            border-radius: 16px;
            background: #FF566B;
            transition: all 5s linear;
          }

          .line {
            width: 100%;
          }
        }
      }

      .content-bg2 {
        position: absolute;
        top: 0;
        left: 0;
        width: 1920px;
      }

      .content-answer {
        position: relative;
        height: 100%;
        padding: 162px 620px 270px 596px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;

        .content-item {
          width: 192px;
          height: 192px;
          border: 2px dashed rgba(250, 250, 255, 0.6);

          font-size: 111px;
          text-align: center;
          line-height: 172px;
          font-weight: 600;
          font-family: PingFang SC;
          color: #fff;
          cursor: pointer;
        }

        .content-choose-item {
          border: 2px solid #9BF584;
        }
      }
    }

    .footer {
      position: absolute;
      bottom: 73px;
      display: flex;
      justify-content: space-between;
      width: 1470px;
      padding-right: 38px;

      .img {
        height: 115px;
        cursor: pointer;
      }
    }

    .center-icon {
      position: absolute;
      width: 287px;
      margin-bottom: 40px;
      z-index: 99;
    }
  }
}
</style>
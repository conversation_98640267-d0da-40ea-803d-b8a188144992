<template>
  <div class="game34-page">
    <div class="page-bg" :class="status > 1 ? 'page-bg2': 'page-bg1'"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPage title="时钟转换--分配" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <div class="synopsis-content">
          <p>1、这是一项注意力保持的训练任务。</p>
          <p>2、这项任务包括计数钟声及钟表数量两项任务，通过边听边看的技术任务，训练您的注意分配能力。</p>
          <p>3、计数钟声任务：录音里传出几声钟表报时声音，就是几点。</p>
          <p>4、计数钟表数量任务：任务中有黄红两种颜色的钟表，要求您计数并比较黄红钟表数量的多少，如果红色钟表的个数多于黄色钟表，则提示为上午，反之，提示为下午。</p>
          <p>5、以上两个任务同时进行，即您在判断上下午的同时还要对钟表报时声进行计数，响几声就表示几点。在任务结束时，会有“几点钟”的问题提出，这时您可在所给的选项中选择正确答案。</p>
        </div>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="clock-content">
        <div class="clock" v-for="item in 5" :key="item">
          <img class="clock-shadow" src="/static/game_assets/game34/shadow.png" />
          <template v-if="randomArr.indexOf(item) === -1">
            <img class="clock-bg" src="/static/game_assets/game34/yellow_bg.png" />
            <img class="clock-point" src="/static/game_assets/game34/yellow_point.png" />
          </template>
          <template v-else>
            <img class="clock-bg" src="/static/game_assets/game34/red_bg.png" />
            <img class="clock-point" src="/static/game_assets/game34/red_point.png" />
          </template>
          <img class="hour-hand" src="/static/game_assets/game34/hour_hand.png" />
          <img class="minute-hand" src="/static/game_assets/game34/minute_hand.png" />
        </div>
      </div>

      <div class="keyboard" v-if="answerStatus === 'answer'">
        <div class="morning">
          <img class="morning-img" src="/static/game_assets/game34/morning.png" />
          <div class="right">
            <div class="btn" v-for="item in 12" :key="item" @click="select(1, item)">
              <img v-if="selectTime.hour === item && selectTime.period === 'am'" class="btn-bg" src="/static/game_assets/game34/btn_bg_2.png" />
              <img v-else class="btn-bg" src="/static/game_assets/game34/btn_bg_1.png" />
              <img class="btn-number" :src="`/static/game_assets/game34/btn_${item}.png`" />
            </div>
          </div>
        </div>
        <div class="afternoon">
          <img class="afternoon-img" src="/static/game_assets/game34/afternoon.png" />
          <div class="right">
            <div class="btn" v-for="item in 12" :key="item" @click="select(2, item)">
              <img v-if="selectTime.hour === item && selectTime.period === 'pm'" class="btn-bg" src="/static/game_assets/game34/btn_bg_2.png" />
              <img v-else class="btn-bg" src="/static/game_assets/game34/btn_bg_1.png" />
              <img class="btn-number" :src="`/static/game_assets/game34/btn_${item}.png`" />
            </div>
          </div>
        </div>
      </div>

      <div class="btn-group">
        <img class="left" src="/static/game_assets/game34/stop.png" @click="stop" />
        <div class="right">
          <img v-if="answerStatus === 'wait'" class="start" src="/static/game_assets/game34/start.png" @click="startProcess" />
          <img v-if="answerStatus === 'result'" class="reset" src="/static/game_assets/game34/reset.png" @click="reset" />
          <img v-if="answerStatus === 'result'" class="continue" src="/static/game_assets/game34/continue.png" @click="continueTrain" />
          <img v-if="answerStatus === 'answer'" class="submit" src="/static/game_assets/game34/submit.png" @click="submit" />
        </div>
      </div>
    </div>

    <maskPage v-if="answerStatus === 'judge'">
      <div class="mask-content">
        <img v-if="!isCorrect" class="img" src="/static/game_assets/game34/error.png" />
        <img v-else class="img" src="/static/game_assets/game34/success.png" />
      </div>
    </maskPage>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import maskPage from '../component/mask.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'
// 游戏分为3个等级，每轮游戏3个回合
// 初级：1-4点
// 中级：5-8点
// 高级：9-12点

export default {
  name: 'game34',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [maskPage.name]: maskPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm
  },

  data() {
    return {
      musicUrl: '',
      number: 0,
      level: 1,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      isStop: false,
      isPlay: false,
      isContinue: false,
      show: false,
      timeNum: 0, // 几点
      bellNum: 0, // 报时数量
      answerStatus: 'wait', // wait--等待状态，play--播报题目，answer--答题，judge--判定对错，result--结果
      randomArr: [], // 红色闹钟的个数和位置（大于2为上午，反之为下午）
      selectTime: {
        hour: 0,
        period: ''
      },
      isCorrect: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 0
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.isStop = false
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play(url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$nextTick(() => {
        this.$refs.music.play()
        this.isPlay = true
      })
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    handleEnd() {
      this.isPlay = false
      if (this.answerStatus !== 'play') return

      this.bellNum++
      if (this.bellNum < this.timeNum) {
        this.play('/static/game_assets/audio/bell.mp3')
      } else {
        this.answerStatus = 'answer'
        this.isStop = false
        this.timing()
        this.play('/static/game_assets/audio/game34/audio1.mp3')
      }
    },

    start() {
      this.level = Number(this.info.level) || 1
      this.status = 3
      // this.timing()
    },

    select(type, item) {
      this.selectTime.hour = item
      this.selectTime.period = type === 1 ? 'am' : 'pm'
    },

    startProcess() {
      this.answerStatus = 'play'
      this.bellNum = 0
      this.timeNum = api.randomNum((this.level - 1) * 4 + 1, this.level * 4)
      const numArr = [1, 2, 3, 4, 5]
      const randomNumber = api.randomNum(0, 5)
      this.randomArr = api.getRandomArray(numArr, randomNumber)
      this.play('/static/game_assets/audio/bell.mp3')
    },

    submit() {
      if (!this.selectTime.hour) return
      this.isStop = true
      this.answerStatus = 'judge'
      this.isCorrect = (this.selectTime.hour === this.timeNum) && ((this.selectTime.period === 'pm' && this.randomArr.length < 3) || (this.selectTime.period === 'am' && this.randomArr.length > 2))
      if (this.isCorrect) {
        this.succesNum++
      } else {
        this.errorNum++
      }

      setTimeout(() => {
        this.number++
        if (this.number >= 3) {
          this.answerStatus = 'wait'
          this.store = this.succesNum ? (this.succesNum * 30 + 10) : 0
          this.infos[0].value = this.level
          this.infos[1].value = this.second
          this.infos[2].value = this.succesNum
          this.infos[3].value = this.errorNum
          this.status = 4
          this.params = {
            id: this.info.id,
            grade: this.level,
            time: this.second,
            totalPoints: this.store
          }
        } else {
          this.answerStatus = 'result'
        }
      }, 1000)
    },

    stop() {
      this.isStop = true
      this.show = true
      if (this.isPlay) {
        this.pause()
        this.isContinue = true
      }
    },

    // 继续游戏, 继续计时
    cancel() {
      if (this.answerStatus === 'answer') {
        this.isStop = false
        this.timing()
      }
      if (this.isContinue) {
        this.play()
        this.isContinue = false
      }
    },

    reset() {
      this.number = 0
      this.second = 0
      this.store = 0
      this.succesNum = 0
      this.errorNum = 0
      this.timeNum = 0
      this.answerStatus = 'wait'
      this.randomArr = []
      this.selectTime = {
        hour: 0,
        period: ''
      }
    },

    continueTrain() {
      this.answerStatus = 'wait'
      this.randomArr = []
      this.timeNum = 0
      this.selectTime = {
        hour: 0,
        period: ''
      }
    },

    again() {
      this.isStop = false
      this.status = 3
      this.reset()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game34-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background-image: url("/static/game_assets/game34/bg_1.png");
  }
  .page-bg2 {
    background-image: url("/static/game_assets/game34/bg_2.png");
  }

  .game-synopsis {
    width: 1000px;
    height: 504px; 
    padding-left: 200px;  
    padding-top: 50px;   

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #333;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 25px;

      p {
        margin: 0;
        font-size: 25px;
        line-height: 40px;
        font-weight: 400;
        color: #333;
        user-select: none;
      }
    }
  }

  .game-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative;
    width: 100%;
    height: 100%;
    padding: 30px 0 15px 0;

    .clock-content {
      display: flex;
      width: 1640px;

      .clock {
        position: relative;
        width: 328px;
        height: 416px;

        .clock-bg {
          position: absolute;
          top: 0;
          left: 11px;
          width: 292px;
          height: 400px;
        }

        .clock-shadow {
          position: absolute;
          left: 0;
          bottom: 0;
          width: 328px;
          height: 47px;
          opacity: 0.2;
        }

        .clock-point {
          position: absolute;
          top: 218px;
          left: 142px;
          width: 41px;
          height: 39px;
          z-index: 1;
        }

        .hour-hand {
          position: absolute;
          top: 232px;
          left: 162px;
          width: 79px;
          height: 48px;
          animation: rotate 120s infinite linear;
          transform-origin: 0 5px;
        }

        .minute-hand {
          position: absolute;
          top: 155px;
          left: 127px;
          width: 46px;
          height: 92px;
          animation: rotate 20s infinite linear;
          transform-origin: 34px 83px;
        }
      }
    }

    .keyboard {
      width: 1548px;
      padding: 15px 0 10px 0;

      .morning, .afternoon {
        display: flex;

        .morning-img {
          width: 255px;
          height: 164px;
          margin-right: 18px;
        }

        .afternoon-img {
          width: 229px;
          height: 164px;
          margin-left: 26px;
          margin-right: 18px;
          margin-top: 10px;
        }

        .right {
          display: flex;
          flex-wrap: wrap;

          .btn {
            position: relative;
            width: 199px;
            height: 80px;
            margin: 2px 0 2px 8px;
            cursor: pointer;

            .btn-bg {
              width: 199px;
              height: 80px;
            }

            .btn-number {
              position: absolute;
              top: 17px;
              left: 69px;
              width: 59px;
              height: 40px;
            }
          }
        }
      }
    }

    .btn-group {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding: 0 123px 30px 123px;

      .left {
        width: 270px;
        height: 115px;
        cursor: pointer;
      }

      .right {
        .start, .continue, .reset {
          width: 268px; 
          height: 115px;
          margin-left: 40px;
          cursor: pointer;
        }

        .submit {
          width: 270px; 
          height: 115px;
          cursor: pointer;
        }
      }
    }
  }

  .mask-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;

    .img {
      width: 287px;
      height: 310px;
      margin-top: 320px;
    }
  }
}
@keyframes rotate {
  100% {
    transform: rotateZ(360deg);
  }
}
</style>
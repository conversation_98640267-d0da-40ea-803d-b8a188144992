{"data": [{"question": "从H地到B地应该", "answer": 2, "answerList": [{"value": "向北直行看到<家乐福超市>后向东，一直直行", "index": 1}, {"value": "向北直行看到<家乐福超市>后向西，直行看到<红绿灯>后向北，然后直行", "index": 2}, {"value": "向北直行看到<家乐福超市>后向西，直行看到<红绿灯>后向南，然后直行", "index": 3}, {"value": "向北直行看到<家乐福超市>后向西，一直直行", "index": 4}]}, {"question": "从C地到F地应该", "answer": 1, "answerList": [{"value": "行至<红绿灯>处向东，然后直行", "index": 1}, {"value": "行至<红绿灯>处向南，然后直行", "index": 2}, {"value": "行至<红绿灯>处向西，然后直行", "index": 3}, {"value": "行至<红绿灯>处向北，然后直行", "index": 4}]}, {"question": "从E地到A地应该", "answer": 2, "answerList": [{"value": "直行看到<肯德基快餐店>后向南，然后直行看到<家乐福超市>后向东，一直直行", "index": 1}, {"value": "直行看到<肯德基快餐店>后向南，然后直行看到<家乐福超市>后向西，一直直行", "index": 2}, {"value": "直行看到<肯德基快餐店>后向南，然后直行看到<同仁堂药店>后向东，一直直行", "index": 3}, {"value": "直行看到<肯德基快餐店>后向南，然后直行看到<中国石化加油站>后向东，一直直行", "index": 4}]}, {"question": "从A地到E地应该", "answer": 4, "answerList": [{"value": "行至第二个十字路口向北，直行看到<同仁堂药店>后向东", "index": 1}, {"value": "行至第二个十字路口向南，直行看到<中国石化加油站>后向东", "index": 2}, {"value": "行至第二个十字路口向南，然后一直直行", "index": 3}, {"value": "行至第二个十字路口向北，直行看到<肯德基快餐店>后向东", "index": 4}]}, {"question": "从F地到J地应该", "answer": 3, "answerList": [{"value": "向西直行看到<家乐福超市>后向北，直行看到<肯德基快餐店>后向东，一直直行", "index": 1}, {"value": "向西直行看到<家乐福超市>后向北，直行看到<同仁堂药店>后向东，然后直行看到<中国建设银行>后向北", "index": 2}, {"value": "向西直行看到<家乐福超市>后向北，直行看到<同仁堂药店>后向东，然后直行看到<中国建设银行>后向南", "index": 3}, {"value": "向西直行看到<家乐福超市>后向北，然后一直直行", "index": 4}]}, {"question": "从D地到I地应该", "answer": 3, "answerList": [{"value": "直行看到<家乐福超市>后向东，然后直行", "index": 1}, {"value": "直行看到<同仁堂药店>后向东，然后直行看到<中国建设银行>后向南", "index": 2}, {"value": "直行看到<同仁堂药店>后向东，然后直行看到<中国建设银行>后向北", "index": 3}, {"value": "直行看到<中国石化加油站>后向东，然后直行", "index": 4}]}, {"question": "从J地到G地应该", "answer": 3, "answerList": [{"value": "向北直行看到<中国建设银行>后向西，直行看到<同仁堂药店>后向北，然后直行看到<肯德基快餐店>后向东，一直直行", "index": 1}, {"value": "向北直行看到<中国建设银行>后向西，直行看到<同仁堂药店>后向南，一直直行", "index": 2}, {"value": "向北直行看到<中国建设银行>后向西，直行看到<同仁堂药店>后向南，然后直行看到<中国石化加油站店>后向东，一直直行", "index": 3}, {"value": "向北直行看到<中国建设银行>后向西，直行看到<同仁堂药店>后向北，一直直行", "index": 4}]}, {"question": "从A地到B地应该", "answer": 1, "answerList": [{"value": "直行看到<红绿灯>后向北，然后继续直行", "index": 1}, {"value": "直行看到<红绿灯>后向西，然后继续直行", "index": 2}, {"value": "直行看到<红绿灯>后向东，然后继续直行", "index": 3}, {"value": "直行看到<红绿灯>后向南，然后继续直行", "index": 4}]}, {"question": "从C地到J地应该", "answer": 4, "answerList": [{"value": "行至第一个十字路口向东，直行看到<家乐福超市>后向北，然后直行看到<同仁堂药店>后向东，直行看到<中国建设银行>后向北，然后直行", "index": 1}, {"value": "行至第一个十字路口向东，直行看到<家乐福超市>后向南，然后直行看到<中国石化加油站>后向东，然后直行", "index": 2}, {"value": "行至第一个十字路口向东，直行看到<家乐福超市>后向北，然后直行看到<肯德基快餐店>后向东，然后直行", "index": 3}, {"value": "行至第一个十字路口向东，直行看到<家乐福超市>后向北，然后直行看到<同仁堂药店>后向东，直行看到<中国建设银行>后向南，然后直行", "index": 4}]}, {"question": "从F地到I地应该", "answer": 1, "answerList": [{"value": "向西直行看到<家乐福超市>后向北，直行看到<同仁堂药店>后向东，然后直行看到<中国建设银行>后向北", "index": 1}, {"value": "向西直行看到<家乐福超市>后向北，然后直行", "index": 2}, {"value": "向西直行看到<家乐福超市>后向北，直行看到<肯德基快餐店>后向东，然后直行", "index": 3}, {"value": "向西直行看到<家乐福超市>后向北，直行看到<同仁堂药店>后向东，然后直行看到<中国建设银行>后向南", "index": 4}]}]}
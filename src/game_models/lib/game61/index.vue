<template>
  <div class="game61-page">
    <div class="page-bg"></div>
		<settingPage title="词段补笔" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <p class="synopsis-content">1、分类是在头脑中根据事物的共同点和差异点，把它们区分为不同种类的心智操作。分类必须有一定的标准，但标准是可以根据需要更改的。</p>
        <p class="synopsis-content">2、请按要求找出同类的词语(文字)。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="title">
        <img class="title-bg" src="/static/game_assets/game61/title_bg.png" />
        <span class="title-text">请将《{{question.topic}}》指出来</span>
      </div>

      <div class="content">
        <img class="content-bg" src="/static/game_assets/game61/content_bg.png" />
        <img class="left-icon" src="/static/game_assets/game61/left.png" />
        <img class="right-icon" src="/static/game_assets/game61/right.png" />

        <div class="content-item" v-for="item in answerList" :key="item + 'item'" @click="chooseItem(item)">
          <img class="bg" src="/static/game_assets/game50/text_bg.png" />
          <p class="text">{{item}}</p>
          <div :class="[choose.includes(item) && 'choose-item', isJudge && question.answer.includes(item) && !choose.includes(item) && 'correct-item']"></div>
          <img class="icon" v-if="isJudge && question.answer.includes(item) && choose.includes(item)" src="/static/game_assets/common/correct.png">
          <img class="icon" v-if="isJudge && !question.answer.includes(item) && choose.includes(item)" src="/static/game_assets/common/error.png">
        </div>
      </div>

      <div class="footer">
        <img class="img" src="/static/game_assets/common/stop.png" @click="stop">
        <div class="group-btn">
          <img class="img" v-if="choose.length && !isJudge" src="/static/game_assets/common/confirm.png" @click="confirm">

          <template v-if="isJudge">
            <img class="img" src="/static/game_assets/common/continue.png" @click="goOn">
            <!-- <img class="img right-btn" src="/static/game_assets/common/reset.png" @click="reset"> -->
          </template>
        </div>        
      </div>
    </div>
    <bgMusic :status="status"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'

export default {
  name: 'game61',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isJudge: false,
      current: {},
      question: {
        topic: '',
        answer: []
      },
      answerList: [],
      choose: [],

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.status = 3
      this.current = api.getRandomArray(data.data, 1)[0]
      this.current.questionList = api.shuffle(this.current.questionList)
      this.answerList = api.shuffle(this.current.answerList)
      this.startProcess()
      // this.timing()
    },

    startProcess() {
      this.question = this.current.questionList[this.number]
      this.number++
    },

    chooseItem(item) {
      if (this.isJudge) return
      if (this.choose.includes(item)) {
        this.choose = this.choose.filter(it => it !== item)
        return
      }
      this.choose.push(item)
      this.choose = Array.from(new Set(this.choose))
    },
    
    goOn() {
      if (this.number >= 10) {
        this.submit()
      } else {
        this.isJudge = false
        this.choose = []
        this.startProcess()
      }
    },

    reset() {
      this.isJudge = false
      this.choose = []
      this.number = 0
      this.startProcess()
    },

    confirm() {
      const answer = this.question.answer
      this.choose.forEach(item => {
        if (answer.includes(item)) {
          this.succesNum++
        } else {
          this.errorNum++
        }
      })
      this.isJudge = true
    },

    stop() {
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
    },

    submit() {
      this.isStop = true
			this.store = 5 * this.succesNum
			this.infos[0].value = this.second
			this.infos[1].value = this.succesNum
			this.infos[2].value = this.errorNum
			this.status = 4
			this.params = {
				id: this.info.id,
				grade: '',
				time: this.second,
				totalPoints: this.store
			}
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
      this.isJudge = false
			this.current = {}
      this.choose = []
      this.question = {
        topic: '',
        answer: []
      }
			this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game61-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game126/bg.png");
  } 

  .game-synopsis {
    width: 1576px;
    height: 1066px;
    margin-top: 14px;
    padding: 300px 354px 0 440px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game22/info_bg.png");

    .synopsis-title {
      margin: 0;
      padding-bottom: 30px;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #1E1E1D;
      user-select: none;
    }

    .synopsis-content {
      margin: 0;
      font-size: 32px;
      line-height: 45px;
      font-weight: 400;
      color: #1E1E1D;
      user-select: none;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .title {
      position: absolute;
      top: 0;
      width: 932px;
      height: 125px;
      z-index: 1;

      .title-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 932px;
        height: 125px;
      }

      .title-text {
        position: relative;
        display: block;
        padding: 31px 0 58px 0;
        font-size: 36px;
        line-height: 36px;
        text-align: center;
        color: #1B1D2D;
        user-select: none;
      }
    }

    .content {
      position: relative;
      width: 1920px;
      height: 1024px;
      margin-top: 55px;
      padding: 140px 214px 300px 214px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between; 
      align-content: space-between;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1920px;
        height: 1024px;
      }

      .left-icon {
        position: absolute;
        left: 22px;
        bottom: 36px;
        width: 214px;
      }

      .right-icon {
        position: absolute;
        right: 66px;
        bottom: 26px;
        width: 204px;
      }

      .content-item {
        position: relative;
        width: 309px;
        height: 156px;
        margin: -12px -13px;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 309px;
          height: 156px;
        }

        .text {
          position: relative;
          margin: 0;
          margin: 19px 22px;
          padding: 36px 0;
          font-size: 47px;
          line-height: 47px;
          text-align: center;
          color: #2D3632;
          user-select: none;
        }

        .choose-item {
          position: absolute;
          top: 14px;
          left: 18px;
          width: 268px;
          height: 118px;
          border: 3px solid #007CFB;
          border-radius: 32px;
        }

        .correct-item {
          position: absolute;
          top: 14px;
          left: 18px;
          width: 268px;
          height: 118px;
          border: 3px solid #F1CC6E;
          border-radius: 32px;
        }

        .icon {
          position: absolute;
          right: 36px;
          bottom: 32px;
          width: 57px;
        }
      }
		}

    .footer {
      position: absolute;
      bottom: 75px;
      display: flex;
      justify-content: space-between;
      width: 1470px;
      padding-right: 38px;

      .img {
        height: 115px;
        cursor: pointer;
      }

      .right-btn {
        margin-left: 43px;
      }
    }
  }
}
</style>
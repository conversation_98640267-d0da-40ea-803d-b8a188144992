<template>
  <div class="game36-page">
    <div class="page-bg"></div>
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <settingPage @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、由于记忆障碍，用户的日常生活常常受到影响。电话留言训练正对日常生活所遇到的常见问题而设计。</p>
        <p class="synopsis-content">2、点击“开始训练”，进入训练页面。 </p>
        <p class="synopsis-content">3、点击“播放”键，播放电话留言。听理解障碍，但阅读理解尚可的用户可阅读显示屏上的电话留言内容。</p>
        <p class="synopsis-content">4、点击“答题”键，进入回答问题页面。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="content1" v-if="answerStatus === 'topic'">
        <img class="phone-img" src="/static/game_assets/game36/img.png" />
        <div class="content-text">
          <img class="text-bg" src="/static/game_assets/game36/text_bg.png" />
          <span class="text">{{currect.info}}</span>
        </div>
      </div>

      <div class="content2" v-if="answerStatus === 'answer'">
        <p class="content-title">{{currect.question[number].text}}</p>

        <div class="content-question">
          <img class="question-bg" src="/static/game_assets/game8/content_bg.png" />

          <div class="question-item" v-for="item in currect.question[number].answerList" :key="item.index + 'text1'">
            <div class="item-icon" @click="chooseItem(item.index)">
              <img v-if="answer === item.index" class="icon" src="/static/game_assets/game36/icon_1.png" />
            </div>

            <span class="item-text">{{item.text}}</span>
          </div>
        </div>
      </div>

      <div class="content3" v-if="answerStatus === 'reveal'">
        <img class="content-bg" src="/static/game_assets/game8/content_bg.png" />

        <div class="content-item" v-for="(item, index) in currect.question" :key="index + 'text2'">
          <span class="item-text">{{item.text}}</span>

          <div class="item-icon">
            <img v-if="correctList.includes(index)" class="icon" src="/static/game_assets/game36/icon_1.png" />
            <img v-else class="icon" src="/static/game_assets/game36/icon_2.png" />
          </div>          
        </div>
      </div>
      
      <div :class="['btn-group']">
        <div class="btn" @click="stop">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>

        <template v-if="answerStatus === 'topic'">
          <div  class="btn" @click="playQuestion">
            <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
            <span class="text">播放</span>
          </div>
          <div class="btn" @click="changgeQuestion">
            <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
            <span class="text">换题</span>
          </div>
          <div class="btn" @click="answerQuestion">
            <img class="bg" src="/static/game_assets/game8/brown_bg.png">
            <span class="text">答题</span>
          </div>
        </template>
        
        <template v-if="answerStatus === 'answer'">
          <div v-if="answer" class="btn" @click="goOn">
            <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
            <span class="text">继续</span>
          </div>
          <div class="btn" @click="toPrevious">
            <img class="bg" src="/static/game_assets/game8/brown_bg.png">
            <span class="text">前页</span>
          </div>
        </template>

        <template v-if="answerStatus === 'reveal'">
          <div class="btn" @click="goon">
            <img class="bg" src="/static/game_assets/game8/yellow_bg.png">
            <span class="text">继续</span>
          </div>
          <div class="btn" @click="reset">
            <img class="bg" src="/static/game_assets/game8/brown_bg.png">
            <span class="text">重做</span>
          </div>
        </template>
      </div>
    </div>
    <bgMusic :status="status" ref="bgMusic"></bgMusic>
    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import bgMusic from '../component/bgMusic.vue'
import api from '../../utils/common.js'
import data from './data/data.json'

export default {
  name: 'game36',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    [bgMusic.name]: bgMusic,
  },

  data() {
    return {
      musicUrl: '',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      show: false,
      status: 1,
      isStop: false,
      isPlay: false,
      isContinue: false,

      answerStatus: 'topic', // topic -- 题目 answer -- 回答 reveal -- 展示
      currect: {},
      answer: 0,
      correctList: [],

      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    start() {
      this.status = 3
      // this.timing()
      this.startProcess()
    },

    startProcess() {
      this.currect = api.getRandomArray(data.data, 1)[0]
    },

    chooseItem(item) {
      this.answer = item
    },

    playQuestion() {
      this.play(this.currect.audio, true)
    },

    play(url, flag = false) {
      if (this.isPlay) return

      this.$refs.bgMusic.pause()
      if (url) this.musicUrl = url
      if (flag) this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
      this.isContinue = true
    },

    handleEnd() {
      this.isPlay = false
      this.isContinue = false
      this.$refs.bgMusic.play()
    },

    changgeQuestion() {
      this.pause()
      this.startProcess()
    },

    answerQuestion() {
      this.pause()
      this.$refs.bgMusic.play()
      this.answerStatus = 'answer'
      this.currect.question[this.number].answerList = api.shuffle(this.currect.question[this.number].answerList)
    },

    toPrevious() {
      if (this.number < 1) return
      this.answer = 0
      this.number--
      if (this.correctList.includes(this.number)) {
        this.succesNum--
      } else {
        this.errorNum--
      }
    },

    goOn() {
      if (this.answer === this.currect.question[this.number].answer) {
        this.succesNum++
        this.correctList.push(this.number)
      } else {
        this.errorNum++
      }

      this.number++
      if (this.number >= 3) {
        this.answerStatus = 'reveal'
      } else {
        this.currect.question[this.number].answerList = api.shuffle(this.currect.question[this.number].answerList)
        this.answer = 0
      }
    },

    goon() {
      this.submit()
    },

    reset() {
      this.succesNum = 0
      this.errorNum = 0
      this.answer = 0
      this.correctList = []
      this.number = 0
      this.answerStatus = 'answer'
    },

    submit() {
      this.store = this.succesNum * 30 + 10
      this.isStop = true
      this.infos[0].value = this.second
      this.infos[1].value = this.succesNum
      this.infos[2].value = this.errorNum
      this.status = 4
      this.params = {
        id: this.info.id,
        grade: '',
        time: this.second,
        totalPoints: this.store
      }
    },

    stop() {
      if (this.isPlay) this.pause()
      this.isStop = true
      this.show = true
    },

    // 继续游戏, 继续计时
    cancel() {
      if (this.isContinue && this.answerStatus === 'topic') this.play()
      this.isStop = false
      this.timing()
    },

    again() {
      this.number = 0
      this.second = 0
      this.store = 0
      this.errorNum = 0
      this.succesNum = 0
      this.isStop = false
      this.isPlay = false
      this.isContinue = false
      this.answerStatus = 'topic'
      this.answer = 0
      this.correctList = []
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss">
.game36-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game36/bg.png");
  }

  .game-synopsis {

    .synopsis-content {
      padding-top: 20px;
      margin: 0;
      font-size: 30px;
      line-height: 42px;
      font-weight: 400;
      color: #A83A01;
    }
  }

  .game-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .content1 {
      position: relative;
      width: 1920px;
      height: 1080px;

      .phone-img {
        position: absolute;
        bottom: 0;
        right: 114px;
        width: 544px;
      }

      .content-text {
        position: absolute;
        right: 806px;
        bottom: 481px;
        width: 675px;
        height: 427px;
        padding: 70px 150px 0 80px;

        .text-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 676px;
        }

        .text {
          position: relative;
          display: block;
          font-size: 30px;
          line-height: 42px;
          color: #484848;
        }
      }
    }

    .content2 {
      width: 1253px;
      height: 808px;
      margin-bottom: 40px;

      .content-title {
        margin: 0;
        padding-bottom: 50px;
        font-size: 36px;
        line-height: 50px;
        font-weight: 500;
        color: #484848;
        text-align: center;
      }

      .content-question {
        position: relative;
        width: 1253px;
        height: 708px;
        padding-top: 165px;
        padding-left: 320px;
        display: flex;
        flex-direction: column;

        .question-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 1253px;
        }

        .question-item {
          display: inline-flex;
          align-items: center;
          padding-bottom: 35px;

          .item-icon {
            position: relative;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #484848;
            cursor: pointer;

            img {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%
            }
          }

          .item-text {
            position: relative;
            display: block;
            padding-left: 35px;
            font-size: 38px;
            line-height: 44px;
            font-weight: 500;
            color: #484848;
          }
        }
      }
    }

    .content3 {
      position: relative;
      width: 1253px;
      height: 708px;
      margin-bottom: 60px;
      padding: 165px 115px 0 98px;
      display: flex;
      flex-direction: column;
      align-content: space-between;

      .content-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1253px;
      }

      .content-item {
        width: 100%;
        display: inline-flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 35px;

        .item-icon {
          position: relative;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          border: 2px solid #484848;

          img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%
          }
        }

        .item-text {
          position: relative;
          display: block;
          padding-left: 35px;
          font-size: 38px;
          line-height: 44px;
          font-weight: 500;
          color: #484848;
        }
      }
    }

    .btn-group {
      position: absolute;
      bottom: 82px;
      width: 1554px;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px;
        height: 84px;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px;
          height: 84px;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px 0 21px 0;
          font-size: 38px;
          line-height: 53px;
          text-align: center;
          color: #A83A01;
        }
      }
    }

    .judge {
      position: absolute;
      width: 460px;
      height: 460px;

      .img {
        position: absolute;
        width: 460px;
      }
    }
  }
}
</style>
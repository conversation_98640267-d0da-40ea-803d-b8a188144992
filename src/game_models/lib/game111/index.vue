<template>
  <div class="game111-page">
    <audio ref="music" muted controls="controls" style="display:none" @ended="handleEnd">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <div class="page-bg"></div>
		<settingPage @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-content">1、本训练用于提高训练者对各种水果认知。</p>
        <p class="synopsis-content">2、一共2关，请根据提示进行操作。</p>
        <p class="synopsis-content">3、本训练难度为B（普通）。</p>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="content1" v-if="number === 0">
        <div class="content-top">
          <img class="top-item" v-for="item in 7" :key="item + 'img'" :src="`/static/game_assets/game111/item_bg_${item}.png`" />
        </div>

        <template v-for="item in 7">
          <img v-if="item < currect || currect === item" :key="item + 'img0'" :class="['content-bottom', itemClass(item)]" :src="`/static/game_assets/game111/item_${item}.png`" />
        </template>
      </div>

      <div class="content2" v-if="number === 1">
        <div class="content-top" ref="topRef">
          <img class="top-bg" src="/static/game_assets/game111/title_bg.png" />
          <span class="top-text">得分：{{store}}</span>
        </div>

        <div class="content-bottom" ref="bottom">
          <div :class="['bottom-item']" ref="bottomItem" v-for="(item, index) in questions" :key="index + 'item'" @click="chooseItem(item, index)">
            <img class="item-img" :ref="`itemImg${index}`" :src="`/static/game_assets/game111/item_${item}.png`" />
            <p class="item-store" :ref="`itemStore${index}`">+{{item === answer ? 10 : 0}}</p>
          </div>
        </div>
      </div>

      <div class="content3" v-if="number === 2">
        <div :class="['content-item', 'content-item-' + index, itemClass(item, index)]" v-for="(item, index) in questions1" :key="index + 'img2'" @click="setChoose(item, index)" >
          <img class="item-img" :src="`/static/game_assets/game111/item_${item}.png`" />
        </div>
      </div>

      <!-- <div class="footer">
        <div class="btn" @click="stop">
          <img class="bg" src="/static/game_assets/game8/red_bg.png">
          <span class="text">停止</span>
        </div>
      </div> -->
    </div>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
    <quitConfirm :show.sync="show" @cancel="cancel"></quitConfirm>
  </div>
</template>

<script>
import draggable from "vuedraggable"
import settingPage from '../component/settingPage2.vue'
import resultPage from '../component/resultPage2.vue'
import quitConfirm from '../component/quitCofirm.vue'
import api from '../../utils/common.js'

export default {
  name: 'game111',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage,
    [quitConfirm.name]: quitConfirm,
    draggable
  },

  data() {
    return {
      musicUrl: '',
      number: 0,
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      show: false,
      isPlay: false,
      isRotate: false,

      choose: [],
      list: [],
      questions: [],
      questions1: [],
      answer: 0,
      currect: 1,

      isStop: false,
      params: {},
      infos: [
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        }
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    itemClass(item, index) {
      if (this.number === 0) {
        if (this.currect > item) return 'rotate-item' + item
        if (this.isRotate) return 'rotate-item' + item
      }

      if (this.number === 2) {
        const list1 = this.choose.filter(it => it.name === item)
        const list2 = this.choose.filter(it => it.index === index)
        if (list2.length && list1.length === 1) {
          return 'scale-item-' + index
        }
        if (list2.length && list1.length === 2) {
          return 'translate-item-' + index
        }
        return ''
      }
    },

		play(url) {
      if (url) this.musicUrl = url
      this.$refs.music.load()
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },
  
    handleEnd() {
      this.isPlay = false
      if (this.number === 0) {
        this.isRotate = true
        setTimeout(() => {
          this.isRotate = false
          this.currect++
          if (this.currect > 7) {
            this.number = 1
            this.startProcess()
          } else {
            this.play(`/static/game_assets/audio/game111/audio_${this.currect}.mp3`)
          }
        }, 1800)
      }
    },

    start() {
      this.status = 3
      this.startProcess()
    },

    startProcess() {
      this.questions = []
      this.questions1 = []
      if (this.number === 0) {
        this.play(`/static/game_assets/audio/game111/audio_${this.currect}.mp3`)
      }

      const list = [1, 2, 3, 4, 5, 6, 7]
      if (this.number === 1) {
        // this.timing()
        this.list = api.getRandomArray(list, 4)
        this.answer = this.list[0]
        this.list.push(this.answer)
        this.list.push(this.answer)
        this.questions = api.shuffle(JSON.parse(JSON.stringify(this.list)))
        this.play(`/static/game_assets/audio/game111/title_${this.answer}.mp3`)

        this.$nextTick(() => {
          const bottomRefs = this.$refs.bottomItem
          bottomRefs.forEach((item, index) => {
            const flag = api.randomNum(0, 1)
            const top = index * 100
            const delay = api.randomNum(0, 8) * 0.5
            item.style.top = top + 'px'
            item.style['animation-delay'] = `${delay}s`
            if (flag) {
              item.style['left'] = '-158px'
              item.className = item.className + ' rotate-item-0'
            } else {
              item.style['right'] = '-158px'
              item.className = item.className + ' rotate-item-1'
            }

            item.addEventListener('animationend', () => {
              const w = parseInt(document.body.clientWidth)
              const num = api.getRandomArray(this.list, 1)[0]
              this.$set(this.questions, index, num)
              item.className = item.className.split(' ')[0]
              item.style['animation-delay'] = '0s'

              const fl = api.randomNum(0, 1)
              if (fl) {
                item.style['left'] = '-158px'
                item.style['right'] = w + 'px'
                setTimeout(() => {
                  item.className = item.className + ' rotate-item-0'
                }, 100)
              } else {
                item.style['right'] = '-158px'
                item.style['left'] = w + 'px'
                setTimeout(() => {
                  item.className = item.className + ' rotate-item-1'
                }, 100)
              }
            })
          })
        })
      }

      if (this.number === 2) {
        this.play('/static/game_assets/audio/game111/question.mp3')
        this.questions1 = api.getRandomArray(list, 5)
        this.questions1 = this.questions1.concat(this.questions1)
        this.questions1 = api.shuffle(this.questions1)
      }
    },

    chooseItem(item, index) {
      const ref = this.$refs.bottomItem[index]
      const x = parseInt(ref.getBoundingClientRect().left)
      const y1 = parseInt(ref.getBoundingClientRect().top)
      const y2 = parseInt(this.$refs.topRef.getBoundingClientRect().top)
      const w = parseInt(document.body.clientWidth)
      ref.style.left = x + 'px'
      ref.className = ref.className.split(' ')[0]
      const imgRef = this.$refs[`itemImg${index}`][0]
      imgRef.className = imgRef.className + ' translate-item'
      const storeRef = this.$refs[`itemStore${index}`][0]
      storeRef.style['transform'] = `translate3d(${w / 2 - x}px, -${y1 - y2}px, 0) scale(0)`
      storeRef.style['opacity'] = '1'
      if (this.answer !== this.questions[index]) {
        this.errorNum++
        this.play(`/static/game_assets/audio/error_audio.mp3`)
      }
      if (this.choose.includes(index)) return

      imgRef.addEventListener('animationend', () => {
        if (this.answer === this.questions[index]) {
          this.succesNum++
          this.store = this.succesNum * 10
        }
        imgRef.className = ref.className.split(' ')[0]
        const num = api.getRandomArray(this.list, 1)[0]
        this.$set(this.questions, index, num)
        storeRef.style['transform'] = 'none'
        storeRef.style['opacity'] = '0'
        ref.className = ref.className.split(' ')[0]
        ref.style['animation-delay'] = '0s'

        const flag = api.randomNum(0, 1)
        if (flag) {
          ref.style['left'] = '-158px'
          ref.style['right'] = w + 'px'
          ref.className = ref.className.split(' ')[0]
          setTimeout(() => {
            ref.className = ref.className + ' rotate-item-0'
          }, 100)
        } else {
          ref.style['right'] = '-158px'
          ref.style['left'] = w + 'px'
          ref.className = ref.className.split(' ')[0]
          setTimeout(() => {
            ref.className = ref.className + ' rotate-item-1'
          }, 100)
        }

        if (this.succesNum >= 10) {
          this.choose = []
          this.number = 2
          this.startProcess()
        }
      })
      this.choose.push(index)
    },

    setChoose(item, index) {
      const list = this.choose.filter(it => it.index === index)
      if (list.length) return
      if ((this.choose.length % 2) && (item !== this.choose[this.choose.length - 1].name)) {
        this.errorNum++
        this.play(`/static/game_assets/audio/error_audio.mp3`)
        return
      }
      this.succesNum++
      this.choose.push({
        name: item,
        index
      })

      if (this.choose.length >= 10) {
        setTimeout(() => {
          this.submit()
        }, 1500)
      }
    },

    stop() {
      this.isStop = true
      this.show = true
      if (this.isPlay) this.pause()
    },

    // 继续游戏, 继续计时
    cancel() {
      this.isStop = false
      this.timing()
      this.play()
    },

    submit() {
      this.isStop = true
      this.store = 100 - 3 * this.errorNum > 0 ? 100 - 3 * this.errorNum : 0
			this.infos[0].value = this.second
			this.infos[1].value = this.succesNum
			this.infos[2].value = this.errorNum
			this.status = 4
			this.params = {
				id: this.info.id,
				grade: '',
				time: this.second,
				totalPoints: this.store
			}
    },

    again() {
      this.number = 0
      this.second = 0
      this.errorNum = 0
      this.succesNum = 0
      this.store = 0
      this.isStop = false
			this.choose = 0
      this.dragItemTop = []
      this.dragItemBottom = [[], [], [], [], []]
			this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game111-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("/static/game_assets/game110/bg.png");
  } 

  .game-synopsis {
    .synopsis-content {
      padding-top: 20px;
      margin: 0;
      font-size: 30px;
      line-height: 42px;
      font-weight: 400;
      color: #A83A01;
    }
  }

  .game-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    .content1 {
      position: relative;
      width: 1346px;
      height: 720px;
      margin-bottom: 250px;
      margin-right: 80px;

      .content-top {
        width: 100%;
        padding-bottom: 246px;
        display: flex;
        justify-content: space-between;

        .top-item {
          width: 158px;
          height: 158px;
        }
      }

      .content-bottom {
        position: absolute;
        bottom: 0;
        left: 531px;
        width: 316px;
        height: 316px;
        transition: All 1.5s ease-in-out;
        z-index: 1
      }

      .rotate-item1 {
        transform: translate3d(-610px, -483px, 0) scale(0.5);
      }

      .rotate-item2 {
        transform: translate3d(-412px, -483px, 0) scale(0.5);
      }

      .rotate-item3 {
        transform: translate3d(-214px, -483px, 0) scale(0.5);
      }

      .rotate-item4 {
        transform: translate3d(-16px, -483px, 0) scale(0.5);
      }

      .rotate-item5 {
        transform: translate3d(182px, -483px, 0) scale(0.5);
      }

      .rotate-item6 {
        transform: translate3d(380px, -483px, 0) scale(0.5);
      }

      .rotate-item7 {
        transform: translate3d(578px, -483px, 0) scale(0.5);
      }
    }

    .content2 {
      position: relative;
      width: 100%;
      height: 864px;
      margin-bottom: 123px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .content-top {
        position: relative;
        width: 487px;
        height: 93px;

        .top-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 487px;
        }

        .top-text {
          position: relative;
          display: block;
          padding: 21px 0 32px 0;
          font-size: 40px;
          line-height: 40px;
          text-align: center;
          font-weight: 600;
          color: #A83A01;
        }
      }

      .content-bottom {
        position: relative;
        width: 100%;
        flex: 1;

        .bottom-item {
          position: absolute;
          width: 188px;
          height: 158px;
          cursor: pointer;

          &:hover {
            animation-play-state: paused;
          }

          .item-img {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 158px;
          }

          .translate-item {
            animation: translate1 1.5s linear infinite;
            animation-iteration-count: 1;
            animation-fill-mode: forwards;
          }

          .item-store {
            position: relative;
            margin: 0;
            font-size: 60px;
            line-height: 60px;
            text-align: center;
            font-weight: 600;
            color: #A83A01;
            transition: transform 1.5s ease-in-out;
            z-index: 2;
            opacity: 0;
          }
        }

        .rotate-item-0 {
          animation: rotate1 16s linear infinite;
          animation-iteration-count: 1;
        }

        .rotate-item-1 {
          animation: rotate2 16s linear infinite;
          animation-iteration-count: 1;
        }
      }
    }

    .content3 {
      position: relative;
      width: 1750px;
      height: 864px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;

      .content-item {
        width: 286px;
        height: 286px;
        margin: 10px;
        padding: 10px;
        background: #fff;
        border-radius: 24px;
        cursor: pointer;
        transition: all 1.5s ease-in-out;

        .item-img {
          width: 266px;
          height: 266px;
        }
      }

      .scale-item-0, .scale-item-1, .scale-item-2, .scale-item-3, .scale-item-4, .scale-item-5, .scale-item-6, .scale-item-7, .scale-item-8, .scale-item-9 {
        transform: scale(1.2);
      }

      .translate-item-0 {
        transform: translate3d(722px, 279px, 0);
        opacity: 0;
      }

      .translate-item-1 {
        transform: translate3d(361px, 279px, 0);
        opacity: 0;
      }

      .translate-item-2 {
        transform: translate3d(0, 279px, 0);
        opacity: 0;
      }

      .translate-item-3 {
        transform: translate3d(-361px, 279px, 0);
        opacity: 0;
      }

      .translate-item-4 {
        transform: translate3d(-722px, 279px, 0);
        opacity: 0;
      }

      .translate-item-5 {
        transform: translate3d(722px, -279px, 0);
        opacity: 0;
      }

      .translate-item-6 {
        transform: translate3d(361px, -279px, 0);
        opacity: 0;
      }

      .translate-item-7 {
        transform: translate3d(0, -279px, 0);
        opacity: 0;
      }

      .translate-item-8 {
        transform: translate3d(-361px, -279px, 0);
        opacity: 0;
      }

      .translate-item-9 {
        transform: translate3d(-722px, -279px, 0);
        opacity: 0;
      }
    }

    .footer {
      position: absolute;
      bottom: 82px;
      width: 1072px;
      display: flex;
      justify-content: space-between;

      .btn {
        position: relative;
        width: 295px;
        height: 84px;
        cursor: pointer;

        .bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 295px;
          height: 84px;
        }

        .text {
          position: relative;
          display: block;
          padding: 10px 0 21px 0;
          font-size: 38px;
          line-height: 53px;
          text-align: center;
          color: #A83A01;
        }
      }
    }
  }
}

@keyframes rotate1{
  0% {
    transform: translate3d(0, 0, 0);
  }
  50% {
    transform: translate3d(calc(100vw + 316px), 0, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes rotate2{
  0% {
    transform: translate3d(0, 0, 0);
  }
  50% {
    transform: translate3d(calc(-100vw - 316px), 0, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes translate1{
  0% {
    transform: translate3d(0, 0, 0);
  }
  100% {
    transform: translate3d(0, 1200px, 0);
  }
}

@keyframes translate2{
  0% {
    transform: translate3d(0, 0, 0);
  }
  100% {
    transform: translate3d(0, 1200px, 0);
  }
}
</style>
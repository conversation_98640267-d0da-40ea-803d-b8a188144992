<template>
  <div class="game31-page">
    <audio v-if="musicUrl" ref="music" muted controls="controls" loop="loop" style="display:none">
      <source :src="musicUrl" type="audio/mpeg" />
    </audio>
    <div class="page-bg" :class="status > 1 ? 'page-bg2': 'page-bg1'"></div>
    <settingPage title="找数训练--困难" @start="status = 2" @challenge="start" v-if="status < 3">
      <div class="game-synopsis">
        <p class="synopsis-title">挑战游戏说明</p>
        <div class="synopsis-content">
          <p>1、5*5数字方格，请按照数字序列顺序选择数字。</p>
          <p>2、选择错误，会提示您“错了”，请重新正确选择数字。</p>
          <p>3、训练用时和错误次数将作为成绩记录下来。</p>
          <p>4、本训练难度为C（困难）。</p>
        </div>
      </div>
    </settingPage>
    <div class="game-content" v-if="status === 3">
      <div class="game-bottom">
        <img class="bg" src="/static/game_assets/game29/bg.png" />
        <img class="bar-bg" src="/static/game_assets/game29/progress_bar_bg.png" />
        <div :class="['progress-bar', 'bar-' + (succesNum + 1)]"></div>

        <div class="btn-group">
          <div :class="['btn', errorNumber === item ? 'error-style' : '']" v-for="item in numList" :key="item" @click="select(item)">
            <img v-if="selectItem < item" class="btn-bg" src="/static/game_assets/game29/btn_middle_bg1.png" />
            <img v-else class="btn-bg" src="/static/game_assets/game29/btn_middle_bg2.png" />
            <span class="btn-num">{{ item }}</span>
          </div>
        </div>
      </div>

      <div class="game-top">
        <img v-if="!isPlay" class="icon" src="/static/game_assets/common/play.png" @click="play" />
        <img v-else class="icon" src="/static/game_assets/common/pause.png" @click="pause" />

        <div class="right">
          <div class="error">
            <img class="icon" src="/static/game_assets/game29/error.png" />
            <p class="text">错误次数：<span>{{ errorNum }}</span></p>
          </div>

          <div class="clock">
            <img class="icon" src="/static/game_assets/common/clock.png" />
            <p class="text">训练用时：<span>{{ second }}秒</span></p>
          </div>
        </div>
      </div>

      <div :class="['sucess', selectItem === numList.length ? 'translate-top' : '']">
        <img class="img" src="/static/game_assets/game29/hot_balloon.png">
      </div>
    </div>

    <resultPage v-if="status === 4" :store="store" :info="infos" :params="params" @again="again"></resultPage>
  </div>
</template>

<script>
import settingPage from '../component/settingPage.vue'
import resultPage from '../component/resultPage.vue'
import api from '../../utils/common.js'

export default {
  name: 'game31',

  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },

  components: {
    [settingPage.name]: settingPage,
    [resultPage.name]: resultPage
  },

  data() {
    return {
      musicUrl: '/static/game_assets/audio/bg_audio.mp3',
      second: 0,
      store: 0,
      succesNum: 0,
      errorNum: 0,
      status: 1,
      numList: [],
      selectItem: 0,
      errorNumber: 0, // 错误的数字
      isStop: false,
      isPlay: false,
      params: {},
      infos: [
        {
          key: '难度等级',
          value: 'C'
        },
        {
          key: '训练时长',
          value: 0
        },
        {
          key: '正确数',
          value: 0
        },
        {
          key: '错误数',
          value: 0
        },
      ]
    }
  },

  mounted() {
    this.timing()
  },

  methods: {
    timing() {
      if (this.isStop) return
      setTimeout(() => {
        this.second++
        this.timing()
      }, 1000)
    },

    play() {
      this.$refs.music.play()
      this.isPlay = true
    },

    pause() {
      this.$refs.music.pause()
      this.isPlay = false
    },

    select(item) {
      if (item !== (this.selectItem + 1)) {
        this.errorNumber = item
        this.errorNum++
        return
      }
      this.selectItem = item
      this.succesNum++
      if (item === this.numList.length) {
        this.pause()
        this.isStop = true
        this.store = parseInt(100 / (this.succesNum + this.errorNum) * this.succesNum)
        this.infos[1].value = this.second
        this.infos[2].value = this.succesNum
        this.infos[3].value = this.errorNum
        this.params = {
          id: this.info.id,
          grade: 3,
          time: this.second,
          totalPoints: this.store
        }
        setTimeout(() => {
          this.status = 4
        }, 2500)
      }
    },

    start() {
      const list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]
      this.numList = api.shuffle(list)
      this.status = 3
      // this.timing()
      this.play()
    },

    again() {
      this.selectItem = 0
      this.errorNumber = 0
      this.succesNum = 0
      this.errorNum = 0
      this.second = 0
      this.store = 0
      this.numList = []
      this.isStop = false
      this.start()
      this.timing()
    }
  }
}
</script>

<style lang="scss" scoped>
.game31-page {
  width: 100%;
  height: 100%;

  .page-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .page-bg1 {
    background: url("/static/game_assets/game34/bg_1.png") center center no-repeat;
    background-size: cover;
  }
  .page-bg2 {
    background-image: url("/static/game_assets/game34/bg_2.png");
  }

  .game-synopsis {
    width: 1000px;
    height: 504px; 
    padding-left: 200px;  
    padding-top: 50px;
    overflow: hidden;   

    .synopsis-title {
      margin: 0;
      font-size: 36px;
      line-height: 50px;
      font-weight: 600;
      color: #333;
      user-select: none;
    }

    .synopsis-content {
      padding-top: 25px;

      p {
        margin: 0;
        font-size: 25px;
        line-height: 40px;
        font-weight: 400;
        color: #333;
        user-select: none;
      }
    }
  }

  .game-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 40px;

    .game-top {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      display: flex;
      justify-content: space-between;
      padding: 35px 118px 0 105px;

      .icon {
        margin-top: 5px;
        width: 80px;
        height: 80px;
        cursor: pointer;
      }

      .right {
        display: inline-flex;
        justify-content: space-between;

        .error, .clock {
          margin-left: 30px;
          padding: 5px 20px;
          border: 1px solid #37B982;
          border-radius: 38px;
          font-size: 0;

          .icon {
            margin: 0;
            width: 65px;
            height: 65px;
            vertical-align: middle;
            cursor: auto;
          }

          .text {
            display: inline-block;
            margin: 0;
            padding: 0 13px;
            font-size: 32px;
            line-height: 65px;
            color: #51547E;
            vertical-align: middle;
            user-select: none;
          }
        }

        .error {
          .text {
            span {
              padding-left: 5px;
              color: #EF4835;
              font-weight: 500;
            }
          }
        }

        .clock {
          .text {
            span {
              padding-left: 5px;
              color: #51547E;
              font-weight: 600;
            }
          }
        }
      }
    }

    .game-bottom {
      position: relative;
      width: 1763px;
      height: 841px;

      .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 1763px;
        height: 841px;
      }

      .bar-bg {
        position: absolute;
        top: 135px;
        left: 472px;
        width: 847px;
        height: 70px;
      }

      .progress-bar {
        position: absolute;
        top: 145px;
        left: 485px;
        height: 40px;
        background: #FF607A;
        border-radius: 8px;
      }

      .bar-1 {
        width: 32px;
      }

      .bar-2 {
        width: 63px;
      }

      .bar-3 {
        width: 95px;
      }

      .bar-4 {
        width: 126px;
      }

      .bar-5 {
        width: 158px;
      }

      .bar-6 {
        width: 189px;
      }

      .bar-7 {
        width: 221px;
      }

      .bar-8 {
        width: 252px;
      }

      .bar-9 {
        width: 284px;
      }

      .bar-10 {
        width: 315px;
      }

      .bar-11 {
        width: 347px;
      }

      .bar-12 {
        width: 378px;
      }

      .bar-13 {
        width: 410px;
      }

      .bar-14 {
        width: 441px;
      }

      .bar-15 {
        width: 473px;
      }

      .bar-16 {
        width: 504px;
      }

      .bar-17 {
        width: 536px;
      }

      .bar-18 {
        width: 567px;
      }

      .bar-19 {
        width: 599px;
      }

      .bar-20 {
        width: 630px;
      }

      .bar-21 {
        width: 662px;
      }

      .bar-22 {
        width: 693px;
      }

      .bar-23 {
        width: 725px;
      }

      .bar-24 {
        width: 756px;
      }

      .bar-25 {
        width: 788px;
      }

      .bar-26 {
        width: 819px;
      }

      .bar-26 {
        width: 819px;
      }

      .btn-group {
        position: absolute;
        top: 227px;
        left: 470px;
        display: flex;
        flex-wrap: wrap;
        width: 848px;
        height: 454px;
        justify-content: space-between;

        .btn {
          position: relative;
          width: 166px;
          height: 87px;
          cursor: pointer;

          .btn-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 166px;
            height: 87px;
          }

          .btn-num {
            position: relative;
            top: 17px;
            left: 0;
            display: block;
            width: 166px;
            height: 43px;
            font-size: 43px;
            line-height: 43px;
            text-align: center;
            font-weight: 500;
            color: #fff;
            font-family: PingFang SC;
            user-select: none;
          }
        }

        .error-style {
          animation: shake 800ms ease-in-out;
        }
      }
    }

    .sucess {
      position: absolute;
      bottom: -762px;
      width: 774px;
      height: 762px;
      transition: all 2s ease-in;

      .img {
        width: 774px;
        height: 762px;
      }
    }

    .translate-top {
      transform: translateY(-2442px);
    }
  }

  .mask-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;

    .img {
      width: 287px;
      height: 310px;
      margin-top: 320px;
    }
  }
}
@keyframes shake { /* 水平抖动，核心代码 */
  10%, 90% { transform: translate3d(-1px, 0, 0); }
  20%, 80% { transform: translate3d(+2px, 0, 0); }
  30%, 70% { transform: translate3d(-4px, 0, 0); }
  40%, 60% { transform: translate3d(+4px, 0, 0); }
  50% { transform: translate3d(-4px, 0, 0); }
}
</style>